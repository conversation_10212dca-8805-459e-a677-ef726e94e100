"""remove campaign sending window

Revision ID: 9b7c2201ba63
Revises: 1344d5ce4085
Create Date: 2023-07-07 14:01:30.713792

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9b7c2201ba63'
down_revision = '1344d5ce4085'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.drop_column('campaigns', 'sending_window')


def downgrade() -> None:
    op.add_column('campaigns', sa.Column('sending_window', sa.PickleType, default=dict))
