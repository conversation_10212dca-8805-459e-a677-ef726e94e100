"""add tag forwarding

Revision ID: f4d78e651b85
Revises: ************
Create Date: 2024-06-05 12:54:21.343894

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'f4d78e651b85'
down_revision = '************'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table('email_tag_forwards',
                    sa.Column('id', sa.UUID(), nullable=False),
                    sa.Column('tag', sa.String(length=40), nullable=False),
                    sa.Column('email', sa.String(length=40), nullable=False),
                    sa.Column('organisation_id', sa.UUID(), nullable=False),
                    sa.ForeignKeyConstraint(['organisation_id'], ['organisation.id'], ondelete='CASCADE'),
                    sa.PrimaryKeyConstraint('id')
                    )


def downgrade() -> None:
    op.drop_table('email_tag_forwards')
