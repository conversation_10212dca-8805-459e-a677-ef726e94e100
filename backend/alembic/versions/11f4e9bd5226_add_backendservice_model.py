"""add BackendService model

Revision ID: 11f4e9bd5226
Revises: 9b7c2201ba63
Create Date: 2023-07-07 15:22:46.371835

"""
from secrets import token_urlsafe
from uuid import uuid4

from alembic import op
import sqlalchemy as sa
from sqlalchemy import UUID

# revision identifiers, used by Alembic.
revision = '11f4e9bd5226'
down_revision = '9b7c2201ba63'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table('backend_services',
                    sa.Column('id', UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4),
                    sa.Column('name', sa.String(40), nullable=False),
                    sa.Column('api_key', sa.String(), nullable=False, default=lambda x: token_urlsafe(32))
                    )


def downgrade() -> None:
    op.drop_table('backend_services')
