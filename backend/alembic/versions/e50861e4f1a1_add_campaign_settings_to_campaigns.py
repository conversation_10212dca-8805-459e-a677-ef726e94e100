"""add campaign settings to campaigns

Revision ID: e50861e4f1a1
Revises: 11f4e9bd5226
Create Date: 2023-07-12 13:33:05.148157

"""
from sqlalchemy import (MetaData, Table, Column, String, DateTime, Integer,
                        Boolean, Foreign<PERSON>ey, Text, PickleType)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import select
from sqlalchemy.orm import sessionmaker, relationship, backref
from sqlalchemy.ext.declarative import declarative_base
from uuid import uuid4
from datetime import datetime
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e50861e4f1a1'
down_revision = '11f4e9bd5226'
branch_labels = None
depends_on = None


Model = declarative_base()
metadata = MetaData()


class Campaign(Model):
    __tablename__ = 'campaigns'
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    name = Column(String(100), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    user_id = Column(UUID(as_uuid=True), ForeignKey('user.id'), nullable=False)
    organisation_id = Column(UUID(as_uuid=True), ForeignKey('organisation.id'), nullable=False)
    audience_id = Column(UUID(as_uuid=True), ForeignKey('audience.id'), nullable=True)
    flow = Column(Text, nullable=True)
    paused = Column(Boolean, nullable=False, default=False)
    published = Column(Boolean, nullable=False, default=False)


class CampaignSettings(Model):
    __tablename__ = 'campaign_settings'
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    campaign_id = Column(UUID(as_uuid=True), ForeignKey('campaigns.id', ondelete='CASCADE'), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    budget = Column(Integer, nullable=False, default=0)
    budget_type = Column(String(10), nullable=False, default='daily')
    start_date = Column(DateTime, nullable=True)
    end_date = Column(DateTime, nullable=True)
    sending_window = Column(PickleType, default=dict)

    campaign = relationship('Campaign', back_populates='settings')


Campaign.settings = relationship('CampaignSettings', back_populates='campaign', uselist=False, cascade='all, delete-orphan')


def upgrade():
    # set CampaignSettings' start_date to nullable
    op.alter_column('campaign_settings', 'start_date', nullable=True)

    # create a new session
    session_maker = sessionmaker(bind=op.get_bind())
    session = session_maker()

    # get all campaigns
    campaigns = session.query(Campaign).all()

    # for each campaign, create a new CampaignSettings object with default values
    for campaign in campaigns:
        settings = CampaignSettings(
            id=str(uuid4()),
            campaign_id=campaign.id,
            created_at=datetime.utcnow(),
            budget=0,
            budget_type='daily',
            sending_window={}
        )
        session.add(settings)

    # commit the session
    session.commit()


def downgrade():
    # create a new session
    session_maker = sessionmaker(bind=op.get_bind())
    session = session_maker()

    # delete all CampaignSettings
    session.query(CampaignSettings).delete()

    # commit the session
    session.commit()