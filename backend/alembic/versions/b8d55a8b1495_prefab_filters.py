"""prefab filters

Revision ID: b8d55a8b1495
Revises: 1d9d9b964d9d
Create Date: 2023-10-23 12:02:11.143503

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID, JSO<PERSON>
from uuid import uuid4

# revision identifiers, used by Alembic.
revision = 'b8d55a8b1495'
down_revision = '1d9d9b964d9d'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table('prefab_filters',
                    sa.Column('id', UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4),
                    sa.Column('filter_class_name', sa.String(40), nullable=False),
                    sa.Column('visual_name', sa.String(40), nullable=False),
                    sa.Column('data', JSON, nullable=True),
                    sa.Column('data_point_id', UUID(as_uuid=True), sa.<PERSON><PERSON>('datapoints.id'), nullable=False),
                    )

    op.drop_column('audience', 'filters')
    op.add_column('audience', sa.Column('filters', sa.JSON, nullable=True))


def downgrade() -> None:
    op.drop_table('prefab_filters')
    op.drop_column('audience', 'filters')
    op.add_column('audience', sa.Column('filters', sa.PickleType, nullable=False))
