"""add settings

Revision ID: 1344d5ce4085
Revises: 
Create Date: 2023-07-07 11:14:26.862924

"""
from datetime import datetime
from uuid import uuid4

from alembic import op
import sqlalchemy as sa
from sqlalchemy import UUID, PickleType

# revision identifiers, used by Alembic.
revision = '1344d5ce4085'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table('campaign_settings',
                    sa.Column('id', UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4),
                    sa.Column('campaign_id', UUID(as_uuid=True), sa.ForeignKey('campaigns.id', ondelete='CASCADE'), nullable=False),
                    sa.Column('created_at', sa.DateTime, default=datetime.utcnow),
                    sa.Column('budget', sa.Integer, nullable=False, default=0),
                    sa.Column('budget_type', sa.String(10), nullable=False, default='daily'),
                    sa.Column('start_date', sa.DateTime, nullable=False),
                    sa.Column('end_date', sa.DateTime, nullable=True),
                    sa.Column('sending_window', PickleType, default=dict),
                    )

    op.create_table('user_notification_settings',
                    sa.Column('id', UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4),
                    sa.Column('user_id', UUID(as_uuid=True), sa.ForeignKey('user.id', ondelete='CASCADE'), primary_key=True),
                    sa.Column('created_at', sa.DateTime, default=datetime.utcnow),
                    sa.Column('updated_at', sa.DateTime, default=datetime.utcnow),
                    sa.Column('email', sa.Boolean, nullable=False, default=True),
                    sa.Column('in_app', sa.Boolean, nullable=False, default=True),
                    sa.Column('on_campaign_publish', sa.Boolean, nullable=False, default=True),
                    sa.Column('on_campaign_update', sa.Boolean, nullable=False, default=True),
                    sa.Column('on_campaign_status', sa.Boolean, nullable=False, default=True),
                    sa.Column('on_all_campaign_responses', sa.Boolean, nullable=False, default=True),
                    sa.Column('on_all_owned_campaign_responses', sa.Boolean, nullable=False, default=True),
                    )


def downgrade() -> None:
    op.drop_table('campaign_settings')
    op.drop_table('user_notification_settings')


