"""update audience

Revision ID: cda76b4c34bd
Revises: 3516a21294fc
Create Date: 2024-03-27 15:55:30.463502

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'cda76b4c34bd'
down_revision = '3516a21294fc'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('audience', sa.Column('personal_email_only', sa.<PERSON>(), default=False))
    op.add_column('audience', sa.Column('correct_function_only', sa.<PERSON>(), default=False))
    op.add_column('audience', sa.Column('status', sa.String(length=20)))

    op.execute("UPDATE audience SET status = 'new'")
    op.execute("UPDATE audience SET personal_email_only = false")
    op.execute("UPDATE audience SET correct_function_only = false")

    op.alter_column('audience', 'status', existing_type=sa.String(length=20), nullable=False)
    op.alter_column('audience', 'personal_email_only', existing_type=sa.Boolean(), nullable=False)
    op.alter_column('audience', 'correct_function_only', existing_type=sa.Boolean(), nullable=False)


def downgrade() -> None:
    op.drop_column('audience', 'status')
    op.drop_column('audience', 'correct_function_only')
    op.drop_column('audience', 'personal_email_only')
