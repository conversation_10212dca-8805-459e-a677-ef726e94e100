"""add campaign.archived

Revision ID: 05d32a2e2b20
Revises: 7325f3e074e5
Create Date: 2023-07-20 13:08:34.106711

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '05d32a2e2b20'
down_revision = '7325f3e074e5'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # server_default is required for columns which aren't nullable
    op.add_column('campaigns', sa.Column('archived', sa.Boolean(), nullable=False, default=False, server_default='False'))


def downgrade() -> None:
    op.drop_column('campaigns', 'archived')
