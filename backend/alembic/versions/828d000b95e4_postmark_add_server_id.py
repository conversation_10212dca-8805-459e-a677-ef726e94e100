"""postmark add server_id

Revision ID: 828d000b95e4
Revises: 7611c97723e6
Create Date: 2024-07-19 13:49:53.253485

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '828d000b95e4'
down_revision = '7611c97723e6'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('sending_domains', sa.Column('server_id', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('sending_domains', 'server_id')
    # ### end Alembic commands ###
