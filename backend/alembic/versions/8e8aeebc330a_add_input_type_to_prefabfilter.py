"""add input_type to prefabfilter

Revision ID: 8e8aeebc330a
Revises: a2d58f27a797
Create Date: 2023-10-27 10:54:11.550430

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8e8aeebc330a'
down_revision = 'a2d58f27a797'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column('prefab_filters', sa.Column('input_type', sa.String(40), nullable=True))


def downgrade() -> None:
    op.drop_column('prefab_filters', 'input_type')
