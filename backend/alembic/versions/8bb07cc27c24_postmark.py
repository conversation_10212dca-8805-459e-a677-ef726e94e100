"""postmark

Revision ID: 8bb07cc27c24
Revises: 7fe66b5aba8a
Create Date: 2024-09-16 14:31:23.162493

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import text
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '8bb07cc27c24'
down_revision = '7fe66b5aba8a'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(text("delete from sending_domains"))
    op.execute(text("delete from email_senders where is_postmark_sender = true"))

    op.add_column('email_senders', sa.Column('is_sparkpost_sender', sa.<PERSON>(), nullable=True))
    op.drop_column('email_senders', 'is_postmark_sender')
    op.add_column('sending_domains', sa.Column('domain', sa.String(length=100), nullable=False))
    op.add_column('sending_domains', sa.Column('dns_records', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.drop_column('sending_domains', 'subdomain')
    op.drop_column('sending_domains', 'server_token')
    op.drop_column('sending_domains', 'server_id')
    op.drop_column('sending_domains', 'full_domain')
    op.drop_column('sending_domains', 'return_path')
    op.drop_column('sending_domains', 'name')
    op.drop_column('sending_domains', 'cname_record')
    op.drop_column('sending_domains', 'mx_record')
    op.drop_column('sending_domains', 'signature_id')
    op.drop_column('sending_domains', 'dmarc_record')
    op.drop_column('sending_domains', 'dkim_host')
    op.drop_column('sending_domains', 'dkim_record')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('sending_domains', sa.Column('dkim_record', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('sending_domains', sa.Column('dkim_host', sa.VARCHAR(length=100), autoincrement=False, nullable=False))
    op.add_column('sending_domains', sa.Column('dmarc_record', sa.VARCHAR(length=100), autoincrement=False, nullable=False))
    op.add_column('sending_domains', sa.Column('signature_id', sa.VARCHAR(length=100), autoincrement=False, nullable=False))
    op.add_column('sending_domains', sa.Column('mx_record', sa.VARCHAR(length=100), autoincrement=False, nullable=False))
    op.add_column('sending_domains', sa.Column('cname_record', sa.VARCHAR(length=100), autoincrement=False, nullable=False))
    op.add_column('sending_domains', sa.Column('name', sa.VARCHAR(length=100), autoincrement=False, nullable=False))
    op.add_column('sending_domains', sa.Column('return_path', sa.VARCHAR(length=100), autoincrement=False, nullable=False))
    op.add_column('sending_domains', sa.Column('full_domain', sa.VARCHAR(length=100), autoincrement=False, nullable=False))
    op.add_column('sending_domains', sa.Column('server_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('sending_domains', sa.Column('server_token', sa.VARCHAR(length=100), autoincrement=False, nullable=True))
    op.add_column('sending_domains', sa.Column('subdomain', sa.VARCHAR(length=100), autoincrement=False, nullable=False))
    op.drop_column('sending_domains', 'dns_records')
    op.drop_column('sending_domains', 'domain')
    op.add_column('email_senders', sa.Column('is_postmark_sender', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.drop_column('email_senders', 'is_sparkpost_sender')
    # ### end Alembic commands ###
