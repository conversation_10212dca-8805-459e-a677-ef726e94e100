"""postmark

Revision ID: 7740860aef26
Revises: f4d78e651b85
Create Date: 2024-07-19 12:38:33.430537

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '7740860aef26'
down_revision = 'f4d78e651b85'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('sending_domains',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('organisation_id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('server_token', sa.String(length=100), nullable=False),
    sa.Column('return_path', sa.String(length=100), nullable=False),
    sa.Column('full_domain', sa.String(length=100), nullable=False),
    sa.Column('subdomain', sa.String(length=100), nullable=False),
    sa.Column('dkim_host', sa.String(length=100), nullable=False),
    sa.Column('dkim_record', sa.String(length=100), nullable=False),
    sa.Column('mx_record', sa.String(length=100), nullable=False),
    sa.Column('cname_record', sa.String(length=100), nullable=False),
    sa.Column('dmarc_record', sa.String(length=100), nullable=False),
    sa.Column('is_verified', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['organisation_id'], ['organisation.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.alter_column('campaigns', 'name',
               existing_type=sa.VARCHAR(length=100),
               nullable=False)
    op.alter_column('campaigns', 'published',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.add_column('email_senders', sa.Column('domain_id', sa.UUID(), nullable=True))
    op.alter_column('email_senders', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True)
    op.create_foreign_key(None, 'email_senders', 'sending_domains', ['domain_id'], ['id'], ondelete='CASCADE')
    op.alter_column('prefab_filters', 'visual_name',
               existing_type=sa.VARCHAR(length=40),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('prefab_filters', 'visual_name',
               existing_type=sa.VARCHAR(length=40),
               nullable=False)
    op.drop_constraint(None, 'email_senders', type_='foreignkey')
    op.alter_column('email_senders', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False)
    op.drop_column('email_senders', 'domain_id')
    op.alter_column('campaigns', 'published',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('campaigns', 'name',
               existing_type=sa.VARCHAR(length=100),
               nullable=True)
    op.drop_table('sending_domains')
    # ### end Alembic commands ###
