"""add audience.prospects_added

Revision ID: 40085da6bd07
Revises: cda76b4c34bd
Create Date: 2024-04-11 15:22:39.193295

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '40085da6bd07'
down_revision = 'cda76b4c34bd'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('audience', sa.Column('prospects_added', sa.Integer(), nullable=True))


def downgrade() -> None:
    op.drop_column('audience', 'prospects_added')
    # ### end Alembic commands ###
