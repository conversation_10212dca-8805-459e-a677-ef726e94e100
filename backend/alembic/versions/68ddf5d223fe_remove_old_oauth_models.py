"""remove old oauth models

Revision ID: 68ddf5d223fe
Revises: 05d32a2e2b20
Create Date: 2023-07-28 13:31:16.520632

"""
from uuid import uuid4

from alembic import op
import sqlalchemy as sa
from sqlalchemy import UUID

# revision identifiers, used by Alembic.
revision = '68ddf5d223fe'
down_revision = '05d32a2e2b20'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.drop_table('gmail_oauth_token')
    op.drop_table('office365_authentication')


def downgrade() -> None:
    op.create_table('gmail_oauth_token',
                    sa.Column('id', UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4),
                    sa.Column('authentication_id', UUID(as_uuid=True)),
                    sa.Column('access_token', sa.String(),  nullable=False),
                    sa.Column('refresh_token', sa.String(), nullable=False),
                    sa.Column('expiry', sa.DateTime(), nullable=False)
                    )
    op.create_table('office_365_authentication',
                    sa.Column('id', UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4),
                    sa.Column('authentication_id', UUID(as_uuid=True)),
                    sa.Column('access_token', sa.String(), nullable=False),
                    sa.Column('refresh_token', sa.String(), nullable=False),
                    sa.Column('expiry', sa.DateTime(), nullable=False)
                    )
