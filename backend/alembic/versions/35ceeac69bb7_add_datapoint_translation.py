"""add datapoint translation

Revision ID: 35ceeac69bb7
Revises: b2fa14534f6e
Create Date: 2024-01-29 15:09:26.636874

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '35ceeac69bb7'
down_revision = 'b2fa14534f6e'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('datapointtranslations',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('datapoint_id', sa.UUID(), nullable=False),
    sa.Column('value', sa.String(), nullable=False),
    sa.Column('translation', sa.String(), nullable=False),
    sa.ForeignKeyConstraint(['datapoint_id'], ['datapoints.id'], ),
    sa.PrimaryKeyConstraint('id')
    )


def downgrade() -> None:
    op.drop_table('datapointtranslations')
