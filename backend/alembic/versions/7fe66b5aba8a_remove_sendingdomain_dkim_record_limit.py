"""remove sendingdomain.dkim_record limit

Revision ID: 7fe66b5aba8a
Revises: e78c586ca856
Create Date: 2024-07-22 11:48:15.374023

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7fe66b5aba8a'
down_revision = 'e78c586ca856'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.drop_column('sending_domains', 'dkim_record')
    op.add_column('sending_domains', sa.Column('dkim_record', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    op.drop_column('sending_domains', 'dkim_record')
    op.add_column('sending_domains', sa.Column('dkim_record', sa.String(100), nullable=True))
