"""add packages

Revision ID: 7e2cdcb42db9
Revises: b8d55a8b1495
Create Date: 2023-10-23 23:37:59.655372

"""
from alembic import op
from uuid import uuid4
from sqlalchemy import text
from datetime import datetime ,timedelta
# revision identifiers, used by Alembic.
revision = '7e2cdcb42db9'
down_revision = 'b8d55a8b1495'
branch_labels = None
depends_on = None


def upgrade() -> None:
    organisations = op.get_bind().execute(text('SELECT id FROM organisation')).fetchall()
    for organisation in organisations:
        op.get_bind().execute(text(f"INSERT INTO packages (id, organisation_id, name, ends_at) VALUES "
                                   f"('{str(uuid4())}', '{organisation[0]}', 'pro', '{datetime.utcnow() + timedelta(weeks=12)}')"))


def downgrade() -> None:
    op.get_bind().execute(text('DELETE FROM packages'))
