"""wallet

Revision ID: 4662f4bf699d
Revises: 68ddf5d223fe
Create Date: 2023-10-13 10:58:39.197302

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import text
from sqlalchemy.dialects.postgresql import UUID
from uuid import uuid4
from uuid import uuid4
from datetime import datetime

# revision identifiers, used by Alembic.
revision = '4662f4bf699d'
down_revision = '68ddf5d223fe'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table('wallet',
                    sa.Column('id', UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4),
                    sa.Column('organisation_id', UUID(as_uuid=True), sa.<PERSON>ey('organisation.id', ondelete='CASCADE'), nullable=False),
                    sa.Column('balance', sa.Numeric(precision=10, scale=2), nullable=False, default=0.00),
                    sa.Column('created_at', sa.DateTime(), default=datetime.utcnow),
                    )

    op.create_table('transaction',
                    sa.Column('id', UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4),
                    sa.Column('wallet_id', UUID(as_uuid=True), sa.ForeignKey('wallet.id'), nullable=False),
                    sa.Column('amount', sa.Numeric(precision=10, scale=2), nullable=False, default=0.00),
                    sa.Column('type', sa.String(50), nullable=False),
                    sa.Column('created_at', sa.DateTime(), default=datetime.utcnow),
                    )

    # Create a wallet for every organisation

    # Get all organisations
    organisations = op.get_bind().execute(text('SELECT id FROM organisation')).fetchall()
    for organisation in organisations:
        op.get_bind().execute(text(f"INSERT INTO wallet (id, organisation_id, balance) VALUES "
                                   f"('{str(uuid4())}', '{organisation[0]}', '0.00')"))


def downgrade() -> None:
    op.drop_table('wallet')
    op.drop_table('transaction')
