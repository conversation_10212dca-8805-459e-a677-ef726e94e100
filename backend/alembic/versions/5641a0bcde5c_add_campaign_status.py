"""add campaign.status

Revision ID: 5641a0bcde5c
Revises: 40085da6bd07
Create Date: 2024-05-06 16:23:42.850540

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '5641a0bcde5c'
down_revision = '40085da6bd07'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column('campaigns', sa.Column('status', sa.String(length=20), nullable=True))


def downgrade() -> None:
    op.drop_column('campaigns', 'status')
