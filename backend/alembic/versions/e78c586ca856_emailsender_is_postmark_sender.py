"""emailsender.is_postmark_sender

Revision ID: e78c586ca856
Revises: 828d000b95e4
Create Date: 2024-07-19 14:15:53.842465

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e78c586ca856'
down_revision = '828d000b95e4'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('email_senders', sa.Column('is_postmark_sender', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('email_senders', 'is_postmark_sender')
    # ### end Alembic commands ###
