"""add category to prefab_filters

Revision ID: a2d58f27a797
Revises: 7e2cdcb42db9
Create Date: 2023-10-24 18:21:41.766406

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a2d58f27a797'
down_revision = '7e2cdcb42db9'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column('prefab_filters', sa.Column('category', sa.String(40), nullable=True))


def downgrade() -> None:
    op.drop_column('prefab_filters', 'category')
