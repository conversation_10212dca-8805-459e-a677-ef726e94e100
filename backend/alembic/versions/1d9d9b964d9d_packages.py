"""packages

Revision ID: 1d9d9b964d9d
Revises: 4662f4bf699d
Create Date: 2023-10-19 10:42:48.223010

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID
from uuid import uuid4
from datetime import datetime

# revision identifiers, used by Alembic.
revision = '1d9d9b964d9d'
down_revision = '4662f4bf699d'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        'packages',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, default=uuid4),
        sa.Column('name', sa.String, nullable=False),
        sa.Column('created_at', sa.DateTime, default=datetime.utcnow),
        sa.Column('starts_at', sa.DateTime, default=datetime.utcnow),
        sa.Column('ends_at', sa.DateTime, nullable=False),
        sa.Column('expired', sa.<PERSON>, default=False),
        sa.Column('organisation_id', UUID(as_uuid=True), sa.ForeignKey('organisation.id'), nullable=False)
    )

    op.create_table(
        'datapoints',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, default=uuid4),
        sa.Column('slug_name', sa.String(40), nullable=False),
        sa.Column('type', sa.String(40), nullable=False),
        sa.Column('source', sa.String(40), nullable=False),
        sa.Column('visual_name', sa.String(40), nullable=True),
        sa.Column('package', sa.String(40), nullable=True)
    )


def downgrade():
    op.drop_table('datapoints')
    op.drop_table('packages')
