"""datapoints.source nullable

Revision ID: b2fa14534f6e
Revises: 8e8aeebc330a
Create Date: 2024-01-12 12:22:07.258055

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b2fa14534f6e'
down_revision = '8e8aeebc330a'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.alter_column('datapoints', 'source',
                    existing_type=sa.VARCHAR(length=40),
                    nullable=True)


def downgrade() -> None:
    op.alter_column('datapoints', 'source',
               existing_type=sa.VARCHAR(length=40),
               nullable=False)