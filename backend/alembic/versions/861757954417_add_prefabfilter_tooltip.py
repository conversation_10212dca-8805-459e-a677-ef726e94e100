"""add prefabfilter.tooltip

Revision ID: 861757954417
Revises: 5641a0bcde5c
Create Date: 2024-05-23 11:49:28.675640

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '861757954417'
down_revision = '5641a0bcde5c'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column('prefab_filters', sa.Column('tooltip', sa.String(), nullable=True))


def downgrade() -> None:
    op.drop_column('prefab_filters', 'tooltip')
