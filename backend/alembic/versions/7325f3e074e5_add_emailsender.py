"""add emailsender

Revision ID: 7325f3e074e5
Revises: e50861e4f1a1
Create Date: 2023-07-20 13:06:31.371639

"""
from datetime import datetime

from alembic import op
import sqlalchemy as sa
from uuid import uuid4
from sqlalchemy import UUID


# revision identifiers, used by Alembic.
revision = '7325f3e074e5'
down_revision = 'e50861e4f1a1'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table('email_senders',
                    sa.Column('id', UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4),
                    sa.Column('user_id', UUID(as_uuid=True), sa.<PERSON>ey('user.id', ondelete='CASCADE'), nullable=False),
                    sa.Column('organisation_id', UUID(as_uuid=True), sa.<PERSON>Key('organisation.id', ondelete='CASCADE'), nullable=False),
                    sa.Column('created_at', sa.DateTime(), nullable=False, default=datetime.utcnow),
                    sa.Column('name', sa.String(100), nullable=False),
                    sa.Column('email', sa.String(100), nullable=False),
                    sa.Column('data', sa.String(), nullable=True)
                    )


def downgrade() -> None:
    op.drop_table('email_senders')
