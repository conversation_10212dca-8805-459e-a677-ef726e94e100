"""subscriptions

Revision ID: 3516a21294fc
Revises: 35ceeac69bb7
Create Date: 2024-03-07 14:52:02.111001

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '3516a21294fc'
down_revision = '35ceeac69bb7'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('credit_package',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('amount', sa.Integer(), nullable=False),
    sa.Column('price', sa.Integer(), nullable=False),
    sa.Column('organisation_id', sa.UUID(), nullable=False),
    sa.ForeignKeyConstraint(['organisation_id'], ['organisation.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('subscriptions',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('starts_at', sa.DateTime(), nullable=True),
    sa.Column('ends_at', sa.DateTime(), nullable=False),
    sa.Column('expired', sa.Boolean(), nullable=True),
    sa.Column('credits_per_month', sa.Integer(), nullable=False),
    sa.Column('price_per_month', sa.Integer(), nullable=False),
    sa.Column('senders_included', sa.Integer(), nullable=False),
    sa.Column('organisation_id', sa.UUID(), nullable=False),
    sa.ForeignKeyConstraint(['organisation_id'], ['organisation.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_table('packages')
    op.drop_table('token_blocklist')
    op.drop_table('sender')

    op.alter_column('transaction', 'amount',
               existing_type=sa.NUMERIC(precision=10, scale=2),
               type_=sa.Integer(),
               existing_nullable=False)
    op.drop_constraint('transaction_wallet_id_fkey', 'transaction', type_='foreignkey')

    op.create_foreign_key(None, 'transaction',
                          'wallet', ['wallet_id'], ['id'], ondelete='CASCADE')
    op.alter_column('wallet', 'balance',
               existing_type=sa.NUMERIC(precision=10, scale=2),
               type_=sa.Integer(),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('wallet', 'balance',
               existing_type=sa.Integer(),
               type_=sa.NUMERIC(precision=10, scale=2),
               existing_nullable=False)
    op.drop_constraint(None, 'transaction', type_='foreignkey')
    op.create_foreign_key('transaction_wallet_id_fkey', 'transaction', 'wallet', ['wallet_id'], ['id'])
    op.alter_column('transaction', 'amount',
               existing_type=sa.Integer(),
               type_=sa.NUMERIC(precision=10, scale=2),
               existing_nullable=False)
    op.alter_column('prefab_filters', 'visual_name',
               existing_type=sa.VARCHAR(length=40),
               nullable=False)
    op.alter_column('email_senders', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('campaigns', 'published',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('campaigns', 'name',
               existing_type=sa.VARCHAR(length=100),
               nullable=True)
    op.create_table('sender',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('organisation_id', sa.UUID(), autoincrement=False, nullable=True),
    sa.Column('first_name', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('last_name', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('email', sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    sa.Column('auth_type', sa.VARCHAR(length=50), autoincrement=False, nullable=True),
    sa.Column('authentication_id', sa.UUID(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='sender_pkey')
    )
    op.create_table('token_blocklist',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('jti', sa.VARCHAR(length=36), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='token_blocklist_pkey')
    )
    op.create_index('ix_token_blocklist_jti', 'token_blocklist', ['jti'], unique=False)
    op.create_table('packages',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('starts_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('ends_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('expired', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('organisation_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['organisation_id'], ['organisation.id'], name='packages_organisation_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='packages_pkey')
    )
    op.drop_table('subscriptions')
    op.drop_table('credit_package')
    # ### end Alembic commands ###
