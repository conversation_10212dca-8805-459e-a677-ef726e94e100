"""postmark 2

Revision ID: 7611c97723e6
Revises: 7740860aef26
Create Date: 2024-07-19 13:42:51.313835

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7611c97723e6'
down_revision = '7740860aef26'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('sending_domains', sa.Column('signature_id', sa.String(length=100), nullable=False))
    op.alter_column('sending_domains', 'server_token',
               existing_type=sa.VARCHAR(length=100),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('sending_domains', 'server_token',
               existing_type=sa.VARCHAR(length=100),
               nullable=False)
    op.drop_column('sending_domains', 'signature_id')
    # ### end Alembic commands ###
