import json
import logging
from uuid import UUID

from flask import request, jsonify

from src.build import create_app
from sqlalchemy.engine.url import URL
from datetime import timedelta
from os import environ

sql_url = URL(
    drivername="postgresql",
    username=environ['PG_USER'],
    password=environ['PG_PASSWORD'],
    host=environ['PG_HOST'],
    port=int(environ['PG_PORT']),
    database=environ['DB_NAME'],
    query={'sslmode': 'require'}
)
app = create_app({
    'JWT_SECRET_KEY': environ['SECRET_KEY'],
    'JWT_TOKEN_LOCATION': json.loads(environ['TOKEN_LOCATION']),
    "JWT_ACCESS_TOKEN_EXPIRES": timedelta(hours=1),
    'SQLALCHEMY_TRACK_MODIFICATIONS': False,
    'SQLALCHEMY_DATABASE_URI': sql_url,
    'JWT_COOKIE_CSRF_PROTECT': True,
    'JWT_COOKIE_DOMAIN': '.whatson.ai',
    'JWT_COOKIE_SECURE': True,
    'JWT_COOKIE_HTTPONLY': True,
    'JWT_CSRF_METHODS': ['POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    'JWT_CSRF_IN_COOKIES': True,
    'JWT_CSRF_CHECK_FORM': True,
    'JWT_REFRESH_CSRF_ENABLED': False

})
if environ.get('LOGLEVEL_DEBUG'):
    logging.basicConfig(level=logging.DEBUG)
    app.logger.setLevel(logging.DEBUG)


@app.before_request
def validate_uuid():
    ids_to_check = ['campaign_id', 'audience_id', 'sender_id', 'organisation_id', 'user_id']

    def get_invalid_ids(dict_to_check) -> (str, int):
        for id_to_check in ids_to_check:
            if isinstance(dict_to_check, dict) and dict_to_check.get(id_to_check):
                if not is_valid_uuid(dict_to_check[id_to_check]):
                    return f'Malformed {id_to_check}, please supply a valid UUID.', 400
        return '', 200

    if request.view_args:
        msg, code = get_invalid_ids(request.view_args)
        if code != 200:
            return jsonify({'error': msg}), code

    if request.is_json:
        msg, code = get_invalid_ids(request.json)
        if code != 200:
            return jsonify({'error': msg}), code

    if request.args:
        msg, code = get_invalid_ids(request.args)
        if code != 200:
            return jsonify({'error': msg}), code


def is_valid_uuid(value):
    try:
        UUID(value)
        return True
    except (ValueError, TypeError):
        return False
