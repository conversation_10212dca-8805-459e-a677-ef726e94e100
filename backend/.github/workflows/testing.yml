name: Python Pytest

on:
  push:
    branches:
      - main
      - dev
  pull_request:
    branches:
      - main
      - dev

jobs:
  build:

    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:latest
        env:
          POSTGRES_USER: ${{ vars.PG_USER }}
          POSTGRES_PASSWORD: ${{ secrets.PG_PASSWORD }}
          POSTGRES_DB: ${{ vars.DB_NAME }}
        ports:
          - 25060:5432
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5

    strategy:
      matrix:
        python-version: [3.10.13]

    steps:
    - uses: actions/checkout@v2

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v2
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
        python -m spacy download nl_core_news_sm

      env:
        GITHUB_TOKEN: ${{ secrets.HOLMES_REPO_KEY }}

    - name: Run tests with pytest
      run: |
        pytest tests
      env:
        PYTHONUNBUFFERED: 1
        MAILDB_URL: ${{ secrets.MAILDB_URL }}
        MORIARTY_API_KEY: ${{ secrets.MORIARTY_API_KEY }}
        PG_PASSWORD: ${{ secrets.PG_PASSWORD }}
        SPACES_ACCESS: ${{ secrets.SPACES_ACCESS }}
        SPACES_SECRET: ${{ secrets.SPACES_SECRET }}
        DATA_DB: ${{ vars.DATA_DB }}
        DB_NAME: ${{ vars.DB_NAME }}
        MORIARTY_URL: ${{ vars.MORIARTY_URL }}
        PG_HOST: localhost
        PG_USER: ${{ vars.PG_USER }}
        ENVIRONMENT: testing
        MARYM_URL: ${{ vars.MARYM_URL }}
        MARYM_API_KEY: ${{ secrets.MARYM_API_KEY }}
