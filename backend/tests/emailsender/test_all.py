import pytest
from flask import url_for
from src.models import EmailSender, db


@pytest.fixture
def email_sender(app, user, organisation):
    with app.app_context():
        sender = EmailSender(user_id=user.id, organisation_id=organisation.id, name='Test Email Sender',
                             email='<EMAIL>')
        db.session.add(sender)
        db.session.commit()
        yield sender

        db.session.delete(sender)
        db.session.commit()


def test_senders_endpoint(client, organisation, auth_token, email_sender):
    response = client.get(url_for('organisation.get_senders', organisation_id=organisation.id), headers={
        'Authorization': f'Bearer {auth_token}'
    })
    assert response.status_code == 200
    assert len(response.json) == 1
    assert response.json[0]['name'] == email_sender.name


def test_sender_organisation_relation(organisation, email_sender):
    # We fetch the organisation again to make sure the EmailSender relation is cached.
    organisation = db.session.get(organisation.__class__, organisation.id)

    assert email_sender.organisation_id == organisation.id
    assert email_sender.name == organisation.senders[0].name
    assert email_sender.user_id == organisation.senders[0].user_id
