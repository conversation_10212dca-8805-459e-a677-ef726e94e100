import sys
from os import environ

import pytest
from flask import url_for
from sqlalchemy import URL

from src.build import create_app
from src.functions.aud import create_new_audience
from src.functions.campaign import create_campaign
from src.functions.org import create_new_org
from src.functions.user import register_user
from src.models import db, User


@pytest.fixture
def app():
    sql_url = URL(
        drivername="postgresql",
        username=environ['PG_USER'],
        password=environ['PG_PASSWORD'],
        host=environ['PG_HOST'],
        port=25060,
        database='whatson_test',
        query={'sslmode': 'require'}
    )

    if environ['ENVIRONMENT'] == 'testing':
        sql_url.query['sslmode'] = 'disable'

    app = create_app({
        'TESTING': True,
        'SQLALCHEMY_DATABASE_URI': sql_url,
        'SERVER_NAME': 'whatson.ai',
        'SECRET_KEY': 'test',
        'APPLICATION_ROOT': '/',
        'PREFERRED_URL_SCHEME': 'http',
    })

    with app.app_context():
        db.create_all()

    yield app

    with app.app_context():
        db.drop_all()


@pytest.fixture
def client(app):
    return app.test_client()


@pytest.fixture
def user(app):
    with app.app_context():
        user = register_user('Bram', 'van Ulden', 'WhatsonTest2023!@', '<EMAIL>', True, True)
        yield user
        db.session.delete(user)
        db.session.commit()


@pytest.fixture
def user2(app):
    with app.app_context():
        user = register_user('Bram2', 'van Ulden', 'WhatsonTest2023!@', '<EMAIL>', True, True)
        yield user
        db.session.delete(user)
        db.session.commit()


@pytest.fixture
def organisation(app, user):
    with app.app_context():
        org = create_new_org(user.id, 'Whatson', 'whatson.ai')
        yield org
        db.session.delete(org)
        db.session.commit()


@pytest.fixture
def campaign(app, user, organisation):
    with app.app_context():
        campaign = create_campaign(organisation.id, user.id, 'Test Campaign')
        yield campaign
        db.session.delete(campaign)
        db.session.commit()


@pytest.fixture
def campaign_settings(campaign):
    yield campaign.settings


@pytest.fixture
def audience(app, user, organisation):
    with app.app_context():
        audience = create_new_audience(organisation.id, user.id, [], 'Test Audience', 123)
        yield audience
        db.session.delete(audience)
        db.session.commit()


@pytest.fixture
def auth_token(client, user):
    response = client.post(url_for('user.login'), json={'email': user.email, 'password': 'WhatsonTest2023!@'})
    return response.json['access_token']
