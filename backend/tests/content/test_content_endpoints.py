from io import BytesIO
from flask import url_for
import base64


def test_image_upload_and_get(client, user, organisation, auth_token):
    # generate small b64 image
    test_b64 = b'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAFhAJ/wlseKgAAAABJRU5ErkJggg=='
    test_type = 'image/png'
    response = client.post(url_for('organisation_content.upload_image'), headers={
        'Authorization': f'Bearer {auth_token}'
    }, json={
        'organisation_id': organisation.id,
        'image': test_b64.decode('utf-8'),
        'image_type': test_type
    })

    assert response.status_code == 200

    # retrieve the image
    response = client.get(url_for('organisation_content.get_image',
                                  organisation_id=organisation.id, image_id=response.json['id']),
                          headers={'Authorization': f'Bearer {auth_token}'})
    assert response.status_code == 200
    assert response.data == BytesIO(base64.b64decode(test_b64)).read()
