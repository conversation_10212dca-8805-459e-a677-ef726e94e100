import pytest
from flask import url_for


def test_login(client, user):
    response = client.post(url_for('user.login'), json={
        'email': user.email,
        'password': 'WhatsonTest2023!@'
    })
    assert response.status_code == 200


def test_status(client, user, auth_token):
    response = client.get(url_for('user.status'), headers={
        'Authorization': f'Bearer {auth_token}'
    })
    user_data = response.json['user']

    assert response.status_code == 200
    assert len(user.organisations) == len(user_data['organisations'])
    assert len(user.roles) == len(user_data['roles'])
    assert user_data['email'] == user.email
    assert user_data['first_name'] == user.first_name
    assert user_data['last_name'] == user.last_name
    assert user_data['email_verified'] == user.email_verified


def test_register_new_user(app, client, user):
    response = client.post(url_for('user.register'), json={
        'first_name': 'Test',
        'last_name': 'User',
        'email': '<EMAIL>',
        'password': 'ABC12345',
        'email_optin': True
    })
    assert response.status_code == 200


def test_register_existing_user(app, client, user):
    response = client.post(url_for('user.register'), json={
        'first_name': 'Test',
        'last_name': 'User',
        'email': '<EMAIL>',
        'password': 'ABC12345',
        'email_optin': True
    })
    assert response.status_code == 409


def test_get_notification_settings(app, client, auth_token):
    response = client.get(url_for('user.get_notification_settings'), headers={
        'Authorization': f'Bearer {auth_token}'
    })
    assert response.status_code == 200


def test_update_notification_settings(client, auth_token):
    response = client.put(url_for('user.get_notification_settings'), headers={
        'Authorization': f'Bearer {auth_token}'},
                          json={
                              'settings': {
                                  'email': True,
                                  'in_app': True
                              }})
    assert response.status_code == 200

