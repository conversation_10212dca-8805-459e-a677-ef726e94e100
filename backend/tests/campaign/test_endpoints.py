from datetime import datetime, timedelta

import pytest
from flask import url_for
from src.models import db
from copy import deepcopy


def test_campaign_update_endpoint(client, user, campaign, organisation, auth_token):
    response = client.put(url_for('campaigns.update_a_campaign'), headers={
        'Authorization': f'Bearer {auth_token}'
    }, json={
        'organisation_id': organisation.id,
        'campaign_id': campaign.id,
        'settings': {
            'name': 'Test 123',
            'archived': True,
            'advanced': {
                'sending_window': {
                    "1": ["10:00", "12:00"]
                },
                'budget': 420
            }
        }
    })
    campaign = db.session.get(campaign.__class__, campaign.id)
    assert response.status_code == 200
    assert campaign.name == 'Test 123'
    assert campaign.archived is True
    assert campaign.settings.sending_window == {"1": ["10:00", "12:00"]}

    response = client.put(url_for('campaigns.update_a_campaign'), headers={
        'Authorization': f'Bearer {auth_token}'
    }, json={
        'organisation_id': organisation.id,
        'campaign_id': campaign.id,
        'settings': {
            'advanced': {
                'start_date': datetime.now().isoformat(),
                'end_date': (datetime.now() - timedelta(days=1)).isoformat()
            }
        }
    })
    assert response.status_code == 400


def test_campaign_list_endpoint(client, user, campaign, organisation, auth_token):
    campaign.paused = True
    db.session.add(campaign)
    db.session.commit()
    response = client.post(url_for('campaigns.list_campaigns'), headers={
        'Authorization': f'Bearer {auth_token}'
    }, json={
        'organisation_id': organisation.id,
        'paused': True
    })
    assert response.status_code == 200
    assert len(response.json) == 1
    assert response.json[0]['id'] == str(campaign.id)

    campaign.paused = False
    campaign.archived = True
    db.session.add(campaign)
    db.session.commit()
    response = client.post(url_for('campaigns.list_campaigns'), headers={
        'Authorization': f'Bearer {auth_token}'
    }, json={
        'organisation_id': organisation.id,
        'paused': False,
        'archived': True
    })
    assert response.status_code == 200
    assert len(response.json) == 1
    assert response.json[0]['id'] == str(campaign.id)

    campaign.paused = False
    campaign.archived = False
    campaign.published = True
    db.session.add(campaign)
    db.session.commit()
    response = client.post(url_for('campaigns.list_campaigns'), headers={
        'Authorization': f'Bearer {auth_token}'
    }, json={
        'organisation_id': organisation.id,
        'published': True,
        'archived': False,
        'paused': False
    })
    assert response.status_code == 200
    assert len(response.json) == 1
    assert response.json[0]['id'] == str(campaign.id)

    campaign.paused = False
    campaign.archived = False
    campaign.published = False
    db.session.add(campaign)
    db.session.commit()
    response = client.post(url_for('campaigns.list_campaigns'), headers={
        'Authorization': f'Bearer {auth_token}'
    }, json={
        'organisation_id': organisation.id,
        'published': False,
        'archived': False,
        'paused': False
    })
    assert response.status_code == 200
    assert len(response.json) == 1
    assert response.json[0]['id'] == str(campaign.id)
