from src.models import db, Campaign


def test_settings(campaign_settings):
    campaign_settings.budget = 12345
    campaign_settings.sending_window = {
        '1': ['10:00', '12:00'],
        '3': ['10:00', '12:00']
    }
    campaign_settings.budget_type = 'daily'

    db.session.add(campaign_settings)
    db.session.commit()

    campaign = Campaign.query.filter_by(id=campaign_settings.campaign_id).one_or_none()
    assert campaign is not None
    assert campaign.settings.budget == 12345
    assert campaign.settings.sending_window == {
        '1': ['10:00', '12:00'],
        '3': ['10:00', '12:00']
    }
    assert campaign.settings.budget_type == 'daily'

