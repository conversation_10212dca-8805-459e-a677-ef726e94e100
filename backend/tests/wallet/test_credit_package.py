import pytest
from src.models import CreditPackage, db
from datetime import datetime


@pytest.fixture
def credit_package(app, organisation):
    with app.app_context():
        credit_package = CreditPackage(
            organisation_id=organisation.id,
            amount=1000,
            price=100
        )
        db.session.add(credit_package)
        db.session.commit()
        yield credit_package
        db.session.delete(credit_package)
        db.session.commit()


def test_credit_package_creation(organisation):
    cp = CreditPackage(
        organisation_id=organisation.id,
        created_at=datetime.utcnow(),
        amount=500,
        price=50
    )
    db.session.add(cp)
    db.session.commit()

    assert cp.id is not None
    assert cp.organisation_id == organisation.id
    assert cp.amount == 500
    assert cp.price == 50


def test_credit_package_create_transaction(credit_package, organisation):
    transaction = credit_package.create_transaction()
    assert transaction is not None
    assert transaction.amount == credit_package.amount
    assert transaction.wallet.organisation_id == organisation.id
    assert transaction.type == 'credit'
