import pytest
from src.models import Subscription, db
from datetime import datetime, timedelta


def test_create_sub(organisation):
    subscription = Subscription(created_at=datetime.utcnow(), starts_at=datetime.utcnow(),
                                ends_at=datetime.utcnow() + timedelta(days=30), expired=False,
                                organisation_id=organisation.id)
    db.session.add(subscription)
    db.session.commit()
    assert subscription.id is not None
    assert subscription.organisation_id == organisation.id


def test_one_sub_per_org(organisation):
    sub1 = Subscription(created_at=datetime.utcnow(), starts_at=datetime.utcnow(),
                        ends_at=datetime.utcnow() + timedelta(days=30), expired=False, organisation_id=organisation.id)
    db.session.add(sub1)
    db.session.commit()

    sub2 = Subscription(created_at=datetime.utcnow(), starts_at=datetime.utcnow(),
                        ends_at=datetime.utcnow() + timedelta(days=30), expired=False, organisation_id=organisation.id)

    with pytest.raises(ValueError):
        sub2.save()  # Validating one active package at a time


def test_org_sub_property(organisation):
    sub1 = Subscription(created_at=datetime.utcnow(), starts_at=datetime.utcnow(),
                        ends_at=datetime.utcnow() + timedelta(days=30), expired=False, organisation_id=organisation.id)
    db.session.add(sub1)
    db.session.commit()

    assert organisation.current_subscription == sub1
