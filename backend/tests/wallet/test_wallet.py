from flask import url_for
from src.models import Transaction, db, TransactionService


def test_create_transaction_credit(app, organisation):
    with app.app_context():
        assert organisation.get_wallet_balance() == 0

        TransactionService.create_transaction(organisation.id, 50, 'credit')

        assert organisation.get_wallet_balance() == 50


def test_create_transaction_debit(app, organisation):
    with app.app_context():
        TransactionService.create_transaction(organisation.id, 50, 'credit')

        assert organisation.get_wallet_balance() == 50

        TransactionService.create_transaction(organisation.id, 25, 'debit')

        assert organisation.get_wallet_balance() == 25


def test_delete_transaction(app, organisation):
    with app.app_context():
        TransactionService.create_transaction(organisation.id, 50, 'credit')

        assert organisation.get_wallet_balance() == 50

        transaction = Transaction.query.filter_by(wallet_id=organisation.wallet.id, amount=50, type='credit').first()
        TransactionService.delete_transaction(transaction.id)

        assert organisation.get_wallet_balance() == 0


def test_get_balance_endpoint(client, app, organisation, auth_token):
    with app.app_context():
        wallet = organisation.wallet
        response = client.get(url_for('organisation.get_balance', organisation_id=wallet.organisation_id),
                              headers={'Authorization': f'Bearer {auth_token}'})
        assert response.status_code == 200
        assert response.json['balance'] == organisation.get_wallet_balance()
