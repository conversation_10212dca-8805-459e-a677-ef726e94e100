import pytest
from src.models import Subscription, CreditPackage, db, EmailSender
from src.functions.admin import (get_active_organisations, count_senders,
                                 get_credit_usage, get_credit_packages, get_billing_info, CREDIT_COST, SENDER_COST)
from datetime import datetime, timedelta

from src.utils import mail_database


@pytest.fixture
def active_subscription(app, organisation):
    # Create an active Subscription fixture
    with app.app_context():
        # We set the ends_at to 30 minutes from now, so it's active and it can be tested on
        subscription = Subscription(organisation_id=organisation.id, ends_at=datetime.now() + timedelta(minutes=30))
        db.session.add(subscription)
        db.session.commit()
        yield subscription
        db.session.delete(subscription)
        db.session.commit()


@pytest.fixture
def credit_package(app, organisation):
    # Create a CreditPackage fixture
    with app.app_context():
        credit_package = CreditPackage(organisation_id=organisation.id, amount=100, price=100)
        db.session.add(credit_package)
        db.session.commit()
        yield credit_package
        db.session.delete(credit_package)
        db.session.commit()


#def test_get_active_organisations(organisation, active_subscription):
#    organisations = get_active_organisations(datetime.now() - timedelta(days=1), datetime.now() + timedelta(days=1))
#    assert organisation.id == organisations[0].id


def test_count_senders(organisation, user):
    num_senders = count_senders(organisation)
    assert num_senders == 0
    db.session.add(EmailSender(organisation_id=organisation.id, name='Bram', email='<EMAIL>', user_id=user.id))
    assert count_senders(organisation) == 1


#def test_get_credit_usage(organisation):
#    mail_database[str(organisation.id)].client.insert_many([
#        {'outreaches': [{'timestamp': datetime.now() - timedelta(hours=4)}]},
#        {'outreaches': [{'timestamp': datetime.now() - timedelta(hours=4)}]}
#    ])
#    start_date = datetime.utcnow() - timedelta(days=1)
#    end_date = datetime.utcnow()
#    usage = get_credit_usage(organisation, start_date, end_date)
#    assert usage == 2
#    mail_database.db.drop_collection(str(organisation.id))


def test_get_credit_packages(organisation, credit_package):
    start_date = datetime.utcnow() - timedelta(days=30)
    end_date = datetime.utcnow()
    packages = get_credit_packages(organisation, start_date, end_date)
    assert credit_package in packages


#def test_get_billing(organisation, user, active_subscription):
#    # Create two Senders, meaning one is billable
#    db.session.add(EmailSender(organisation_id=organisation.id, name='Bram', email='<EMAIL>', user_id=user.id))
#    db.session.add(EmailSender(organisation_id=organisation.id, name='Bram', email='<EMAIL>', user_id=user.id))
#    db.session.commit()
#
#    # Create two outreaches, meaning two credits are used
#    mail_database[str(organisation.id)].client.insert_many([
#        {'outreaches': [{'timestamp': datetime.now() - timedelta(hours=4)}]},
#        {'outreaches': [{'timestamp': datetime.now() - timedelta(hours=4)}]}
#    ])
#
#    start = datetime.now() - timedelta(days=15)
#    end = datetime.now() + timedelta(days=15)
#
#    billing_info = get_billing_info(start, end)
#    assert billing_info[0]['billable_senders'] == 1
#    assert billing_info[0]['credit_usage'] == 2
#    assert billing_info[0]['total_cost'] == (1 * SENDER_COST) + (2 * CREDIT_COST)
#    assert billing_info[0]['id'] == str(organisation.id)
#    assert billing_info[0]['name'] == organisation.org_name
#
#    # Clean up
#    mail_database.db.drop_collection(str(organisation.id))
