from typing import Literal, Union
from Holmes.campaign.flow import Flow
from Holmes.sources.postgres.filter import FrontendFilter, FrontendFilterFactory
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import event, PickleType
from sqlalchemy.dialects.postgresql import UUID, JSON, JSONB
from uuid import uuid4
from datetime import datetime
from datetime import timedelta
from secrets import token_urlsafe
from sqlalchemy.orm import reconstructor
from werkzeug.security import generate_password_hash, check_password_hash
from random import SystemRandom
import json

db = SQLAlchemy(engine_options={'pool_pre_ping': True})


def generate_6_random_code() -> str:
    sr = SystemRandom()
    return ''.join([f'{sr.randint(1, 9)}' for _ in range(6)])


def one_hour_from_now_utc() -> datetime:
    return datetime.utcnow() + timedelta(hours=1)


def generate_32_char_token() -> str:
    return token_urlsafe(32)


class Audience(db.Model):
    id = db.Column(UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4, unique=True)
    organisation_id = db.Column(UUID(as_uuid=True), db.ForeignKey('organisation.id', ondelete='CASCADE'),
                                primary_key=True)
    created_by = db.Column(UUID(as_uuid=True), db.ForeignKey('user.id', ondelete='CASCADE'), primary_key=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    filters = db.Column(JSON, nullable=True)
    name = db.Column(db.String(100), nullable=False)
    limit = db.Column(db.Integer)

    personal_email_only = db.Column(db.Boolean, nullable=False, default=False)
    correct_function_only = db.Column(db.Boolean, nullable=False, default=False)
    status = db.Column(db.String(20), nullable=False, default='draft')
    prospects_added = db.Column(db.Integer, nullable=True)

    campaigns = db.relationship('Campaign', back_populates='audience', cascade='all, delete-orphan')

    def json(self, frontend_filters: bool = True) -> dict:
        f = self.filters if self.filters else []

        # Convert filters to frontend format only if they aren't serialized yet.
        # Otherwise, on Audience creation, we have to call init_on_load() manually, which messes with the ORM.
        if self.filters and not isinstance(self.filters[0], dict):
            if frontend_filters:
                f = [fil.to_dict() for fil in f]
            else:
                f = [fil.to_filter().to_dict() for fil in f]

        return {
            'id': str(self.id),
            'name': self.name,
            'organisation_id': str(self.organisation_id),
            'created_at': self.created_at.timestamp(),
            'created_by': str(self.created_by),
            'filters': f,
            'limit': self.limit,
            'personal_email_only': self.personal_email_only,
            'correct_function_only': self.correct_function_only,
            'prospects_added': self.prospects_added,
            'status': self.status
        }

    @property
    def json_lite(self) -> dict:
        return {
            'id': str(self.id),
            'name': self.name
        }

    @reconstructor
    def init_on_load(self):
        if not self.filters:
            return
        parsed_filters = [FrontendFilterFactory.from_dict(rf) for rf in self.filters]
        self.filters = parsed_filters


class Campaign(db.Model):
    __tablename__ = 'campaigns'

    id = db.Column(UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4)
    name = db.Column(db.String(100), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('user.id'), nullable=False)
    organisation_id = db.Column(UUID(as_uuid=True), db.ForeignKey('organisation.id'), nullable=False)
    audience_id = db.Column(UUID(as_uuid=True), db.ForeignKey('audience.id'), nullable=True)

    flow = db.Column(db.Text, nullable=True)
    paused = db.Column(db.Boolean, nullable=False, default=False)
    published = db.Column(db.Boolean, nullable=False, default=False)
    archived = db.Column(db.Boolean, nullable=False, default=False)

    status = db.Column(db.String(20), nullable=True)

    audience = db.relationship('Audience', back_populates='campaigns')
    settings = db.relationship('CampaignSettings',
                               back_populates='campaign', uselist=False, cascade='all, delete-orphan')

    def __init__(self, user_id: str, organisation_id: str, name: str, audience_id=None, flow=None, id=None):
        if id:
            self.id = id
        self.user_id = user_id
        self.organisation_id = organisation_id
        self.audience_id = audience_id
        self.name = name
        self.flow = flow

    @reconstructor
    def init_on_load(self):
        if not self.flow:
            return
        self.flow = Flow.from_dict(json.loads(self.flow))

    def to_dict(self):
        status = 'Unknown'
        if self.status == 'Running' and not self.paused:
            status = 'Running'

        elif self.published and not self.paused:
            status = 'Published'
            if self.settings.start_date and self.settings.start_date > datetime.today():
                status = 'Scheduled'

        if self.status == 'Completed':
            status = 'Completed'

        elif self.paused:
            status = 'Paused'

        if not self.published and not self.status:
            status = 'Draft'

        # todo 2023-07-25: remove this hacky fix
        if isinstance(self.flow, str):
            flow = self.flow
        elif isinstance(self.flow, Flow):
            flow = self.flow.to_dict()
        else:
            flow = None

        return {
            'id': self.id,
            'user_id': self.user_id,
            'organization_id': self.organisation_id,
            'audience_id': self.audience_id,
            'created_at': self.created_at.isoformat(),
            'name': self.name,
            'status': status,
            'paused': self.paused,
            'published': self.published,
            'flow': flow,
            'settings': self.settings.json if self.settings else {}
        }

    @property
    def json_lite(self) -> dict:
        return {
            'id': self.id,
            'name': self.name,
        }


class CampaignSettings(db.Model):
    __tablename__ = 'campaign_settings'
    id = db.Column(UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4)
    campaign_id = db.Column(UUID(as_uuid=True), db.ForeignKey('campaigns.id', ondelete='CASCADE'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    budget = db.Column(db.Integer, nullable=False, default=0)
    budget_type = db.Column(db.String(10), nullable=False, default='daily')
    start_date = db.Column(db.DateTime, nullable=True)
    end_date = db.Column(db.DateTime, nullable=True)
    sending_window = db.Column(PickleType, default=dict)

    campaign = db.relationship('Campaign', back_populates='settings')

    @property
    def json(self) -> dict:
        return {
            'id': self.id,
            'campaign_id': self.campaign_id,
            'created_at': self.created_at.isoformat(),
            'budget': self.budget,
            'budget_type': self.budget_type,
            'start_date': self.start_date.isoformat() if self.start_date else None,
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'sending_window': self.sending_window if self.sending_window else {
                1: ['09:00', '17:00'],
                2: ['09:00', '17:00'],
                3: ['09:00', '17:00'],
                4: ['09:00', '17:00'],
                5: ['09:00', '17:00'],
    }
        }


class Organisation(db.Model):
    id = db.Column(UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4)

    org_name = db.Column(db.String(100), nullable=False)
    org_website = db.Column(db.String(100), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    users = db.relationship('User', secondary='user_org_role', back_populates='organisations', lazy='joined')
    audiences = db.relationship('Audience', backref='organisation', lazy='select')
    campaigns = db.relationship('Campaign', backref='organisation', lazy='select')
    senders = db.relationship('EmailSender', backref='organisation', lazy='select', cascade='all, delete-orphan')
    wallet = db.relationship('Wallet', backref='organisation', lazy='select',
                             uselist=False, cascade='all, delete-orphan')

    def __repr__(self):
        return f"<Organisation org_id={self.id} org_name='{self.org_name}'>"

    @property
    def current_subscription(self) -> 'Subscription':
        return next((package for package in self.packages if not package.expired), None)

    @property
    def json(self):
        return {
            'id': self.id,
            'name': self.org_name,
            'website': self.org_website,
            'users': [user.public(self.id) for user in self.users],
            'audiences': [audience.json() for audience in self.audiences],
            'campaigns': [campaign.to_dict() for campaign in self.campaigns],
            'created_at': self.created_at.isoformat(),
        }

    @property
    def json_lite(self) -> dict:
        return {
            'id': self.id,
            'name': self.org_name,
            'website': self.org_website,
            'user_count': len(self.users),
            # temporary base64 1x1 image to be replaced with logo in the future, this is for Akker to implement
            # their adapter in the meantime.
            'logo': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAFhQJ/6+gX8wAAAABJRU5ErkJggg=='
        }

    def has_campaign_id(self, campaign_id: str) -> bool:
        """
        Checks if the Organisation has a campaign with the specified id.
        :param campaign_id: Id of the Campaign to check for.
        :return: True if the Campaign exists in the Organisation's campaigns, False otherwise.
        """
        return any(campaign.id == campaign_id for campaign in self.campaigns)

    def has_audience_id(self, audience_id: str) -> bool:
        """
        Checks if the Organisation has an audience with the specified id.
        :param audience_id: Id of the Audience to check for.
        :return: True if the Audience exists in the Organisation's audiences, False otherwise.
        """
        return any(audience.id == audience_id for audience in self.audiences)

    def get_wallet_balance(self) -> int:
        # We need to fetch the wallet from the database to get the most recent balance. This is because the balance
        #  is updated in the database when transactions are made. We cannot rely on the balance property of the
        #  wallet object in memory due to persistence and caching.
        wallet = db.session.get(Wallet, self.wallet.id)
        return wallet.balance


class Wallet(db.Model):
    id = db.Column(UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4)
    organisation_id = db.Column(UUID(as_uuid=True), db.ForeignKey('organisation.id', ondelete='CASCADE'), nullable=False)
    balance = db.Column(db.Integer, nullable=False, default=0.00)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)


class Transaction(db.Model):
    id = db.Column(UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4)
    wallet_id = db.Column(UUID(as_uuid=True), db.ForeignKey('wallet.id', ondelete='CASCADE'), nullable=False)
    amount = db.Column(db.Integer, nullable=False)
    type: Literal['credit', 'debit'] = db.Column(db.String(50), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    wallet = db.relationship('Wallet', backref=db.backref('transactions', lazy=True, cascade='all, delete-orphan'))


class CreditPackage(db.Model):
    id = db.Column(UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    amount = db.Column(db.Integer, nullable=False)
    price = db.Column(db.Integer, nullable=False)

    organisation_id = db.Column(UUID(as_uuid=True),
                                db.ForeignKey('organisation.id', ondelete='CASCADE'), nullable=False)
    organisation = db.relationship('Organisation', backref=db.backref('credit_packages', lazy=True,
                                                                      cascade='all, delete-orphan'))

    def create_transaction(self) -> 'Transaction':
        return TransactionService.create_transaction(self.organisation_id, self.amount, 'credit')


class TransactionService:
    @staticmethod
    def create_transaction(organisation_id: str,
                           amount: int,
                           type: Literal['credit', 'debit'],
                           time=None) -> Transaction:
        org = db.session.get(Organisation, organisation_id)
        wallet_id = org.wallet.id

        if not time:
            transaction = Transaction(wallet_id=wallet_id, amount=amount, type=type)
        else:
            transaction = Transaction(wallet_id=wallet_id, amount=amount, type=type, created_at=time)

        db.session.add(transaction)

        wallet = db.session.get(Wallet, wallet_id)
        if type == 'credit':
            wallet.balance += amount
        elif type == 'debit' and wallet.balance >= amount:
            wallet.balance -= amount
        else:
            # todo: handle this error
            raise ValueError('Insufficient funds')

        db.session.add(wallet)
        db.session.commit()
        return transaction

    @staticmethod
    def delete_transaction(transaction_id) -> None:
        transaction = db.session.get(Transaction, transaction_id)
        wallet = db.session.get(Wallet, transaction.wallet_id)

        if transaction.type == 'credit':
            wallet.balance -= transaction.amount
        elif transaction.type == 'debit':
            wallet.balance += transaction.amount

        db.session.delete(transaction)
        db.session.add(wallet)
        db.session.commit()


class EmailSender(db.Model):
    __tablename__ = 'email_senders'
    id = db.Column(UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4)
    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('user.id', ondelete='CASCADE'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(100), nullable=False)
    data = db.Column(db.String(), nullable=True)
    is_sparkpost_sender = db.Column(db.Boolean, nullable=True, default=True)

    domain_id = db.Column(UUID(as_uuid=True), db.ForeignKey('sending_domains.id', ondelete='CASCADE'), nullable=True)
    organisation_id = db.Column(UUID(as_uuid=True), db.ForeignKey('organisation.id', ondelete='CASCADE'), nullable=False)

    @reconstructor
    def init_on_load(self):
        self.data = json.loads(self.data) if self.data else {}

    @property
    def json(self):
        return {
            'id': self.id,
            'name': self.name,
            'email': self.email,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
        }

    @property
    def service_json(self):
        return {
            'id': self.id,
            'name': self.name,
            'email': self.email,
            'data': self.data,
            'is_sparkpost_sender': self.is_sparkpost_sender
        }


class SendingDomain(db.Model):
    __tablename__ = 'sending_domains'
    id = db.Column(UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4)
    organisation_id = db.Column(UUID(as_uuid=True), db.ForeignKey('organisation.id', ondelete='CASCADE'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    domain = db.Column(db.String(100), nullable=False)

    is_verified = db.Column(db.Boolean, nullable=False, default=False)
    dns_records = db.Column(JSONB, nullable=True)

    @property
    def json(self):
        return {
            'id': self.id,
            'organisation_id': self.organisation_id,
            'domain': self.domain,
            'dns_records': self.dns_records,
            'is_verified': self.is_verified,
        }

    def create_sender(self, name: str, email: str, user_id: Union[str, UUID]) -> EmailSender:
        if not email.endswith(self.domain):
            raise ValueError(f"Email address {email} does not match domain {self.domain}")

        return EmailSender(
            user_id=user_id,
            name=name,
            email=email,
            is_sparkpost_sender=True,
            organisation_id=self.organisation_id,
            domain_id=self.id
        )


class PasswordReset(db.Model):
    id = db.Column(UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4)
    reset_identifier = db.Column(db.String(), nullable=False, primary_key=True, default=generate_32_char_token)
    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('user.id', ondelete='CASCADE'), primary_key=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    expires_at = db.Column(db.DateTime, default=one_hour_from_now_utc)
    used = db.Column(db.Boolean, nullable=False, default=False)


class UserOrgRole(db.Model):
    __tablename__ = 'user_org_role'

    id = db.Column(UUID, primary_key=True, default=uuid4)
    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('user.id', ondelete='CASCADE'), primary_key=True)
    organisation_id = db.Column(UUID(as_uuid=True), db.ForeignKey('organisation.id', ondelete='CASCADE'))

    role = db.Column(db.String(50), nullable=False)
    accepted = db.Column(db.Boolean, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    __table_args__ = (
        db.UniqueConstraint('user_id', 'organisation_id'),
    )

    @property
    def json(self):
        return {
            'id': self.id,
            'organisation_id': self.organisation_id,
            'role': self.role,
            'accepted': self.accepted,
            'created_at': self.created_at.timestamp()
        }


class UserNotificationSettings(db.Model):
    __tablename__ = 'user_notification_settings'
    id = db.Column(UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4)
    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('user.id', ondelete='CASCADE'), primary_key=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow)
    email = db.Column(db.Boolean, nullable=False, default=True)
    in_app = db.Column(db.Boolean, nullable=False, default=True)
    on_campaign_publish = db.Column(db.Boolean, nullable=False, default=True)
    on_campaign_update = db.Column(db.Boolean, nullable=False, default=True)
    on_campaign_status = db.Column(db.Boolean, nullable=False, default=True)
    on_all_campaign_responses = db.Column(db.Boolean, nullable=False, default=True)
    on_all_owned_campaign_responses = db.Column(db.Boolean, nullable=False, default=True)

    @property
    def json(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'email': self.email,
            'in_app': self.in_app,
            'on_campaign_publish': self.on_campaign_publish,
            'on_campaign_update': self.on_campaign_update,
            'on_campaign_status': self.on_campaign_status,
            'on_all_campaign_responses': self.on_all_campaign_responses,
            'on_all_owned_campaign_responses': self.on_all_owned_campaign_responses
        }


class User(db.Model):
    id = db.Column(UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4)
    first_name = db.Column(db.String(40), nullable=False)
    last_name = db.Column(db.String(40), nullable=False)
    password = db.Column(db.String(), nullable=False)
    email = db.Column(db.String(40), nullable=False, unique=True)
    email_verified = db.Column(db.Boolean, nullable=False, default=False)
    email_optin = db.Column(db.Boolean, nullable=False, default=False)
    is_superadmin = db.Column(db.Boolean, default=False)

    notification_settings = db.relationship('UserNotificationSettings', backref='user', lazy='select', uselist=False,
                                            cascade='all, delete-orphan')
    roles = db.relationship('UserOrgRole', backref='user', cascade='all, delete-orphan', lazy='select')
    organisations = db.relationship('Organisation', secondary='user_org_role', back_populates='users',
                                    cascade='all', lazy='select')

    def __str__(self):
        return f'{self.first_name} {self.last_name} <{self.email}>'

    def check_password(self, plain_password: str) -> bool:
        return check_password_hash(self.password, plain_password)

    def get_role(self, org_id: str) -> UserOrgRole or None:
        for role in self.roles:
            if role.organisation_id == org_id:
                return role

        if self.is_superadmin:
            return UserOrgRole(role='owner')

        return None

    @classmethod
    def find_by_user_id(cls, user_id):
        return cls.query.filter_by(id=user_id).one_or_none()

    @property
    def json(self):
        return {
            'id': self.id,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'email': self.email,
            'email_verified': self.email_verified,
            'email_optin': self.email_optin,
            'roles': [role.json for role in self.roles],
            'organisations': [organisation for organisation in self.organisations],
            'notification_settings': self.notification_settings.json if self.notification_settings else {}
        }

    @property
    def json_lite(self):
        return {
            'id': self.id,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'email': self.email,
            'email_verified': self.email_verified,
            'email_optin': self.email_optin,
            'roles': [role.json for role in self.roles],
            'organisations': [organisation.json_lite for organisation in self.organisations],
            'notification_settings': self.notification_settings.json if self.notification_settings else {}
        }

    def public(self, org_id: str):
        result = [role for role in self.roles if role.organisation_id == org_id]
        if not result:
            return {
                {
                    'id': 'CASPER-3',
                    'first_name': 'Unknown',
                    'last_name': 'Unknown',
                }
            }
        return {
            'id': self.id,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'email': self.email,
            'role': result[0].role
        }


class Subscription(db.Model):
    __tablename__ = 'subscriptions'

    id = db.Column(UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    starts_at = db.Column(db.DateTime, default=datetime.utcnow)
    ends_at = db.Column(db.DateTime, nullable=False, default=lambda: datetime.utcnow() + timedelta(days=30))
    expired = db.Column(db.Boolean, default=False)

    credits_per_month = db.Column(db.Integer, nullable=False, default=500)
    price_per_month = db.Column(db.Integer, nullable=False, default=500)
    senders_included = db.Column(db.Integer, nullable=False, default=1)

    organisation_id = db.Column(UUID(as_uuid=True), db.ForeignKey('organisation.id'), nullable=False)
    organisation = db.relationship('Organisation', backref=db.backref('packages', cascade='all, delete-orphan'))

    def save(self, *args, **kwargs):
        # Validation to ensure only one non-expired package is associated at a time
        non_expired_packages = Subscription.query.filter_by(organisation_id=self.organisation_id, expired=False).all()
        if non_expired_packages and not self.expired:
            raise ValueError("Another non-expired Subscription is already associated with this organisation.")

        super().save(*args, **kwargs)


class EmailValidator(db.Model):
    id = db.Column(UUID, primary_key=True, default=uuid4)
    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('user.id', ondelete='CASCADE'), primary_key=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    accepted = db.Column(db.Boolean, nullable=False, default=False)
    code = db.Column(db.String(6), nullable=False, default=generate_6_random_code)


class BackendService(db.Model):
    __tablename__ = 'backend_services'
    id = db.Column(UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4)
    name = db.Column(db.String(40), nullable=False)
    api_key = db.Column(db.String(), nullable=False, default=lambda x: generate_password_hash(token_urlsafe(32)))

    def generate_new_api_key(self) -> str:
        api_key = token_urlsafe(32)
        self.api_key = api_key
        return api_key


class DataPoint(db.Model):
    # todo: set package property to null when organisation package is high enough
    __tablename__ = 'datapoints'
    id = db.Column(UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4)
    slug_name = db.Column(db.String(40), nullable=False)
    type = db.Column(db.String(40), nullable=False)
    source = db.Column(db.String(40), nullable=True)
    visual_name = db.Column(db.String(40), nullable=True)
    package = db.Column(db.String(40), nullable=True)
    prefab_filters = db.relationship('PrefabFilter', back_populates='data_point')
    translations = db.relationship('DataPointTranslation', back_populates='data_point')


class DataPointTranslation(db.Model):
    __tablename__ = 'datapointtranslations'
    id = db.Column(UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4)
    datapoint_id = db.Column(UUID(as_uuid=True), db.ForeignKey('datapoints.id'), nullable=False)
    value = db.Column(db.String(), nullable=False)
    translation = db.Column(db.String(), nullable=False)

    data_point = db.relationship('DataPoint', back_populates='translations')


class PrefabFilter(db.Model):
    __tablename__ = 'prefab_filters'
    id = db.Column(UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4)
    filter_class_name = db.Column(db.String(40), nullable=False)
    data_point_id = db.Column(UUID(as_uuid=True), db.ForeignKey('datapoints.id'), nullable=False)
    data_point = db.relationship('DataPoint', back_populates='prefab_filters')
    visual_name = db.Column(db.String(40), nullable=True)
    input_type: Literal['number', 'string', 'datetime', 'option', 'boolean'] = db.Column(db.String(40), nullable=True)
    category = db.Column(db.String(40), nullable=True)
    tooltip = db.Column(db.String(), nullable=True)
    data = db.Column(db.JSON, nullable=True)

    def to_frontend(self) -> FrontendFilter:
        data = {
            'target_type': self.data_point.type,
            'target_field': self.data_point.slug_name,
            'visual_name': self.visual_name,
            'type': self.filter_class_name,
            'id': self.id,
            'category': self.category if self.category else self.data_point.type.capitalize(),
            'input_type': self.input_type if self.input_type else 'text',
            'tooltip': self.tooltip
        }

        if self.data:
            data.update(self.data)

        return FrontendFilterFactory.from_dict(data)


class EmailTagForward(db.Model):
    __tablename__ = 'email_tag_forwards'
    id = db.Column(UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid4)
    tag = db.Column(db.String(40), nullable=False)
    email = db.Column(db.String(40), nullable=False)
    organisation_id = db.Column(UUID(as_uuid=True), db.ForeignKey('organisation.id', ondelete='CASCADE'), nullable=False)

    @property
    def json(self):
        return {
            'id': self.id,
            'tag': self.tag,
            'email': self.email
        }


@event.listens_for(BackendService.api_key, 'set', retval=True)
def hash_api_key(target, value, old_value, initiator):
    if value != old_value:
        return generate_password_hash(value)
    return value


@event.listens_for(User.password, 'set', retval=True)
def hash_user_password(target, value, old_value, initiator):
    if value != old_value:
        return generate_password_hash(value)
    return value


if __name__ == '__main__':
    print(User.query.filter_by(username='bramvanulden1234').one_or_none().organisations)
