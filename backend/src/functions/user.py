from os import environ
from secrets import choice
from string import ascii_letters, digits
from src.models import User, db, PasswordReset, EmailValidator, UserNotificationSettings
from datetime import datetime, date, timedelta
import requests as r


class UnknownUserError(Exception):
    def __init__(self, identifier):
        self.identifier = identifier

    def __str__(self):
        return f'User {self.identifier} was supposedly authenticated, but could not be found in DB.'


class UserAlreadyExists(Exception):
    def __str__(self):
        return f'A user by that name already exists.'


def generate_password() -> str:
    """
    Generates a password consisting of 12 characters, made up of special characters, symbols, upper and lowercase text.
    :return: a password string
    """
    punctuation = '!@#$%^&*()'
    alphabet = ascii_letters + digits + punctuation
    password = ''

    # generate a password while none of the punctuation letters are in the password
    while (not [symbol for symbol in punctuation if symbol in password]
           and not [digit for digit in digits if digit in password]
           and not [p for p in punctuation if p in password]):
        password = ''.join(choice(alphabet) for _ in range(20))

    return password


def register_user(first_name: str, last_name: str, password: str, email: str, email_optin: bool,
                  email_verified: bool = False) -> User:
    # Check if user already exists
    existing_user = User.query.filter_by(email=email).first()
    if existing_user:
        raise UserAlreadyExists

    # Create new user with provided credentials
    new_user = User(first_name=first_name, password=password, email=email,
                    last_name=last_name, email_optin=email_optin, email_verified=email_verified)
    db.session.add(new_user)
    db.session.commit()

    # We need to create the settings object after the user has been added to the db, so we can get the user id
    # Create new user notification settings, with default values
    new_settings = UserNotificationSettings(user_id=new_user.id)
    db.session.add(new_settings)
    db.session.commit()

    # The db_name check is there to prevent verification emails being sent
    # to the test emails.
    if email_verified is False and environ['ENVIRONMENT'] == 'production':
        request_email_validation(new_user)
    return new_user


def update_settings(user_id: str, new_settings: dict) -> (str, int):
    settings = UserNotificationSettings.query.filter_by(user_id=user_id).one_or_none()

    if not settings:
        settings = UserNotificationSettings(user_id=user_id)

    for key, value in new_settings.items():
        if key in ['id', 'user_id', 'created_at', 'updated_at']:
            continue
        try:
            setattr(settings, key, value)
        except AttributeError:
            # this means someone is trying to set a setting that doesn't exist
            return 'Something went wrong, please try again later.', 500

    db.session.add(settings)
    db.session.commit()

    return 'Settings saved', 200


def reset_password(reset_identifier: str, new_password: str) -> (str, int):
    reset = PasswordReset.query.filter_by(reset_identifier=reset_identifier).one_or_none()
    if not reset:
        return 'Something went wrong. Please try requesting another password reset.', 401

    if datetime.utcnow() > reset.expires_at or reset.used:
        return 'Password reset has expired. Please request another.', 410

    # Getting the user object, and updating its password and the PasswordReset status
    user = User.query.filter_by(id=reset.user_id).one_or_none()
    user.password = new_password
    reset.used = True

    # Adding the updated objects to the session and committing
    db.session.add(user)
    db.session.add(reset)
    db.session.commit()

    return 'Successfully changed password.', 200


def request_password_reset(email: str) -> (str or None, str, int):
    # Try to find the user in our db
    user = User.query.filter_by(email=email).first()
    if not user:
        print("NO USER >:(")
        return None, 'Successfully requested password reset', 200

    # Get their latest password resets
    yesterday = date.today() - timedelta(days=1)
    resets = PasswordReset.query.filter_by(user_id=user.id).filter(PasswordReset.created_at > yesterday).all()

    # Max 5 resets?
    if len(resets) >= 999999:
        return None, 'Successfully requested password reset', 200

    # Create a new PasswordReset object
    new_reset = PasswordReset(user_id=user.id)
    db.session.add(new_reset)
    db.session.commit()
    r.post('https://mail.whatsoff.nl/send_password_reset',
           headers={'key': 'SUPERSECRETREQUESTKEY2022WHAZZONFOREVA'},
           json={'name': user.first_name, 'email': user.email, 'reset_token': new_reset.reset_identifier}, auth=False)
    return new_reset.reset_identifier, 'Successfully requested password reset', 200


def request_email_validation(user: User) -> (str or None, str, int):
    if user.email_verified:
        return None, 'Your email address is already validated.', 401

    yesterday = date.today() - timedelta(days=1)
    validations = EmailValidator.query.filter_by(user_id=user.id).filter(EmailValidator.created_at > yesterday).all()

    if len(validations) > 5:
        return None, 'You have requested too many email validations.', 401

    # Create new validation
    validation = EmailValidator(user_id=user.id)
    db.session.add(validation)
    db.session.commit()

    r.post('https://mail.whatsoff.nl/send_email_verification',
           headers={'key': 'SUPERSECRETREQUESTKEY2022WHAZZONFOREVA'},
           json={'name': user.first_name, 'email': user.email, 'reset_token': validation.code}, auth=False)

    return validation.code, 'Successfully requested email validation.', 200


def validate_email(validation_code: str, user: User) -> (str, int):
    validation = EmailValidator.query.filter_by(user_id=user.id, code=validation_code).one_or_none()
    if not validation:
        return 'Validation failed.', 401

    validation.accepted = True
    user.email_verified = True
    db.session.add(validation)
    db.session.add(user)
    db.session.commit()

    return 'Successfully validated email.', 200
