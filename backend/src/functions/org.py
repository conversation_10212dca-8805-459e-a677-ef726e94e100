from typing import Literal
from src.functions.user import UnknownUserError
from flask import request, jsonify
from functools import wraps
from src.models import Organisation, User, UserOrgRole, db, Campaign, Wallet
from settings import ORG_SETTINGS
from flask_jwt_extended import current_user


class OrganisationLimitExceeded(Exception):
    def __init__(self, org_count: int):
        self.org_count = org_count

    def __str__(self):
        return f'You have reached the maximum of {self.org_count} organisation ownerships. You can still join other ' \
               f'organisations as a member.'


class OrganisationAlreadyExists(Exception):
    def __init__(self, org_domain: str):
        self.org_domain = org_domain

    def __str__(self):
        return f'An organisation using {self.org_domain} already exists.'


class OrganisationDoesntExist(Exception):
    def __str__(self):
        return f'The organisation you are trying to modify no longer exists.'


def add_user_to_org(org_id: str, user_id: str, role: str, accepted=False) -> UserOrgRole:
    organisation = Organisation.query.filter_by(id=org_id).one_or_none()
    user = User.query.filter_by(id=user_id).one_or_none()

    if not organisation:
        raise OrganisationDoesntExist

    if not user:
        raise UnknownUserError(user_id)

    # Check if the user is already a member of the organization
    existing_role = UserOrgRole.query.filter_by(user_id=user_id, organisation_id=org_id).one_or_none()

    if existing_role:
        # User already has a role in the organization
        return existing_role

    # Create a new user_org_role record
    user_role = UserOrgRole(user_id=user_id, organisation_id=org_id, role=role, accepted=accepted)
    db.session.add(user_role)

    db.session.commit()

    return user_role


def get_unaccepted_roles(user_id: str) -> [UserOrgRole]:
    return UserOrgRole.query.filter_by(user_id=user_id, accepted=False).all()


def process_role_response(user_id: str, role_id: str, accepted: bool) -> None:
    # fixme: should we use the role_id as an input here? perhaps we should use the organisation id.
    user_role = UserOrgRole.query.filter_by(user_id=user_id, id=role_id)
    if user_role.accepted:
        return

    if accepted:
        user_role.accepted = True
        db.session.add(user_role)

    else:
        db.session.delete(user_role)

    db.session.commit()


def create_new_org(user_id: str, org_name: str, org_domain: str, _id: str = None) -> Organisation or None:
    user = User.query.filter_by(id=user_id).one_or_none()
    if not user:
        raise UnknownUserError(user_id)

    # Check how many roles the User has where they are 'owner'
    active_ownerships = UserOrgRole.query.filter_by(user_id=user_id, role='owner').count()

    # If that role count exceeds the maximum, raise exception.
    if active_ownerships >= ORG_SETTINGS['MAX_ORGANISATIONS']:
        raise OrganisationLimitExceeded(ORG_SETTINGS['MAX_ORGANISATIONS'])

    # Check if there is another Organisation using this domain
    if Organisation.query.filter_by(org_website=org_domain).one_or_none():
        raise OrganisationAlreadyExists(org_domain)

    data = dict(org_name=org_name, org_website=org_domain)
    if _id:
        data['id'] = _id

    # Creating a new organisation
    new_organisation = Organisation(**data)
    db.session.add(new_organisation)
    db.session.commit()

    # Create a Wallet for the Organisation
    wallet = Wallet(organisation_id=new_organisation.id)
    db.session.add(wallet)
    db.session.commit()

    # Adding the owner role to the User
    add_user_to_org(new_organisation.id, user_id, 'owner', accepted=True)

    return new_organisation


def delete_org(org_id: str):
    org = Organisation.query.filter_by(id=org_id).one_or_none()
    if not org:
        raise OrganisationDoesntExist

    org.delete()
    db.session.commit()


def has_campaign_id(org_id: str, campaign_id: str) -> Campaign or None:
    campaign = Campaign.query.filter_by(id=campaign_id, organisation_id=org_id).one_or_none()
    if not campaign:
        return False
    return campaign


def get_campaign(org_id: str, campaign_id: str) -> Campaign or None:
    campaign = Campaign.query.filter_by(id=campaign_id, organisation_id=org_id).one_or_none()
    return campaign


def has_audience_id(org_id: str, audience_id: str) -> bool:
    org = Organisation.query.filter_by(id=org_id).one_or_none()
    if not org:
        return False
    return any(audience.id == audience_id for audience in org.audiences)


def require_role(required_roles: [str]):
    """
    Decorator that checks organisation_id from request JSON.
    :param required_roles: List of roles. Empty means any role. ORG_SETTINGS has role groups for viewing, admin, etc.
    :return: The decorated function
    """

    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            org_id = ''
            if current_user.is_superadmin:
                return f(*args, **kwargs)

            # check view args
            if not org_id and 'organisation_id' in request.view_args:
                org_id = request.view_args['organisation_id']

            # check query params
            if not org_id and 'organisation_id' in request.args:
                org_id = request.args['organisation_id']

            # check URL parameters
            if not org_id and 'organisation_id' in kwargs:
                org_id = kwargs['organisation_id']

            # check request JSON
            if not org_id:
                if request.json and 'organisation_id' in request.json:
                    org_id = request.json['organisation_id']

            # return error response if org ID not found
            if not org_id:
                return jsonify({'message': 'Organization ID not found within request.'}), 400

            user_role = UserOrgRole.query.filter_by(user_id=current_user.id, organisation_id=org_id).one_or_none()
            if not user_role:
                return jsonify({'message': 'You do not have the required role to perform this action.'}), 401

            # to accept view-only
            if user_role and not required_roles:
                if user_role.accepted:
                    return f()

            if user_role.role not in required_roles:
                return jsonify({'message': 'You do not have the required role to perform this action.'}), 401

            return f(*args, **kwargs)

        return decorated_function

    return decorator


def require_package(required_package: Literal['pro', 'essential']):
    """
    Decorator that checks the Organisation package
    :param required_package: A package name
    :return: The decorated function
    """

    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            org_id = ''
            if current_user.is_superadmin:
                return f(*args, **kwargs)

            # check view args
            if not org_id and 'organisation_id' in request.view_args:
                org_id = request.view_args['organisation_id']

            # check query params
            if not org_id and 'organisation_id' in request.args:
                org_id = request.args['organisation_id']

            # check URL parameters
            if not org_id and 'organisation_id' in kwargs:
                org_id = kwargs['organisation_id']

            # check request JSON
            if not org_id:
                if request.json and 'organisation_id' in request.json:
                    org_id = request.json['organisation_id']

            # return error response if org ID not found
            if not org_id:
                return jsonify({'message': 'Organization ID not found within request.'}), 400

            org = Organisation.query.filter_by(organisation_id=org_id)
            if not org:
                return jsonify({'message': 'Organization not found.'}), 404

            current_package = org.current_package
            if not current_package:
                return jsonify({'message': 'No package found for this organisation.'}), 401

            if current_package.name != required_package:
                return jsonify({'message': f'You need a {required_package} package to perform this action.'}), 401

            return f(*args, **kwargs)

        return decorated_function

    return decorator


def require_super_user():
    """
    Decorator that checks if the user is a superadmin
    """

    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if current_user.is_superadmin:
                return f(*args, **kwargs)
            else:
                return jsonify({'message': 'You do not have the required role to perform this action.'}), 401

        return decorated_function

    return decorator


if __name__ == '__main__':
    from app import app
    from user import register_user, generate_password
    from src.models import DataPoint
    from pprint import pprint
    with app.app_context():
        u = User.query.filter_by(email='<EMAIL>').first()
        pwd = generate_password()
        u.password = pwd
        print(pwd)
        db.session.add(u)
        db.session.commit()
