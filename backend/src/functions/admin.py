from sqlalchemy import and_

from src.models import Organisation, Subscription, CreditPackage, EmailSender
from src.utils import mail_database
from datetime import datetime


SENDER_COST = 100
CREDIT_COST = 1


def get_active_organisations(start: datetime, end: datetime) -> [Organisation]:
    # Get all active organisations where their Subscription is not expired
    return (Organisation.query.join(Subscription).
            filter(and_(start >= Subscription.starts_at, end < Subscription.ends_at)).all())


def count_senders(organisation: Organisation) -> int:
    # Get the number of senders for an organisation
    return len(EmailSender.query.filter_by(organisation_id=organisation.id).all())


def get_credit_usage(organisation: Organisation, start: datetime, end: datetime) -> int:
    org_id = str(organisation.id)
    return mail_database[org_id].get_credit_usage(start, end)


def get_credit_packages(organisation: Organisation, start: datetime, end: datetime) -> [dict]:
    org_id = str(organisation.id)
    return CreditPackage.query.filter(CreditPackage.organisation_id == org_id
                                      and start < CreditPackage.created_at < end).all()


def get_billing_info(start: datetime, end: datetime) -> [dict]:
    records = []
    for organisation in get_active_organisations(start, end):
        senders = count_senders(organisation)
        billable_senders = max(senders - organisation.current_subscription.senders_included, 0)
        credit_usage = get_credit_usage(organisation, start, end)

        # This is for future invoicing, we will manually bill and add the credit packages in the meantime instead.
        # credit_packages = get_credit_packages(organisation, start, end)

        records.append({
            'id': str(organisation.id),
            'name': organisation.org_name,
            'billable_senders': billable_senders,
            'credit_usage': credit_usage,
            'total_cost': (billable_senders * SENDER_COST) + (credit_usage * CREDIT_COST)
        })

    return records
