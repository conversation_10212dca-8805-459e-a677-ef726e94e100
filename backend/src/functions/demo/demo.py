from copy import deepcopy
from datetime import datetime, timedelta

from Holmes.audience.prospect import generate_random_prospect
from <PERSON>.email.email_message import EmailMessage
from Holmes.email.events import Delivered, Opened, Clicked
from Holmes.email.inbox import Outreach, Response
from Holmes.email.record import Record
from Holmes.inbox import Notification

from app import app
from src.utils import spaces_client, prospects_database, mail_database, inbox_manager
from src.functions.org import create_new_org
from src.models import Organisation, Audience, db, Campaign
from src.functions.demo.content import email


def create_demo_audiences(org_id: str, user_id: str) -> [Audience]:
    audiences = []
    for i in range(1, 6):
        audience = Audience(name=f'Demo Audience {i}', organisation_id=org_id, created_by=user_id)
        db.session.add(audience)
        audiences.append(audience)
    db.session.commit()
    return audiences


def create_demo_campaigns(audiences: [Audience], user_id: str) -> [Campaign]:
    campaigns = []
    for audience in audiences:
        campaign = Campaign(user_id, audience.organisation_id, f'Demo Campaign {audience.id}', audience_id=audience.id)
        db.session.add(campaign)
        campaigns.append(campaign)
    db.session.commit()
    return campaigns


def create_demo_content(organisation_id: str) -> None:
    bucket = spaces_client.create_organisation_bucket(organisation_id)
    for i in range(1, 6):
        email_data = email.to_dict()
        email_data['name'] = f"Demo mail {i}"
        email_data.pop('id')
        new_email = EmailMessage.from_json(email_data)
        bucket.upload_email(new_email, new_email.id)


def create_demo_prospects(audiences: [Audience], campaigns: [Campaign]) -> None:
    p_shard = prospects_database[str(audiences[0].organisation_id)]
    m_shard = mail_database[str(audiences[0].organisation_id)]
    for index, (audience, campaign) in enumerate(zip(audiences, campaigns)):
        prospects = []
        for i in range(1, 5001):
            print(index, i)
            random_prospect = generate_random_prospect()
            random_prospect.audience.id = str(audience.id)
            random_prospect.audience.name = audience.name

            prospects.append(random_prospect)
            m_shard.add_record(Record.from_prospect(random_prospect, str(campaign.id), '', ''), block_list=False)

        p_shard.add_prospects(prospects)


def create_demo_analytics(organisation_id: str) -> None:
    shard = mail_database[organisation_id]
    inbox = inbox_manager[organisation_id]
    for date_time in [datetime.now() - timedelta(days=i) for i in range(1, 91)]:
        for i in range(50):
            i = str(i)
            random_prospect = shard.client.find_one({'outreaches.0': {'$exists': False}})
            name = random_prospect['data']['name']
            outreach = Outreach(i, i, i, f"Hey {name}, hoe maak je het?",
                                html=f"Hey {name}, hoe maak je het?<br> Dit is een testbericht!<br>Groetjes, Bram.",
                                subject=f"Testbericht voor {name}", sender_name='Bram',
                                sender_email='<EMAIL>', timestamp=date_time)
            response = Response(f"RE: Testbericht voor {name}", random_prospect['email'], name,
                                i, i, False, False, False, ['book a meeting'], "Hey Bram, alles wel hier...",
                                html=f"Hey Bram, alles wel hier, bedankt voor het vragen! <br> Heb je volgende week tijd voor een meeting? <br> Groetjes, {name}",
                                outreach_id=i, timestamp=date_time, processed=True)
            inbox.add_notification(Notification.from_response(response, random_prospect['campaign_id']))
            events = [
                Delivered(timestamp=date_time + timedelta(minutes=5), outreach_id=i),
                Opened(timestamp=date_time + timedelta(minutes=30), outreach_id=i),
                Clicked(timestamp=date_time + timedelta(minutes=30), outreach_id=i),
            ]

            shard.update_record(outreach, email=random_prospect['email'])
            shard.update_record(response, email=random_prospect['email'])
            [shard.update_record(event, email=random_prospect['email']) for event in events]
            print(date_time, i)


def create_demo_org(organisation_name: str, user_id: str) -> Organisation:
    with app.app_context():
        org_slug = organisation_name.replace(' ', '_').lower()
        try:
            org = create_new_org(user_id, organisation_name, f'{org_slug}.whatson.ai')
        except:
            org = Organisation.query.filter_by(org_website=f'{org_slug}.whatson.ai').one_or_none()
        audiences = create_demo_audiences(str(org.id), user_id)
        campaigns = create_demo_campaigns(audiences, user_id)
        create_demo_content(str(org.id))
        create_demo_prospects(audiences, campaigns)
        create_demo_analytics(str(org.id))

        return org


if __name__ == '__main__':
    create_demo_org('Demo @ Sprints&Sneakers 2', 'acbe2027-3617-499a-afd5-3487339f6dd2')