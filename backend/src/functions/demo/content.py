from Holmes.email.email_message import EmailMessage

email = EmailMessage.from_json({
    "name": "Demo email 1",
    "subject": "Ik heb iets voor je,&nbsp;[[mergefield:81b2d904-94b0-416f-ace8-eea0bc129ea1]]",
    "html": "<p dir=\"ltr\"><span>Hey </span>[[mergefield:81b2d904-94b0-416f-ace8-eea0bc129ea1]]<span>,</span></p><p dir=\"ltr\"><span><PERSON><PERSON> ben <PERSON>, een effectieve sales AI die outreach automatiseert. Hopelijk laat ik je niet schrikken. </span></p><p dir=\"ltr\">[[mergefield:f864cccb-977d-4532-aed1-c4a8eb2e50ca]]<span> kwam mij voorbij en er waren een aantal pijlers die mij opvielen. De reden dat ik je dit bericht stuur is omdat ik zag dat jullie bedrijf dit jaar met </span>[[mergefield:new_employee_count]]<span> FTE gegroeid is naar </span>[[mergefield:60191c4f-f17e-4084-868b-0aea924a6d4b]]<span> personeelsleden. Omdat jullie in de </span>[[mergefield:eda67d45-fdef-44e8-ac5a-acad154c5949]]<span>werken, en een hoop diverse sales collega's in dienst hebben, zullen die waarschijnlijk veel handwerk verrichten.</span></p><p><br></p><p dir=\"ltr\"><span>Ik zag dat jij de functie </span>[[mergefield:eddad41f-0518-41c1-9a0e-eaf45062d7f5]]<span> hebt bij </span>[[mergefield:f864cccb-977d-4532-aed1-c4a8eb2e50ca]]<span>, en als </span>[[mergefield:6f8d494c-01f0-4f56-81d6-06aee124bc19]]<span> binnen de </span>[[mergefield:e6a14fca-6cec-4bea-a321-f7ed3d99b830]]<span> denk ik dat jij beslissingen kan maken over sales automatisering. Aan het aantal jaren werkervaring verwacht ik dat jij de impact van effectieve outreach begrijpt. </span></p><p dir=\"ltr\"><span>Wat is effectieve outreach? Nou.. deze mail is geautomatiseerd. Sterker nog. Het opzetten van deze campagne duurde slechts 30 minuten.</span></p><p><br></p><p dir=\"ltr\"><span>Ik weet veel. Over jou en over je doelgroep. Met die kennis probeer ik op een prettige manier de juiste persoon, met de juiste boodschap, op het juiste moment te benaderen.</span></p><p dir=\"ltr\"><span>Mocht je het interessant vinden om te zien wat dit voor jou kan betekenen, reageer dan gerust.</span></p><p dir=\"ltr\"><span>Mijn collega zal eerdaags telefonisch contact met je opnemen.</span></p><p><span>--</span></p><p dir=\"ltr\"><span>Met vriendelijke groet,</span></p><p dir=\"ltr\"><span>Whatson</span></p><p dir=\"ltr\"><br></p><p dir=\"ltr\"><br></p><p dir=\"ltr\"><span>Ik bereik nu 100 mensen zoals jij per dag. Hiervan reageert 15%. Dus… 15 leads per dag! We hebben het druk ;-)</span></p>",
    "merge_fields": [
        {
            "id": "81b2d904-94b0-416f-ace8-eea0bc129ea1",
            "field_type": "text",
            "fallback": "",
            "slug_name": "person.name",
            "visual_name": "First Name"
        },
        {
            "id": "f864cccb-977d-4532-aed1-c4a8eb2e50ca",
            "field_type": "text",
            "fallback": "",
            "slug_name": "company.name",
            "visual_name": "Company name"
        },
        {
            "field_type": "text",
            "fallback": "New employee count (Legacy)",
            "slug_name": "new_employee_count",
            "visual_name": "New employee count"
        },
        {
            "id": "60191c4f-f17e-4084-868b-0aea924a6d4b",
            "field_type": "text",
            "fallback": "",
            "slug_name": "company.employee_count",
            "visual_name": "Employee count"
        },
        {
            "id": "eda67d45-fdef-44e8-ac5a-acad154c5949",
            "field_type": "text",
            "fallback": "",
            "slug_name": "company.industry",
            "visual_name": "Company main industry"
        },
        {
            "id": "eddad41f-0518-41c1-9a0e-eaf45062d7f5",
            "field_type": "text",
            "fallback": "",
            "slug_name": "person.job_title",
            "visual_name": "Job description"
        },
        {
            "id": "b2022095-a843-4925-baf3-d23fc44a626a",
            "field_type": "text",
            "fallback": "",
            "slug_name": "company.name",
            "visual_name": "Company name"
        },
        {
            "id": "6f8d494c-01f0-4f56-81d6-06aee124bc19",
            "field_type": "text",
            "fallback": "",
            "slug_name": "person.job_level",
            "visual_name": "Job level"
        },
        {
            "id": "e6a14fca-6cec-4bea-a321-f7ed3d99b830",
            "field_type": "text",
            "fallback": "",
            "slug_name": "person.job_industry",
            "visual_name": "Job industry"
        },
        {
            "id": "64c76915-27d7-4a87-992f-a3e4e4d6eb92",
            "field_type": "text",
            "fallback": "",
            "slug_name": "person.name",
            "visual_name": "First Name"
        }
    ],
    "merge_field_count": 10,
    "preheader": "Ik heb iets voor je,&nbsp;[[mergefield:81b2d904-94b0-416f-ace8-eea0bc129ea1]]"
})

