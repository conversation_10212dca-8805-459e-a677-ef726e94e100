from datetime import datetime, timedelta
from typing import List, Dict, Optional

from Holmes.campaign.flow import Flow
from src.models import Campaign, db, CampaignSettings


def create_campaign(organization_id: str, user_id: str, name: str, flow: Flow = '', settings: dict = None) -> Campaign:
    campaign = Campaign(user_id=user_id, organisation_id=organization_id, flow=flow, name=name)
    db.session.add(campaign)
    db.session.commit()

    if not settings:
        settings = {'advanced': {'sending_window': {i: ['09:00', '17:30'] for i in range(1, 6)}}}
    update_campaign_advanced_settings(campaign.id, settings)

    db.session.expire(campaign)
    return campaign


def get_campaigns(organization_id: str, paused: bool = None, published: bool = None,
                  archived: bool = None) -> [dict]:
    filters = {'organisation_id': organization_id}
    if paused is not None:
        filters['paused'] = paused

    if published is not None:
        filters['published'] = published

    if archived is not None:
        filters['archived'] = archived

    campaigns = Campaign.query.filter_by(**filters).all()
    return [campaign.to_dict() for campaign in campaigns]


def get_campaign(campaign_id: str) -> Campaign or None:
    campaign = Campaign.query.get(campaign_id)

    if not campaign:
        return None

    return campaign.to_dict()


def update_campaign_audience(campaign_id: str, audience_id: str = None):
    campaign = Campaign.query.filter_by(id=campaign_id).one_or_none()

    if not campaign:
        return None

    if audience_id:
        campaign.audience_id = audience_id

    return campaign.to_dict()


def delete_campaign(campaign_id: str, organisation_id: str, user_id: str = None):
    campaign = Campaign.query.filter_by(id=campaign_id, organisation_id=organisation_id).one_or_none()

    if not campaign:
        return False

    if user_id:
        if campaign.user_id != user_id:
            return False

    db.session.delete(campaign)
    db.session.commit()

    return True


def validate_sending_window(sending_window: Dict[int, Optional[List[str]]]) -> (bool, str):
    for day in sending_window:
        # Disallow saturday and sunday
        try:
            day_int = int(day)
        except (ValueError, TypeError):
            return False, f'Sending window contained {day} which was not a valid integer'

        if day_int not in range(1, 6):
            return False, 'Sending window contained a key which was not 1 - 5'

        # Disallow empty lists or lists with more than 2 items
        if sending_window[day] and len(sending_window[day]) != 2:
            return False, f'Sending window contained {sending_window[day]} which was not a list of 2 time strings'

        if sending_window[day]:
            for time in sending_window[day]:
                # Disallow empty strings or wrong types
                if not isinstance(time, str) or not time:
                    return False, f'Sending window contained {time} which was not a string or empty'

                # Disallow times before 7am or after 6pm, and badly formatted times
                try:
                    time_hours, time_minutes = time.split(':')
                    time_hours = int(time_hours)
                    time_minutes = int(time_minutes)
                    if time_hours < 7:
                        return False, f'Sending window contained {time}, which was before 7am'
                    elif time_hours > 18:
                        return False, f'Sending window contained {time}, which was after 6pm'

                    if time_minutes < 0 or time_minutes > 59:
                        return False, f'Sending window contained {time}, which had minutes outside of 0 - 59'

                except (ValueError, TypeError):
                    return False, f'Sending window contained {time} which was not a valid time string'

    return True, ''


def update_campaign_advanced_settings(campaign_id: str, settings: dict, published: bool = False) -> (str, int):
    campaign_settings = CampaignSettings.query.filter_by(campaign_id=campaign_id).one_or_none()
    if not campaign_settings:
        campaign_settings = CampaignSettings(campaign_id=campaign_id)

    if settings.get('start_date') and settings.get('end_date'):
        if settings['start_date'] > settings['end_date']:
            return 'Campaign cannot end before it starts', 400

    for key, value in settings.items():

        try:
            if key.endswith('_date') and value:
                if published:
                    continue

                value = datetime.fromisoformat(value)

                if value < datetime.today() - timedelta(days=1):
                    if key.startswith('start'):
                        return 'Campaign cannot start in the past', 400
                    else:
                        return 'Campaign cannot end in the past', 400

            if key in ['id', 'campaign_id', 'created_at', 'updated_at']:
                continue

            if key == 'sending_window':
                success, reason = validate_sending_window(value)
                if not success:
                    return reason, 400

                setattr(campaign_settings, key, value)
                continue

            if key != 'budget':
                setattr(campaign_settings, key, value)
            elif value is not None:
                setattr(campaign_settings, 'budget', value)

        except AttributeError:
            # this means someone is trying to set a setting that doesn't exist
            return 'Something went wrong, please try again later.', 500

    db.session.add(campaign_settings)
    db.session.commit()
    return 'Settings saved', 200



if __name__ == '__main__':
    print(validate_sending_window({
        "1": None,
        "2": None,
        "3": [
            "09:00",
            "17:00"
        ],
        "4": None,
        "5": None
    }))