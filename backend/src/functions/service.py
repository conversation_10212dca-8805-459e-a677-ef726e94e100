from flask import request, jsonify
from functools import wraps
from werkzeug.security import check_password_hash
from src.models import BackendService, db


def service_required():
    """
    Decorator that checks service_key from request.
    :return: The decorated function/endpoint
    """

    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                api_key = request.headers['X-API-KEY']
            except KeyError:
                return jsonify({'message': 'Please supply an API key.'}), 400

            # We will only have a few services, hence this solution.
            services = BackendService.query.all()
            for service in services:
                if check_password_hash(service.api_key, api_key):
                    return f(*args, **kwargs)

            return jsonify({'message': 'Invalid API key.'}), 401

        return decorated_function

    return decorator


def create_service_account(name: str) -> str:
    """
    Creates a new Service account and adds it to the database.
    :param name: Name of the service.
    :return: The API key of the service.
    """

    # Checking if service with name already exists.
    existing = BackendService.query.filter_by(name=name).one_or_none()
    if existing:
        raise ValueError(f'Service with name {name} already exists.')

    # Creating new service.
    new_service = BackendService(name=name)

    # The API key is generated and hashed on __init__, hence we regenerate it and store it.
    api_key = new_service.generate_new_api_key()
    db.session.add(new_service)
    db.session.commit()

    # Returning the API key.
    return api_key


def delete_service_account(name: str) -> bool:
    """
    Deletes a service account from the database.
    :param name: Name of the service.
    :return: True if successful, False if not.
    """
    service = BackendService.query.filter_by(name=name).one_or_none()
    if not service:
        return False

    db.session.delete(service)
    db.session.commit()
    return True


if __name__ == '__main__':
    from app import app
    with app.app_context():
        delete_service_account('Moriarty')
        print(create_service_account('Moriarty'))
