from Holmes.sources.postgres.filter import <PERSON>end<PERSON>ilt<PERSON>, FrontendFilterFactory, ExactMatchFilter, MultiSelectFilter
from flask import jsonify
from src.models import Audience, db, PrefabFilter, DataPointTranslation
import spacy


nlp = spacy.load('nl_core_news_sm')


class DuplicateAudienceName(Exception):
    def __init__(self, audience_name: str):
        self.audience_name = audience_name

    def __str__(self):
        return f'An audience by the name {self.audience_name} already exists.'


def create_new_audience(org_id: str, user_id: str, filters: [FrontendFilter], name: str, limit: int,
                        correct_function_only: bool = False, personal_email_only: bool = False) -> Audience or None:
    """
    Create an audience.
    :param org_id: Organisation UUID4
    :param user_id: User UUID4
    :param filters: List of FrontendFilter objects
    :param name: Name of audience
    :param limit: Hard limit of prospects generated
    :return: Audience object
    """
    args = dict(organisation_id=org_id, created_by=user_id, filters=[f.to_dict() for f in filters], name=name)

    if limit:
        args['limit'] = limit
        if isinstance(limit, str) and limit.isdigit() and int(limit) > 5000:
            raise ValueError('Limit cannot exceed 5000')

    if correct_function_only:
        args['correct_function_only'] = True

    if personal_email_only:
        args['personal_email_only'] = True

    new_audience = Audience(**args)
    db.session.add(new_audience)
    db.session.commit()
    return new_audience


def get_audiences(org_id: str) -> [Audience]:
    """
    Retrieves audiences based on Organisation.
    :param org_id: Organisation UUID4
    :return: List of Audience objects
    """
    audiences = Audience.query.filter_by(organisation_id=org_id).all()
    return audiences


def delete_existing_audience(audience_id: str, user_id: str = None) -> (str, int):
    """
    Attempts to delete an Audience.
    :param audience_id: Audience UUID4
    :param user_id: If supplied, function will check if the User made this Organisation, and return 401 if not.
    :return: None
    """
    audience = Audience.query.filter_by(id=audience_id).one_or_none()
    if audience is None:
        return jsonify({'message': 'Audience not found.'}), 404

    if user_id:
        if user_id != audience.created_by:
            return jsonify({'message': 'You are not authorised to perform this action.'}), 401

    db.session.delete(audience)
    db.session.commit()
    return jsonify({'message': 'Audience successfully deleted'}), 200


def normalize_words_input(input: str) -> str:
    """
    Normalizes the input of the words filter to a lemma.
    :param input: Input string
    :return: Lemmatized and stripped string
    """
    nlpd = nlp(input)
    output = []
    for token in nlpd:
        if token.is_alpha and not token.is_stop and token.pos_ in ['PROPN', 'NOUN', 'VERB']:
            return token.lemma_.strip()
        elif token.is_alpha and not token.is_stop:
            output.append(token.lemma_.strip())

    for ent in nlpd.ents:
        if ent.label_ in ['ORG', 'PERSON', 'MONEY', 'PRODUCT'] and ent.text not in output:
            output.append(ent.text)

    return ' '.join(output)


def handle_filter_translations(f: FrontendFilter) -> FrontendFilter:
    # If there is no PrefabFilter ID, we cannot get the Field
    if not f.id:
        return f

    # By performing a few subqueries, we get the DataPointTranslation objects from the Filter ID
    translations = db.session.query(DataPointTranslation).join(
        PrefabFilter, PrefabFilter.data_point_id == DataPointTranslation.datapoint_id
    ).filter(PrefabFilter.id == f.id).all()
    # If there are no translations, we simply continue
    if not translations:
        return f

    # We check if the input matches any translation values, if so, we replace it with the translated value.
    if isinstance(f.input, str):
        for t in translations:
            if t.value.lower() == f.input.lower():
                f.input = t.translation

    # Here we do the same as in the IF-clause above, except for every item in the input list.
    elif isinstance(f.input, list):
        new_input = []
        for t in translations:
            for filter_input in f.input:
                if filter_input.lower() == t.value.lower():
                    new_input.append(t.translation)

        f.input = new_input

    return f


def parse_filters(raw_filters: list[dict]):
    filters = []
    for rf in raw_filters:
        if rf.get('id'):
            prefab = PrefabFilter.query.filter_by(id=rf['id']).one_or_none()
            if not prefab:
                raise ValueError('Prefab could not be found')
            _filter = prefab.to_frontend()
        else:
            _filter = FrontendFilterFactory.from_dict(rf)

        # setting the input value, differs per filter.
        [setattr(_filter, k, v) for k, v in rf.items() if 'input' in k or k in ['exclude', 'radius']]

        # validate unedited inputs
        try:
            result, reason = _filter.validate()
        except ValueError as VE:
            raise ValueError(f'[{_filter.target_type}.{_filter.target_field} {rf["type"]}: {VE}')
        if not result:
            raise ValueError(reason)

        # handling possible translations
        _filter = handle_filter_translations(_filter)

        # in case of words, we want to lemmatize.
        if _filter.target_field == 'words':
            _filter.input = normalize_words_input(_filter.input)

        filters.append(_filter)

    return filters

