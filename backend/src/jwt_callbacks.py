from flask_jwt_extended.jwt_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>
from flask import jsonify
from sqlalchemy.orm import joinedload
from src.models import User
from src.utils import redis_manager

jwt_manager = JWTManager()


@jwt_manager.expired_token_loader
def expired_token_callback(_jwt_header, jwt_data):
    return jsonify({
        'status': 401,
        'sub_status': 101,
        'msg': 'Your token has expired'
    }), 401


@jwt_manager.user_identity_loader
def lookup_user(user) -> dict:
    return {
        'id': user.id,
        'email': user.email,
        'first_name': user.first_name,
        'last_name': user.last_name
    }


@jwt_manager.user_lookup_loader
def user_lookup_callback(_jwt_header, jwt_data) -> User:
    identity = jwt_data["sub"]
    user_id = identity['id']
    return User.query.options(joinedload(User.roles)).filter_by(id=user_id).one_or_none()


@jwt_manager.token_in_blocklist_loader
def check_if_token_revoked(jwt_header, jwt_payload: dict) -> bool:
    jti = jwt_payload['jti']
    result = redis_manager.get(jti)

    return result is not None
