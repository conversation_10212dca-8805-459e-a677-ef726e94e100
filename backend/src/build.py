import logging
from os import environ

from flask import Flask
from src.routes.user_routes import bp as user_bp
from src.routes.admin.base import bp as admin_bp
from src.routes.organisation.base import bp as org_bp
from src.routes.organisation.inbox import bp as org_inbox_bp
from src.routes.campaign.record import bp as campaign_record_bp
from src.routes.campaign.base import bp as campaign_bp
from src.routes.organisation.content import bp as content_bp
from src.routes.analytics.base import bp as analytics_bp
from src.routes.audience.base import bp as audience_routes
from src.routes.health_routes import bp as health_routes
from src.routes.audience.explore import bp as explore_bp
from src.routes.service.routes import bp as service_bp
from src.routes.organisation.blocklist import bp as blocklist_bp
from src.routes.organisation.leads import bp as leads_routes
from src.models import db
from src.jwt_callbacks import jwt_manager
from src.utils import CustomJSON, ObjectIdConverter


def create_app(config=None):
    app = Flask(__name__)
    if config is not None:
        if isinstance(config, dict):
            app.config.update(config)
        elif config.endswith('.py'):
            app.config.from_pyfile(config)

    # todo: json_encoder will be deprecated in future versions of flask, use app.json instead
    customjson = CustomJSON(app)
    app.json = customjson

    app.url_map.converters['objectid'] = ObjectIdConverter

    jwt_manager.init_app(app)
    setup_app(app)

    return app


def setup_app(app: Flask):
    db.init_app(app)

    # this database is only used for testing, please don't use it for anything else
    # whatson_dev should not be dropped, instead create an Alembic revision and test it in whatson_dev.
    if environ['DB_NAME'] == 'whatson_test':
        with app.app_context():
            print("Dropping and recreating test database")
            db.drop_all()
            db.create_all()

    # testing and staging environments
    if environ['DB_NAME'] != 'whatson':
        app.logger.setLevel(logging.DEBUG)
        logging.basicConfig(level=logging.DEBUG)

    app.register_blueprint(user_bp)
    app.register_blueprint(admin_bp)
    app.register_blueprint(org_bp)
    app.register_blueprint(org_inbox_bp)
    app.register_blueprint(campaign_record_bp)
    app.register_blueprint(campaign_bp)
    app.register_blueprint(content_bp)
    app.register_blueprint(service_bp)
    app.register_blueprint(analytics_bp)
    app.register_blueprint(explore_bp)
    app.register_blueprint(blocklist_bp)
    app.register_blueprint(leads_routes)
    app.register_blueprint(audience_routes)
    app.register_blueprint(health_routes)
