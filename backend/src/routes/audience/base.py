from Holmes.email.io.analyticsmanager import Analytics<PERSON>anager
from src.models import Audience, db
from flask import Blueprint
from flask import jsonify, request
from flask_jwt_extended import jwt_required, get_current_user
from src.functions.org import require_role
from settings import ORG_SETTINGS
from src.functions.aud import create_new_audience, get_audiences, delete_existing_audience, parse_filters
from src.utils import user_cors_args, mail_database, prospects_database
from flask_cors import CORS
import requests
from os import environ

bp = Blueprint('audience', __name__, url_prefix='/audience')
CORS(bp, **user_cors_args, supports_credentials=True)


def start_audience_generation(audience_id: str) -> bool:
    result = requests.post(f"{environ['MORIARTY_URL']}/scraping/start/{audience_id}",
                           headers={'Authorization': f'Bearer {environ["MORIARTY_API_KEY"]}'})
    return result.status_code == 200


@bp.route('/', methods=['GET'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def list_audiences():
    org_id = request.args.get('organisation_id')
    audience_id = request.args.get('audience_id')

    # If an Audience ID is supplied, we only get data for that audience.
    if not audience_id:
        audiences = get_audiences(org_id)
    else:
        audience = Audience.query.filter_by(organisation_id=org_id, id=audience_id).one_or_none()
        if not audience:
            return jsonify({'message': 'Audience not found.'}), 404

        audiences = [audience]

    # Getting the IDs, and running a Pipeline in MongoDB.
    audience_ids = [str(aud.id) for aud in audiences]
    audience_analytics = AnalyticsManager.get_audience_insights(mail_database[org_id], audience_ids)

    # Transforming the data for the frontend
    audience_data = []
    for audience in audiences:
        used_in_campaigns = [{'name': campaign.name, 'id': campaign.id} for campaign in audience.campaigns]
        audience_json = audience.json()

        # todo: BANDAID, REMOVE ME PLEASE
        audience_json.update({'used_in_campaigns': used_in_campaigns})
        this_analytics = audience_analytics.get(str(audience.id), {})
        if not this_analytics:
            available = prospects_database[org_id].col.count_documents({
                'audience.id': str(audience.id),
            })
            this_analytics['available_prospects'] = f"{available} of {available}"

        audience_json.update(this_analytics)
        audience_data.append(audience_json)

    if not audience_id:
        return jsonify(audience_data)
    else:
        return jsonify(audience_data[0])


@bp.route('/', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['EDITORS'])
def create_audience():
    user = get_current_user()
    org_id = request.json.get('organisation_id')
    filters = request.json.get('filters')
    name = request.json.get('name')
    limit = request.json.get('limit')
    generate = request.json.get('generate', False)
    correct_function_only = request.json.get('correct_function_only', False)
    personal_email_only = request.json.get('personal_email_only', False)

    can_generate = generate
    if generate:
        # If we don't disable autoflush we run into filter serialization issues.
        with db.session.no_autoflush:
            generating_audience = db.session.query(Audience).filter_by(
                organisation_id=org_id, status='generating').all()

            if generating_audience:
                can_generate = False

    try:
        parsed_filters = parse_filters(filters)
        result = create_new_audience(org_id, user.id, parsed_filters, name, limit,
                                     correct_function_only, personal_email_only)
        audience_id = str(result.id)
    except ValueError as exc:
        return jsonify({'message': f'{exc}'}), 400

    # If we're not generating, we return the Audience object.
    if not generate:
        return jsonify(result.json())

    # If the user wants to generate, we try to start the generation process.
    if not can_generate:
        return jsonify(result.json()), 429
    else:
        generation_result = start_audience_generation(audience_id)
        if not generation_result:
            return jsonify(result.json()), 400

    # If we're successful, we update the Audience status.
    with db.session.no_autoflush:
        db.session.query(Audience).filter(Audience.id == audience_id).update({'status': 'generating'})
        db.session.commit()

    return jsonify(result.json())


@bp.route('/', methods=['DELETE'])
@jwt_required()
@require_role(ORG_SETTINGS['EDITORS'])
def delete_audience():
    user = get_current_user()
    org_id = request.args.get('organisation_id')
    aud_id = request.args.get('audience_id')

    # If they are Admin or Owner, they can remove Audience objects as they please.
    if user.get_role(org_id).role in ['admin', 'owner']:
        return delete_existing_audience(aud_id)

    # If not, only the creator of the Audience can delete the object. Hence why the User.id is passed.
    return delete_existing_audience(aud_id, user_id=user.id)


@bp.route('/<audience_id>/generate', methods=['GET'])
@jwt_required()
@require_role(ORG_SETTINGS['EDITORS'])
def generate_audience(audience_id: str):
    audience = Audience.query.filter_by(id=audience_id).one_or_none()
    if not audience:
        return jsonify({'message': 'Audience not found.'}), 404

    with db.session.no_autoflush:
        generating_audience = db.session.query(Audience).filter_by(
            organisation_id=audience.organisation_id, status='generating').all()

        if generating_audience:
            return jsonify({'message': 'There is already an Audience generating.'}), 429

    generation_result = start_audience_generation(audience_id)

    if not generation_result:
        return jsonify({'message': 'Failed to start Audience generation.'}), 400

    # If we're successful, we update the Audience status.
    with db.session.no_autoflush:
        db.session.query(Audience).filter(Audience.id == audience_id).update({'status': 'generating'})
        db.session.commit()

    return jsonify({'message': 'Success!'}), 200


@bp.route('/<audience_id>/status', methods=['GET'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_audience_status(audience_id: str):
    audience = Audience.query.filter_by(id=audience_id).one_or_none()
    if not audience:
        return jsonify({'message': 'Audience not found.'}), 404

    return jsonify({'message': 'Success',
                    'status': audience.status})
