import asyncio
from io import Bytes<PERSON>
from os import environ
import requests
from flask import Blueprint, send_file
from flask import jsonify, request
from flask_jwt_extended import jwt_required, current_user

from src.functions.aud import parse_filters
from src.functions.org import require_role
from settings import ORG_SETTINGS
from src.models import Organisation, PrefabFilter, DataPoint, db
from src.utils import user_cors_args, DataPointValidator
from flask_cors import CORS
import aiohttp
import logging

bp = Blueprint('audience_explore', __name__, url_prefix='/<organisation_id>/audience/explore')
CORS(bp, **user_cors_args, supports_credentials=True)

NO_AUTOCOMPLETE_COLUMNS = ['domain.words', 'domain.internal_links', 'domain.external_links',
                           'domain.phone_number', 'domain.phone_numbers', 'company.phone_number']
BASE_URL = environ['MARYM_URL']


async def send_marym_request(session, url, json_payload):
    try:
        async with session.post(url, json=json_payload) as response:
            response.raise_for_status()  # Raises HTTPError for bad responses
            return await response.json()
    except aiohttp.ClientError as http_err:
        logging.error(f'[MARYM] HTTP error occurred: {http_err}')  # Log HTTP Errors
    except Exception as err:
        logging.error(f'[MARYM] Other error occurred: {err}')  # Log other errors
    return None


def create_explore_json_payload(filters,
                                organisation_id,
                                user_id,
                                correct_function_only: bool = False,
                                personal_email_only: bool = False,
                                prospect_search: bool = False):
    if correct_function_only and personal_email_only:
        search_type = 'correct_function'
    elif personal_email_only:
        search_type = 'explore'
    else:
        search_type = 'company'

    return {
        'pwd': environ.get('MARYM_API_KEY'),  # Get API key from environment variable
        'search_type': search_type,
        'filters': filters,
        'limit': 25,
        'organisation_id': organisation_id,
        'user_id': str(user_id),
    }


async def send_single_request(payload: dict, url: str):
    async with aiohttp.ClientSession() as session:
        return await send_marym_request(session, url, payload)


async def send_explore_count_requests(filters: list, organisation_id: str,
                                      user_id: str, correct_function_only: bool = False,
                                      personal_email_only: bool = False,
                                      prospect_search: bool = False):
    payload = create_explore_json_payload(filters, organisation_id, user_id, correct_function_only, personal_email_only,
                                          prospect_search)
    async with aiohttp.ClientSession() as session:
        tasks = [
            send_marym_request(session, BASE_URL + '/explore', payload),
            send_marym_request(session, BASE_URL + '/explore/count', payload)
        ]

        explore_result, count_result = await asyncio.gather(*tasks)
        if explore_result is None:
            raise ConnectionError("Error occurred during Explore request.")

        if count_result is None:
            raise ConnectionError("Error occurred during Count request.")

    return explore_result['data'], count_result['count']



@bp.route('/autocomplete', methods=['GET'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def suggest_autocomplete(organisation_id: str):
    if 'name' in request.args:
        name = request.args.get('name')
        _type = request.args.get('type')
        text = request.args.get('text', '').lower()

    elif 'filter_id' in request.args:
        # Finding the Datapoint ID
        dp_id = db.session.query(PrefabFilter.data_point_id).filter(PrefabFilter.id == request.args['filter_id']).scalar()

        if not dp_id:
            return jsonify({"message": 'No filter found.'}), 404

        # Getting the DataPoint
        datapoint = db.session.query(DataPoint).filter(DataPoint.id == dp_id).one_or_none()
        if not datapoint:
            return jsonify({"message": 'No datapoint found.'}), 404
        else:
            name = datapoint.slug_name
            _type = datapoint.type
            text = request.args.get('text', '')

    else:
        return jsonify({"message": 'Please supply a name+type or a filter_id.'}), 400

    if f'{_type}.{name}' in NO_AUTOCOMPLETE_COLUMNS:
        return jsonify([])

    try:
        limit = request.args.get('limit', 5, type=int)
    except ValueError:
        return jsonify({'message': 'Limit must be an integer.'}), 400
    if limit > 10:
        return jsonify({'message': 'Limit must be less than or equal to 10.'}), 400

    if not DataPoint.query.filter_by(slug_name=name, type=_type).one_or_none():
        return jsonify({"message": f'The field {_type}.{name} could not be found.'}), 404

    response = requests.get('https://seahorse-app-jw5az.ondigitalocean.app/explore/autocomplete',
                            params={
                                'pwd': environ['MARYM_API_KEY'],
                                'name': name,
                                'type': _type,
                                'limit': limit,
                                'text': text
                            })

    if not response.ok:
        return jsonify({"message": "Something went wrong, try again later."})

    return jsonify(response.json())


@bp.route('/filters', methods=['GET'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_available_filters(organisation_id: str):
    return jsonify([f.to_frontend().to_template() for f in PrefabFilter.query.all()])


@bp.route('', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def explore(organisation_id: str):
    raw_filters = request.json.get('filters')
    if not raw_filters:
        return jsonify({'message': 'No filters found.'}), 400

    correct_function_only = request.json.get('correct_function_only', False)
    personal_email_only = request.json.get('personal_email_only', False)

    try:
        filters = parse_filters(raw_filters)
    except ValueError as VE:
        return jsonify({'message': f'Malformed filter found: {str(VE)}'}), 400

    # parsing filters and getting prospects & count
    filters = [prefab.to_filter().to_dict() for prefab in filters]
    try:
        payload = create_explore_json_payload(
            filters,
            organisation_id,
            str(current_user.id),
            correct_function_only,
            personal_email_only,
        )
        prospects = asyncio.run(send_single_request(payload, BASE_URL + '/explore'))

    except ConnectionError as CE:
        return jsonify({'message': str(CE)}), 400

    return jsonify({
        'count': 0,
        **prospects
    })


@bp.route('/count', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def explore_count(organisation_id: str):
    raw_filters = request.json.get('filters')
    if not raw_filters:
        return jsonify({'message': 'No filters found.'}), 400

    correct_function_only = request.json.get('correct_function_only', False)
    personal_email_only = request.json.get('personal_email_only', False)

    try:
        filters = parse_filters(raw_filters)
    except ValueError as VE:
        return jsonify({'message': f'Malformed filter found: {str(VE)}'}), 400

    # parsing filters and getting prospects & count
    filters = [prefab.to_filter().to_dict() for prefab in filters]
    try:
        payload = create_explore_json_payload(
            filters,
            organisation_id,
            str(current_user.id),
            correct_function_only,
            personal_email_only,
        )
        count = asyncio.run(send_single_request(payload, BASE_URL + '/explore/count'))
    except ConnectionError as CE:
        return jsonify({'message': str(CE)}), 400

    return jsonify({
        'count': count,
    })


@bp.route('/testbatch', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_testbatch(organisation_id: str):
    raw_filters = request.json.get('filters')
    if not raw_filters:
        return jsonify({'message': 'No filters found.'}), 400

    correct_function_only = request.json.get('correct_function_only', False)
    personal_email_only = request.json.get('personal_email_only', False)

    try:
        filters = parse_filters(raw_filters)
    except ValueError as VE:
        return jsonify({'message': f'Malformed filter found: {str(VE)}'}), 400

    # parsing filters and getting prospects & count
    filters = [prefab.to_filter().to_dict() for prefab in filters]
    try:
        payload = create_explore_json_payload(
            filters,
            organisation_id,
            str(current_user.id),
            correct_function_only,
            personal_email_only,
        )
        response = requests.post(
            BASE_URL + '/explore/testbatch',
            json=payload
        )
        if response.ok:
            file_stream = BytesIO(response.content)
            return send_file(file_stream, as_attachment=True, download_name='Testbatch.csv')
        else:
            return jsonify({'message': 'Error occurred during test batch generation.',
                            'error': response.text}), 400

    except ConnectionError as CE:
        return jsonify({'message': str(CE)}), 400
