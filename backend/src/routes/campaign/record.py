from flask import Blueprint
from flask import jsonify, request
from flask_jwt_extended import jwt_required
from src.functions.org import require_role, has_campaign_id
from settings import ORG_SETTINGS
from src.utils import mail_database, inbox_manager
from src.utils import user_cors_args
from flask_cors import CORS

bp = Blueprint('campaign_records', __name__, url_prefix='/campaign/record/')
CORS(bp, **user_cors_args, supports_credentials=True)

@bp.route('outreaches', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_record_outreaches():
    org_id = request.json.get('organisation_id')

    identifiers = request.json.get('identifiers')
    if not identifiers:
        return jsonify({'message': 'Please include identifiers such as thread_id or record_id.'}), 400

    thread_id = identifiers.get('thread_id')
    outreaches = mail_database.get_outreaches(org_id, thread_id)
    return jsonify([outreach.to_dict() for outreach in outreaches])

