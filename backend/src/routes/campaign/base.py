from datetime import datetime
from json import dumps
from os import environ

from Holmes.campaign.flow import Flow, FlowParseError
from Holmes.email.io.analyticsmanager import AnalyticsManager
import requests
from sqlalchemy import func, and_

from src.models import db, Campaign, Audience, Organisation
from flask import Blueprint, send_file
from flask import jsonify, request
from flask_jwt_extended import jwt_required, current_user
from src.functions.org import require_role, has_campaign_id, get_campaign
from src.functions.campaign import get_campaigns, create_campaign, delete_campaign, update_campaign_audience, \
    get_campaign as get_single_campaign, update_campaign_advanced_settings
from src.utils import mail_database, populate_flow_to_dict, spaces_client
from Holmes.email.io.analytics import Analytics
from settings import ORG_SETTINGS
from src.utils import user_cors_args, spaces_client
from flask_cors import CORS

bp = Blueprint('campaigns', __name__, url_prefix='/campaign')
CORS(bp, **user_cors_args, supports_credentials=True)


@bp.route('/test_email', methods=['GET'])
@jwt_required()
@require_role(ORG_SETTINGS['EDITORS'])
def test_email():
    content_id = request.args.get('content_id')
    org_id = request.args.get('organisation_id')
    campaign_id = request.args.get('campaign_id')
    sender_id = request.args.get('sender_id')

    if not content_id:
        return jsonify(
            {'message': 'You must provide a content ID.'}), 400

    if not org_id:
        return jsonify({'message': 'You must provide an organisation_id'}), 400

    email = current_user.email

    try:
        test_payload = {'content_id': content_id, 'organisation_id': org_id, 'campaign_id': campaign_id,
                        'emails': [email], 'sender_id': sender_id}

        result = requests.post(f"{environ['MORIARTY_URL']}/campaigns/test",
                               json=test_payload,
                               headers={'Authorization': f'Bearer {environ["MORIARTY_API_KEY"]}'})

    except requests.exceptions.RequestException as E:
        return jsonify({'message': f'Something went wrong connecting to Moriarty: {str(E)}'}), 400

    if result.ok:
        return jsonify({'message': 'Email sent successfully'}), 200

    return jsonify({'message': f'Email could not be sent: {result.text}'}), 400


# List campaigns
@bp.route('/list', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def list_campaigns():
    org_id = request.json.get('organisation_id')
    paused = request.json.get('paused')
    published = request.json.get('published')
    archived = request.json.get('archived')
    return jsonify(get_campaigns(org_id,
                                 paused=paused, published=published, archived=archived)), 200


@bp.route('/<campaign_id>', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_a_campaign(campaign_id: str):
    organisation_id = request.json.get('organisation_id')
    campaign = Campaign.query.filter_by(organisation_id=organisation_id, id=campaign_id).one_or_none()

    if not campaign:
        return jsonify({"message": "Campaign not found."}), 404

    db.session.expire(campaign, ['flow', 'settings'])
    campaign_dict = campaign.to_dict()

    if campaign.flow:
        org = Organisation.query.filter_by(id=organisation_id).one_or_none()
        p_flow = populate_flow_to_dict(campaign.flow, spaces_client.create_organisation_bucket(organisation_id), org)
        campaign_dict['flow'] = p_flow

    return jsonify(campaign_dict)


# Create a campaign
@bp.route('/create', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['EDITORS'])
def create_new_campaign():
    user = current_user
    org_id = request.json.get('organisation_id')
    name = request.json.get('name')

    flow = request.json.get('flow', '')
    if flow:
        is_template = request.json.get('create_from_template', False)

        try:
            if not is_template:
                flow_obj, emails = Flow.from_nested_dict_with_email_message(flow)
            else:
                flow_obj, emails = Flow.from_dict_with_email_message(flow)

            # Upload the emails to the organisation's bucket
            if emails:
                bucket = spaces_client.create_organisation_bucket(org_id)
                [bucket.upload_email(email, email.id) for email in emails]

            # Convert the flow object back to JSON string
            flow = dumps(flow_obj.to_dict())

        except FlowParseError as FPE:
            return jsonify({"message": "Flow could not be parsed correctly",
                            "error": {'name': FPE.__class__.__name__,
                                      'message': str(FPE)}}), 400

    settings = request.json.get('settings')
    campaign = create_campaign(org_id, user.id, name, flow=flow, settings=settings)

    return jsonify(campaign.to_dict())


# Update a campaign
@bp.route('/update', methods=['PUT'])
@jwt_required()
@require_role(ORG_SETTINGS['EDITORS'])
def update_a_campaign():
    # possible changes are:
    # - name
    # - audience ( pre - launch)
    # - advanced settings
    #    - budget
    #    - budget_type
    #    - start date
    #    - end date
    #    - sending_window

    organisation_id = request.json.get('organisation_id')
    campaign_id = request.json.get('campaign_id')
    campaign = Campaign.query.filter_by(organisation_id=organisation_id, id=campaign_id).one_or_none()

    if not campaign:
        return jsonify({"message": "Campaign not found"}), 404

    new_settings = request.json.get('settings')
    allowed_published = {k: v for k, v in new_settings.items() if k in ['name', 'advanced', 'paused']}

    allowed_settings = {k: v for k, v in new_settings.items() if
                        k in ['name', 'audience_id', 'advanced', 'archived', 'paused']}

    if campaign.published is True:
        # disallow certain changes to the campaign if it is published
        if set(allowed_published.keys()).difference(set(new_settings.keys())):
            return jsonify({"message": "Campaign is already published, you can only edit its name, budget or pause "
                                       "it."}), 403

        if new_settings.get('advanced') and {'budget'}.difference(set(new_settings['advanced'].keys())):
            return jsonify({"message": "Campaign is already published, you can only edit its name, budget or pause "
                                       "it."}), 403

    if new_settings.get('name'):
        campaign.name = new_settings['name']
        db.session.add(campaign)
        db.session.commit()

    if new_settings.get('archived'):
        campaign.archived = new_settings['archived']
        db.session.add(campaign)
        db.session.commit()

    if new_settings.get('audience_id'):
        audience_id = new_settings['audience_id']

        if not Audience.query.filter_by(id=audience_id, organisation_id=organisation_id).one_or_none():
            return jsonify({"message": "Audience not found"}), 404

        # This is in a separate function because it needs to send a request to our Celery server
        update_campaign_audience(campaign_id, audience_id)

    if allowed_settings.get('advanced'):
        advanced_settings = allowed_settings['advanced']
        msg, code = update_campaign_advanced_settings(campaign_id, advanced_settings, published=campaign.published)
        if code != 200:
            return jsonify({"message": msg}), code

    return jsonify({"message": "Campaign updated successfully"}), 200


@bp.route('/delete', methods=['DELETE'])
@jwt_required()
@require_role(ORG_SETTINGS['EDITORS'])
def delete_a_campaign():
    user = current_user
    org_id = request.json.get('organisation_id')
    campaign_id = request.json.get('campaign_id')

    if not has_campaign_id(org_id, campaign_id):
        return jsonify({"error": "Campaign not found"}), 404

    # If they are Admin or Owner, they can remove Campaign objects as they please.
    if user.get_role(org_id).role in ['admin', 'owner']:
        result = delete_campaign(campaign_id, org_id)
    else:
        # If not, only the creator of the Campaign can delete the object. Hence why the User.id is passed.
        result = delete_campaign(campaign_id, org_id, user_id=user.id)

    if result is True:
        return jsonify({"message": "Campaign deleted successfully"}), 200
    else:
        return jsonify({"message": "Campaign was unable to be deleted, please try again later."}), 500


@bp.route('/set_flow', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['EDITORS'])
def create_or_update_campaign_flow():
    org_id = request.json.get('organisation_id')
    flow_data = request.json.get('flow')
    campaign_id = request.json.get('campaign_id')
    is_template = request.json.get('create_from_template', False)

    campaign = get_campaign(org_id, campaign_id)
    if not campaign:
        return jsonify({"message": "Campaign not found"}), 404

    try:
        if flow_data:
            if not is_template:
                parsed_flow, emails = Flow.from_nested_dict_with_email_message(flow_data)

            else:
                parsed_flow, emails = Flow.from_dict_with_email_message(flow_data)

            if emails:
                bucket = spaces_client.create_organisation_bucket(org_id)
                [bucket.upload_email(email, email.id) for email in emails]

            # setting the flow to the campaign
            campaign.flow = dumps(parsed_flow.to_dict())

        else:
            campaign.flow = ''

    except FlowParseError as E:
        return jsonify({'message': "Flow is malformed",
                        "error": {'message': str(E),
                                  'name': 'FlowParseError'}}), 400
    except Exception as E:
        return jsonify({'message': "Flow could not be parsed correctly",
                        "error": {'message': str(E),
                                  'name': E.__class__.__name__}}), 500

    db.session.add(campaign)
    db.session.commit()

    return jsonify({'message': "Campaign flow updated successfully"}), 200


@bp.route('/pause', methods=['PUT'])
@jwt_required()
@require_role(ORG_SETTINGS['EDITORS'])
def toggle_campaign_pause():
    campaign_id = request.json.get('campaign_id')
    org_id = request.json.get('organisation_id')

    # todo: hard_pause requires a call from here to mailing service endpoint
    hard_pause = request.json.get('hard_pause')

    campaign = get_campaign(org_id, campaign_id)
    if campaign is None:
        return jsonify({"message": "Campaign not found"}), 404

    campaign.paused = not campaign.paused
    db.session.add(campaign)
    db.session.commit()

    if campaign.paused:
        msg = 'Campaign has been paused.'
    else:
        msg = 'Campaign has been unpaused.'
    return jsonify({'message': msg}), 200


@bp.route('/publish', methods=['PUT'])
@jwt_required()
@require_role(ORG_SETTINGS['EDITORS'])
def publish_campaign():
    org_id = request.json.get('organisation_id')
    campaign_id = request.json.get('campaign_id')

    campaign = get_campaign(org_id, campaign_id)
    if campaign is None:
        return jsonify({"message": "Campaign not found"}), 404

    if not campaign.audience_id:
        return jsonify({"message": "Campaign must have an audience assigned to it"}), 424

    if not campaign.settings.budget or campaign.settings.budget <= 0:
        return jsonify({"message": "Campaign must have a budget assigned to it"}), 400

    update_campaign_advanced_settings(campaign.id, {'start_date': datetime.now().isoformat()})
    campaign.published = True

    db.session.add(campaign)
    db.session.commit()

    return jsonify({'message': "Campaign published successfully"}), 200


@bp.route('/overview', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def overview_campaign():
    org_id = request.args.get('organisation_id')
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        status = request.args.get('status', None)
        if status and status not in ['draft', 'published', 'paused', 'completed', 'running']:
            return jsonify({'message': 'Invalid status value. Must be one of: draft, published, paused, completed, '
                                       'running'}), 400

        name_filter = request.args.get('name', None)

    except ValueError:
        return jsonify({'message': 'Invalid page or per_page value.',
                        'error': f'Expected Int, got {request.args.get("page")} for page'
                                 f'and {request.args.get("per_page")} for per_page'}), 400

    paginate_args = {'page': page, 'per_page': per_page, 'error_out': False}
    filters = {'organisation_id': org_id}

    if status == 'draft':
        filters.update({'published': False, 'paused': False})
    elif status == 'published':
        filters.update({'published': True, 'paused': False})
    elif status == 'paused':
        filters.update({'published': True, 'paused': True})
    elif status == 'completed':
        filters.update({'status': 'Completed'})
    elif status == 'running':
        filters.update({'status': 'Running'})

    if name_filter:
        campaigns_query = Campaign.query.filter(Campaign.name.ilike(f'%{name_filter}%')).filter_by(**filters)
    else:
        campaigns_query = Campaign.query.filter_by(**filters)

    campaigns = campaigns_query.order_by(Campaign.created_at.desc()).paginate(**paginate_args)
    total = campaigns_query.count()

    try:
        start_str = request.args['start']
        end_str = request.args['end']
    except KeyError:
        start_str = '2023-01-01'
        end_str = datetime.today().strftime('%Y-%m-%d')

    try:
        start = datetime.strptime(start_str, '%Y-%m-%d')
        end = datetime.strptime(end_str, '%Y-%m-%d')
    except ValueError:
        return jsonify({'message': 'Start/end date strings incorrectly formatted. Please use "YYYY-MM-DD"'}), 400

    collection = mail_database[org_id]
    campaigns_by_id = {str(c.id): c for c in campaigns.items}

    data = AnalyticsManager.get_campaigns_overview(start, end, collection, [str(c.id) for c in campaigns])
    campaign_datas = []
    for analytics_data in data:
        campaign_data = {}
        _id = analytics_data['id']
        campaign = campaigns_by_id[_id]

        campaign_data.update({
            'published_at': campaign.settings.start_date.isoformat() if (campaign.settings and
                                                                         campaign.settings.start_date) else None,
            'created_at': campaign.created_at.isoformat(),
            'status': campaign.to_dict().get('status'),
            'name': campaign.name,
            'id': str(campaign.id),
            'analytics': analytics_data
        })

        if campaign.audience:
            campaign_data.update({'audience': {
                'name': campaign.audience.name,
                'id': campaign.audience.id
            }})

        campaign_datas.append(campaign_data)

    return jsonify({
        'total_count': total,
        'data': campaign_datas
    }), 200


@bp.route('/counts', methods=['GET'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_campaign_counts():
    # Count the number of campaigns that are draft, published, paused completed and total. Concurrently!
    org_id = request.args.get('organisation_id')
    result = db.session.query(
        func.count(Campaign.id).filter(
            and_(Campaign.organisation_id == org_id, Campaign.paused == False, Campaign.published == False)),

        func.count(Campaign.id).filter(
            and_(Campaign.organisation_id == org_id, Campaign.paused == False, Campaign.published == True)),

        func.count(Campaign.id).filter(
            and_(Campaign.organisation_id == org_id, Campaign.paused == True, Campaign.published == True)),

        func.count(Campaign.id).filter(
            and_(Campaign.organisation_id == org_id, Campaign.status == 'Completed')),

        func.count(Campaign.id).filter(
            and_(Campaign.organisation_id == org_id, Campaign.status == 'Running')),

        func.count(Campaign.id).filter(
            Campaign.organisation_id == org_id),
    ).one()

    draft, published, paused, completed, running, total = result
    return jsonify({
        'draft': draft,
        'published': published,
        'paused': paused,
        'total': total,
        'completed': completed,
        'running': running
    }), 200