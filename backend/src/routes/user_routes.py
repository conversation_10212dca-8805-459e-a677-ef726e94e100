from sqlalchemy import func
from sqlalchemy.orm import lazyload

from src.models import User, db, PasswordReset, Organisation, UserNotificationSettings
from flask import Blueprint
from flask import jsonify, request
from flask_jwt_extended import create_access_token, jwt_required, current_user, get_jwt_identity, set_access_cookies, \
    get_jwt, create_refresh_token, set_refresh_cookies, unset_jwt_cookies
from src.functions.user import register_user, \
    reset_password as try_password_reset, \
    request_password_reset as get_password_reset, \
    request_email_validation, validate_email, \
    UnknownUserError, UserAlreadyExists, update_settings
from datetime import timedelta, datetime, timezone, date
from src.utils import user_cors_args, redis_manager
from flask_cors import CORS

bp = Blueprint('user', __name__, url_prefix='/user')
CORS(bp, **user_cors_args, supports_credentials=True)


@bp.route('/login', methods=['POST'])
def login():
    # Get user credentials from request
    email = request.json.get('email')
    password = request.json.get('password')

    # Try to find the user in our db
    user = User.query.filter(func.lower(User.email) == func.lower(email)).first()

    # If we cannot find the user or the credentials don't match, raise error
    if not user or not user.check_password(password):
        return jsonify({'message': 'Wrong email or password'}), 401

    # Freshly baked access token
    access_token = create_access_token(identity=user, fresh=True)
    refresh_token = create_refresh_token(identity=user)

    resp = jsonify(access_token=access_token)
    set_access_cookies(resp, access_token)
    set_refresh_cookies(resp, refresh_token)
    return resp, 200


@bp.route("/request_email_validation", methods=['POST'])
@jwt_required()
def req_email_validation():
    user = current_user
    code, message, response_code = request_email_validation(user)
    return jsonify({'message': message}), response_code


@bp.route("/verify_email", methods=['POST'])
@jwt_required()
def verify_email_validation():
    user = current_user
    code = request.json.get('code')
    message, response_code = validate_email(code, user)
    return jsonify({'message': message})


@bp.route("/request_password_reset", methods=['POST'])
def request_password_reset():
    # Get the email from the request
    email = request.json.get('email')

    # Handle the requested password reset
    code, message, response_code = get_password_reset(email)
    return jsonify({'message': message}), response_code


@bp.route("/reset_password", methods=['POST'])
def reset_password():
    # Get the reset identifier from the request, then use it to fetch the reset.
    reset_identifier = request.json.get('reset_identifier')
    new_password = request.json.get('password')
    message, response_code = try_password_reset(reset_identifier, new_password)

    return jsonify({'message': message}), response_code


@bp.route("/logout", methods=["DELETE"])
@jwt_required()
def logout():
    jti = get_jwt()['jti']
    now = datetime.utcnow()
    redis_manager.set(jti, now.isoformat())

    resp = jsonify({'message': 'JWT was succesfully revoked'})
    unset_jwt_cookies(resp)
    return resp, 200


@bp.route('/register', methods=['POST'])
def register():
    # Get user credentials from request
    first_name = request.json.get('first_name')
    last_name = request.json.get('last_name')
    password = request.json.get('password')
    email = request.json.get('email')
    email_optin = request.json.get('email_optin')

    if email.split('@')[-1] not in ['akker.co', 'whatson.ai', 'freddydenoord.nl']:
        return jsonify({'message': 'Sorry, still closed for external use!'}), 401

    try:
        new_user = register_user(first_name, last_name, password, email, email_optin)
    except UserAlreadyExists as Exc:
        return jsonify({'message': str(Exc)}), 409

    # Hooking the new user up with a fresh access token
    access_token = create_access_token(identity=new_user, fresh=True)
    return jsonify(access_token=access_token)


@bp.route('/status', methods=['GET'])
@jwt_required(optional=True)
def status():
    identity = get_jwt_identity()
    if identity:
        user = db.session.get(User, identity['id'])
        if not user:
            return jsonify({'message': 'Something went wrong, please try again later.'}), 401

        # Freshly baked access token
        userdata = user.json_lite
        if user.is_superadmin:
            userdata['organisations'] = [org.json_lite for org in Organisation.query.options(
                lazyload(Organisation.audiences), lazyload(Organisation.campaigns)).all()]
        access_token = create_access_token(identity=user, fresh=True)

        resp = jsonify(access_token=access_token, user=userdata)
        set_access_cookies(resp, access_token)
        return resp, 200

    else:
        return jsonify({'message': 'Your session has expired'}), 401


@bp.route('/notification_settings', methods=['GET', 'PUT'])
@jwt_required(optional=True)
def get_notification_settings():
    if request.method == 'PUT':
        new_settings = request.json.get('settings')
        if not new_settings:
            return jsonify({'message': 'No settings provided'}), 400

        message, code = update_settings(current_user.id, new_settings)
        return jsonify({'message': message}), code

    settings = UserNotificationSettings.query.filter_by(user_id=current_user.id).one_or_none()
    return jsonify(settings.json if settings else {})


@bp.after_request
def refresh_expiring_jwt(response):
    try:
        exp_timestamp = get_jwt()["exp"]
        now = datetime.now(timezone.utc)
        target_timestamp = datetime.timestamp(now + timedelta(minutes=30))
        if target_timestamp > exp_timestamp:
            access_token = create_access_token(identity=current_user, fresh=False)
            set_access_cookies(response, access_token)
        return response
    except (RuntimeError, KeyError):
        # This block is reached when someone alters their token headers. In this case,
        # we just do nothing with their current token and we do not send a new one.
        return response

