:root {
    /* Basic Color Definitions */
    --primary-color-light: #fff;
    --primary-color-dark: #121212;
    --color-info: #3498db;
    --color-success: #07bc0c;
    --color-warning: #f1c40f;
    --color-error: #e74c3c;
    --color-transparent: hsla(0,0%,100%,.7);
}

body {
    font-family: sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    line-height: 1.5;
    -moz-tab-size: 4;
    tab-size: 4;
    background-color: var(--primary-color-light);
    color: var(--primary-color-dark);
    padding: 20px;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

th, td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #f2f2f2;
}

.collapsible {
    background-color: #f2f2f2;
    color: #333;
    cursor: pointer;
    padding: 10px;
    width: 100%;
    border: none;
    text-align: left;
    outline: none;
    font-size: 16px;
    margin-top: 20px;
}

.active, .collapsible:hover {
    background-color: #ddd;
}

.content {
    padding: 0 18px;
    display: none;
    overflow: hidden;
    background-color: #f9f9f9;
    margin-bottom: 20px;
}

.flash {
    background-color: var(--color-info);
    color: #fff;
    padding: 10px;
    margin-bottom: 10px;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 10px 0;
    text-decoration: none;
    color: #fff;
    background-color: var(--color-info);
    border-radius: 5px;
    text-align: center;
}

.btn-primary {
    background-color: var(--color-info);
}

/* Search Bar Style */
#searchBar {
    width: 100%;
    max-width: 400px;
    padding: 10px 15px;
    font-size: 16px;
    border: 1px solid #ccc;
    border-radius: 25px;
    outline: none;
    transition: all 0.3s ease-in-out;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 10px;
}

#searchBar:focus {
    border-color: #3498db;
    box-shadow: 0 0 8px rgba(52, 152, 219, 0.5);
}

#searchBar::placeholder {
    color: #999;
    font-style: italic;
}

/* Styles for the add sending domain form */
#addSendingDomainForm input {
    margin-top: 10px;
    padding: 8px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 5px;
    outline: none;
}

#addSendingDomainForm input:focus {
    border-color: #3498db;
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
}

#addSendingDomainForm span {
    font-size: 16px;
    font-weight: bold;
    margin-right: 5px;
}

#addSendingDomainForm button {
    padding: 8px 15px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

#addSendingDomainForm button:hover {
    background-color: #2980b9;
}


#addEmailSenderForm input {
    margin-top: 10px;
    padding: 8px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 5px;
    outline: none;
}

#addEmailSenderForm input:focus {
    border-color: #3498db;
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
}

#addEmailSenderForm span {
    font-size: 16px;
    font-weight: bold;
    margin-right: 5px;
}

#addEmailSenderForm button {
    padding: 8px 15px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

#addEmailSenderForm button:hover {
    background-color: #2980b9;
}
