    function getCookie(name) {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop().split(';').shift();
}


document.addEventListener('DOMContentLoaded', function() {
            var coll = document.getElementsByClassName("collapsible");
            for (var i = 0; i < coll.length; i++) {
                coll[i].addEventListener("click", function() {
                    this.classList.toggle("active");
                    var content = this.nextElementSibling;
                    if (content.style.display === "block") {
                        content.style.display = "none";
                    } else {
                        content.style.display = "block";
                    }
                });
            }
        });


/**
 * Creates a new user.
 * @param {string} email - The email of the user to create.
 * @param {string} firstName - The first name of the user to create.
 * @param {string} lastName - The last name of the user to create.
 * @returns {Promise<Object>} - A promise that resolves to the response data or rejects with an error.
 */
function createUser(email, firstName, lastName) {
    return fetch('/admin/users', { // Adjust URL as needed
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'X-Csrf-Token': getCookie('csrf_access_token')
        },
        body: JSON.stringify({ email: email, first_name: firstName, last_name: lastName })
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(errorData => {
                throw new Error(errorData.error);
            });
        }
        return response.json();
    })
    .catch(error => {
        throw error;
    });
}

/**
 * Creates a new organisation.
 * @param {string} name - The name of the organisation to create.
 * @param {string} website - The website of the organisation to create.
 * @param {null} userId - The ID of the user to associate with the organisation.
 * @returns {Promise<Object>} - A promise that resolves to the response data or rejects with an error.
 */
function createOrganisation(name, website, userId=null) {
    return fetch('/admin/organisation', { // Adjust URL as needed
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'X-Csrf-Token': getCookie('csrf_access_token')
        },
        body: JSON.stringify({name: name, website: website, user_id: userId})
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(errorData => {
                throw new Error(errorData.error);
            });
        }
        return response.json();
    })
    .catch(error => {
        throw error;
    });
}


function showModal(title, message, onClose = null, overwriteElement = null) {
    const modal = document.getElementById('custom-modal');
    const modalTitle = document.getElementById('modal-title');
    const modalMessage = document.getElementById('modal-message');
    const modalClose = document.getElementById('modal-close');
    const modalCopyBtn = document.getElementById('modal-copy-btn');

    modalTitle.textContent = title;
    modalMessage.textContent = message;

    if (overwriteElement) {
        overwriteElement.style.display = 'block';
        modalMessage.innerHTML = '';
        modalMessage.appendChild(overwriteElement);
    }
    modal.style.display = 'block';

    function closeModal() {
        modal.style.display = 'none';
        if (typeof onClose === 'function') {
            onClose();
        }
        if (overwriteElement) {
            modalMessage.querySelector('tbody').innerHTML = '';
            overwriteElement.style.display = 'none';
        }
    }

    modalClose.onclick = closeModal;

    window.onclick = function(event) {
        if (event.target === modal) {
            closeModal();
        }
    };

    modalCopyBtn.onclick = function() {
        navigator.clipboard.writeText(message).then(() => {
            alert('Text copied to clipboard');
        }).catch(err => {
            console.error('Error copying text: ', err);
        });
    };
}
