{% extends 'base.html' %}

{% block content %}
    <div id="billing-container">
    <form id="billing-form">
        <label for="start-date">Month:</label>
        <input type="month" id="start-date" name="start_date">
        <button type="button" onclick="submitBillingForm()">Submit</button>
    </form>
    </div>
<script src="{{ url_for('admin.static', filename='utils.js') }}"></script>
<script>

function submitBillingForm() {
    const startDate = document.getElementById('start-date').value;
    const csrfToken = getCookie('csrf_access_token');

    fetch('/admin/billing', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'X-Csrf-Token': csrfToken
        },
        body: JSON.stringify({ start_date: startDate })
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(errorData => {
                throw new Error(errorData.error);
            });
        }
        // The response is a rendered HTML page
        return response.text();
    })
    .then(data => {
        // Handle success - perhaps redirect or show a success message
        console.log('Success!');
        document.getElementById('billing-container').innerHTML = data;

    })
    .catch(error => {
        // Handle error - perhaps show an error message
        console.error('Error:', error);
    });
}
</script>

{% endblock %}
