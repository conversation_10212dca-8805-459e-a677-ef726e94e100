<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Organisations Management</title>
    <link rel="stylesheet" href="{{ url_for('admin.static', filename='stylesheet.css') }}">
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        .pagination {
            display: flex;
            justify-content: center;
            padding: 10px;
        }
        .pagination button {
            padding: 10px;
            margin: 5px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <a href="{{ url_for('admin.home') }}">Back</a>

    <h1>Organisations Management</h1>

    <input type="text" id="searchBar" placeholder="Search Organisations" oninput="debouncedSearch()">

    <table>
        <thead>
            <tr>
                <th>Go to</th>
                <th>Name</th>
                <th>Website</th>
                <th>User count</th>
            </tr>
        </thead>
        <tbody id="org-list">
            <!-- Rows will be populated by JavaScript -->
        </tbody>
    </table>

    <div class="pagination">
        <button id="prevPageBtn" onclick="changePage(prevPage)">Previous</button>
        <button id="nextPageBtn" onclick="changePage(nextPage)">Next</button>
    </div>

        <button type="button" class="collapsible">Create Organisation</button>
    <div class="content">
        {% include 'components/create_organisation.html' %}
    </div>

    <script src="{{ url_for('admin.static', filename='utils.js') }}"></script>
    <script>
        let timeout = null;
        let prevPage = null;
        let nextPage = null;

        document.addEventListener('DOMContentLoaded', function() {
            fetchOrganisations(); // Fetch organisations on page load

            document.getElementById('searchBar').addEventListener('input', function(event) {
                debouncedSearch();
            });
        });

        function fetchOrganisations(query = '', page = 1) {
            const url = `/admin/organisation?name=${encodeURIComponent(query)}&page=${page}`;

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    populateTable(data.organisations);
                    prevPage = data.prev_page;
                    nextPage = data.next_page;
                    document.getElementById('prevPageBtn').style.display = prevPage ? 'block' : 'none';
                    document.getElementById('nextPageBtn').style.display = nextPage ? 'block' : 'none';
                })
                .catch(error => console.error('Error fetching organisations:', error));
        }

        function populateTable(organisations) {
            const orgList = document.getElementById('org-list');
            orgList.innerHTML = ''; // Clear existing rows

            organisations.forEach(org => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><button onclick="navigateToOrganisation('${org.id}')">Nav</button></td>
                    <td>${org.name}</td>
                    <td>${org.website}</td>
                    <td>${org.user_count}</td>
                `;
                orgList.appendChild(row);
            });
        }

        function changePage(page) {
            if (page) {
                const query = document.getElementById('searchBar').value;
                fetchOrganisations(query, page);
            }
        }

        function navigateToOrganisation(orgId) {
            window.location.href = `/admin/organisation/${orgId}`;
        }

        function debouncedSearch() {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                const query = document.getElementById('searchBar').value;
                fetchOrganisations(query);
            }, 500);
        }
    </script>
</body>
</html>
