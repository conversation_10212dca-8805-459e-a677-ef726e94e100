{% extends 'base.html' %}

{% block title %}
Whatson Admin Login Page
{% endblock %}

{% block styles %}
<style>
    .error { color: red; }
</style>
{% endblock %}

{% block content %}
    <h1>Please login</h1>
    <a style="visibility: hidden" id="redirect" href="{{ redirect if redirect else '/admin' }}"></a>

    <br>
    <form id="login-form" onsubmit="return false;">
        <label>
            <input type="text" placeholder="Email" name="email" id="email" required>
        </label>
        <br>
        <label>
            <input type="password" placeholder="Password" name="password" id="password" required>
        </label>
        <br>
        <input type="button" value="Login" onclick="login()">
    </form>
    <p class="error" id="error-msg" style="display:none;"></p>
    <br>
    <script src="{{ url_for('admin.static', filename='utils.js') }}"></script>
    <script>

        document.addEventListener("DOMContentLoaded", function() {
            function login() {
                console.log('Login function called');
                var email = document.getElementById('email').value;
                var password = document.getElementById('password').value;
                var errorMsg = document.getElementById('error-msg');

                console.log('Email:', email);
                console.log('Password:', password);

                fetch("/user/login", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({ email: email, password: password })
                })
                .then(response => {
                    console.log('Response received');
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response;
                })
                .then(res => {

                        // Loop thru response headers and set cookie
                        res.headers.forEach((value, name) => {
                            console.log('Header:', name, value);
                            document.cookie = name + '=' + value;
                        });

                        res.json().then(data => {
                            console.log('Data:', data);
                            document.cookie = 'access_token_cookie' + '=' + data.access_token;
                        });

                    window.location.href = document.getElementById('redirect').href;

                })
                .catch(error => {
                    console.error('Error:', error);
                    errorMsg.textContent = 'Error: ' + error.message;
                    errorMsg.style.display = 'block';
                });
            }

            // Make login function globally available
            window.login = login;
        });
    </script>
{% endblock %}