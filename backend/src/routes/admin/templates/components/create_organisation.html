<button type="button" class="collapsible">Create Organisation with you as owner</button>
<div class="content">
    <form id="createOrgWithYouForm">
        <input type="text" name="name" placeholder="Organisation Name" required>
        <input type="text" name="website" placeholder="Organisation Website" required>
        <button type="submit">Create</button>
    </form>
</div>

<button type="button" class="collapsible">Create Organisation with a new User as owner</button>
<div class="content">
    <form id="createOrgWithNewUserForm">
        <input type="email" name="email" placeholder="Owner Email" required>
        <input type="text" name="firstName" placeholder="Owner First Name" required>
        <input type="text" name="lastName" placeholder="Owner Last Name" required>
        <input type="text" name="orgName" placeholder="Organisation Name" required>
        <input type="text" name="orgWebsite" placeholder="Organisation Website" required>
        <button type="submit">Create</button>
    </form>
</div>

<script>
        // Create organisation with current user as owner
        document.getElementById('createOrgWithYouForm').addEventListener('submit', function(event) {
            event.preventDefault();
            const formData = new FormData(this);
            let name = formData.get('name');
            let website =formData.get('website');

            if (confirm("Are you sure you want to create this organisation with yourself as owner?")) {
                createOrganisation(name, website)
                    .then(data => {
                        showModal('Organisation Creation', `Organisation created successfully,\nID: ${data.id}`. location.reload);
                    })
                    .catch(error => {
                        console.error('Error creating organisation:', error);
                        showModal('Organisation Creation', error.message);
                    });
            }
        });

        // Create organisation with new user as owner
        document.getElementById('createOrgWithNewUserForm').addEventListener('submit', function(event) {
            event.preventDefault();
            const formData = new FormData(this);
            let email = formData.get('email');
            let first_name = formData.get('firstName');
            let last_name = formData.get('lastName');
            let name = formData.get('orgName');
            let website = formData.get('orgWebsite');

            if (confirm("Are you sure you want to create this organisation with a new user as owner?")) {
                createUser(email, first_name, last_name)
                    .then(user => {
                        let user_id = user.user_id;
                        let password = user.password;

                        return createOrganisation(name, website, user_id)
                            .then(org => {
                                return { org_id: org.id, user_id: user_id, password: password };
                            });
                    })
                    .then(data => {
                        showModal('Organisation Creation',
                            `Organisation created successfully with new user as owner.\nOrganisation ID: ${data.org_id}\nUser ID: ${data.user_id}\nPassword: ${data.password}`,
                        location.reload);
                    })
                    .catch(error => {
                        console.error('Error:', error);
                         showModal('Organisation Creation', error.message)});
            }
        });

</script>
