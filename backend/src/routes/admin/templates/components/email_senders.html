<button type="button" class="collapsible">Email Senders(Sparkpost)</button>
<div class="content">
    <script src="{{ url_for('admin.static', filename='utils.js') }}"></script>
    <table>
        <thead>
            <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Created at</th>
            </tr>
        </thead>
        <tbody id="emailSendersTableBody">
            <!-- Rows will be populated by JavaScript -->
        </tbody>
    </table>
    <button type="button" class="collapsible">Add Email Sender</button>
    <div class="content">
        <form id="addEmailSenderForm">
            <select name="domain_id" id="domainSelect" style="margin-right: 10px;" required>
            <input type="text" name="email" placeholder="<EMAIL>" required>
            <input type="text" name="name" placeholder="<PERSON>" required>
                <!-- Options will be populated by JavaScript -->
            </select>
            <button type="submit" style="margin-left: 10px;">Add Sender</button>
        </form>
        <p>Please select a domain, enter an email address and a fullname.`</p>
    </div>
</div>

<script>

    document.addEventListener('DOMContentLoaded', function() {
        fetchEmailSenders();
        fetchSendingDomainsIDs();

        document.getElementById('addEmailSenderForm').addEventListener('submit', function(event) {
            event.preventDefault();
            addEmailSender(new FormData(this));
        });
    });

    function fetchEmailSenders() {
        fetch(`{{ organisation.id }}/email-senders`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
            },
        })
        .then(response => response.json())
        .then(data => {
            const emailSendersTableBody = document.getElementById('emailSendersTableBody');
            emailSendersTableBody.innerHTML = '';
            data.forEach(sender => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${sender.name}</td>
                    <td>${sender.email}</td>
                    <td>${sender.created_at}</td>
                `;
                emailSendersTableBody.appendChild(row);
            });
        })
        .catch(error => console.error('Error fetching email senders:', error));
    }

    function fetchSendingDomainsIDs() {
        fetch(`{{ organisation.id }}/sending-domains`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
            },
        })
        .then(response => response.json())
        .then(data => {
            const domainSelect = document.getElementById('domainSelect');
            domainSelect.innerHTML = '';
            data.forEach(domain => {
                if (domain.is_verified) {
                    const option = document.createElement('option');
                    option.value = domain.id;
                    option.textContent = domain.domain;
                    domainSelect.appendChild(option);
                }
            });
        })
        .catch(error => console.error('Error fetching sending domains:', error));
    }

    function addEmailSender(formData) {
        fetch(`{{ organisation.id }}/email-senders`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'X-Csrf-Token': getCookie('csrf_access_token'),
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(Object.fromEntries(formData))
        })
        .then(response => response.json())
        .then(data => {
            if (data.id) {
                showModal('Add Email Sender', 'Email sender added successfully', fetchEmailSenders);
            } else {
                showModal('Add Email Sender', 'Error adding email sender');
            }
        })
        .catch(error => console.error('Error adding email sender:', error));
    }
</script>