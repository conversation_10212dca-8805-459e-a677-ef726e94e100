<button type="button" class="collapsible">Sending Domains(Sparkpost)</button>
<div class="content">
    <script src="{{ url_for('admin.static', filename='utils.js') }}"></script>
    <table>
        <thead>
            <tr>
                <th>Domain</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody id="sendingDomainsTableBody">
            <!-- Rows will be populated by JavaScript -->
        </tbody>
    </table>
    <button type="button" class="collapsible">Add Sending Domain</button>
    <div class="content">
        <form id="addSendingDomainForm" style="display: flex; align-items: center;">
            <input type="text" name="domain" placeholder="contact.whatson.ai" required style="width: 200px; margin-left: 5px; margin-right: 10px;">
            <button type="submit" style="margin-left: 10px; margin-top: 10px;">Add Domain</button>
        </form>
        <p>Note that while a subdomain is the norm, it is not mandatory!</p>

    </div>
</div>

<!-- Modal Structure, hidden on startup -->
<div id="dnsRecordsTable" style="display:none">
        <table id="dnsRecordsTable">
            <thead>
                <tr>
                    <th>Type</th>
                    <th>Hostname</th>
                    <th>Priority</th>
                    <th>Value</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                <!-- DNS records will be populated here -->
            </tbody>
        </table>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        fetchSendingDomains();

        document.getElementById('addSendingDomainForm').addEventListener('submit', function(event) {
            event.preventDefault();
            addSendingDomain(new FormData(this));
        });

    });

    function fetchSendingDomains() {
        fetch(`{{ organisation.id }}/sending-domains`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        })
        .then(response => response.json())
        .then(data => {
            console.log('Fetched sending domains:', data);
            const sendingDomainsTableBody = document.getElementById('sendingDomainsTableBody');
            sendingDomainsTableBody.innerHTML = '';
            data.forEach(domain => {
                console.log('Domain:', domain);
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${domain.domain}</td>
                    <td>${domain.is_verified ? 'Verified' : 'Pending'}</td>
                    <td>
                        <button onclick="viewDNSRecords('${domain.id}')">View DNS Records</button>
                        <button onclick="verifySendingDomain('${domain.id}')">Verify</button>
                    </td>
                `;
                sendingDomainsTableBody.appendChild(row);
            });
        })
        .catch(error => console.error('Error fetching sending domains:', error));
    }

    function addSendingDomain(formData) {
        fetch(`{{ organisation.id }}/sending-domains`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'X-Csrf-Token': getCookie('csrf_access_token')
            },
            body: JSON.stringify(Object.fromEntries(formData))
        })
        .then(response => response.json())
        .then(data => {
            console.log('Add domain response:', data); // Add this line
            if (data.id) {
                showModal('Add Sending Domain', 'Sending domain added successfully.', fetchSendingDomains);
            } else {
                showModal('Add Sending Domain', 'Sending domain resulted in an error: ' + data.error);
            }
        })
        .catch(error => console.error('Error adding sending domain:', error));
    }

    function verifySendingDomain(domainId) {
        fetch(`{{ organisation.id }}/sending-domains/` + domainId + '/verify', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                showModal('Verify Sending Domain', 'Error verifying domain: ' + data.error);
                return;
            }
                showModal('Verify Sending Domain', 'Domain verified successfully.', fetchSendingDomains);
        })
        .catch(error => console.error('Error verifying domain', error));
    }

    function viewDNSRecords(domainId) {
        fetch(`{{ organisation.id }}/sending-domains/${domainId}/dns-records`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        })
        .then(response => response.json())
        .then(data => {
            console.log('DNS records:', data); // Add this line
            const dnsRecordsTable = document.getElementById('dnsRecordsTable').querySelector('tbody');
            dnsRecordsTable.innerHTML = '';
            data.forEach(record => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${record.type}</td>
                    <td>${record.hostname}</td>
                    <td>${record.priority}</td>
                    <td class="truncate">${record.value}</td>
                    <td><button onclick="copyToClipboard('${record.value}')">Copy</button></td>
                `;
                dnsRecordsTable.appendChild(row);
            });
            const dnsTable = document.getElementById('dnsRecordsTable');
            showModal('DNS Records', null, null, dnsTable);
        })
        .catch(error => console.error('Error fetching DNS records:', error));
    }

    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            console.log('Copied DNS record to clipboard');
        }).catch(err => {
            console.error('Error copying to clipboard: ', err);
        });
    }

</script>

<style>

    .truncate {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .truncate:hover {
        overflow: visible;
        white-space: normal;
    }
</style>
