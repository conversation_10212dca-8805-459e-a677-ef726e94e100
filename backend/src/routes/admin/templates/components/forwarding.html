<button type="button" class="collapsible">Forwarding Tags</button>
<div class="content">
    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Tag</th>
                <th>Email</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody id="forwardingTableBody">
            <!-- Rows will be populated by JavaScript -->
        </tbody>
    </table>
    <button type="button" class="collapsible">Add Forward</button>
    <div class="content">
        <form id="addForwardingTagForm">
            <select name="tag" id="tagSelect" required>
                <!-- Options will be populated by JavaScript -->
            </select>
            <input type="email" name="email" placeholder="Email" required>
            <button type="submit">Add Tag</button>
        </form>
    </div>
</div>
<script src="{{ url_for('admin.static', filename='utils.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        fetchForwardingTags();
        fetchOrganisationForwards();

        document.getElementById('addForwardingTagForm').addEventListener('submit', function(event) {
            event.preventDefault();
            addForwardingTag(new FormData(this));
        });
    });

    function fetchForwardingTags() {
        fetch(`{{ organisation.id }}/forwarding/tags`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        })
        .then(response => response.json())
        .then(data => {
            window.forwardingTags = data;
            populateTagSelect(data);
        })
        .catch(error => console.error('Error fetching forwarding tags:', error));
    }

        function populateTagSelect(tags) {
        const tagSelect = document.getElementById('tagSelect');
        tagSelect.innerHTML = '';
        tags.forEach(tag => {
            const option = document.createElement('option');
            option.value = tag;
            option.textContent = tag;
            tagSelect.appendChild(option);
        });
    }

    function fetchOrganisationForwards() {
        fetch(`{{ organisation.id }}/forwarding`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        })
        .then(response => response.json())
        .then(data => {
            const forwardingTableBody = document.getElementById('forwardingTableBody');
            forwardingTableBody.innerHTML = '';
            data.forEach(forward => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${forward.id}</td>
                    <td>${forward.tag}</td>
                    <td>${forward.email}</td>
                    <td>
                        <button onclick="confirmRemoveForwardingTag('${forward.tag}',
                         '${forward.email}', '${forward.id}')">Remove</button>
                    </td>
                `;
                forwardingTableBody.appendChild(row);
            });
        })
        .catch(error => console.error('Error fetching organisation forwards:', error));
    }

    function confirmRemoveForwardingTag(tag, email, id) {
        if (confirm(`Are you sure you want to remove the forwarding tag "${tag}" for email "${email}"?`)) {
            removeForwardingTag(id);
        }
    }

    function addForwardingTag(formData) {
        const tag = formData.get('tag');
        const email = formData.get('email');

        if (!window.forwardingTags.includes(tag)) {
            showModal('Add Tag Forward', 'Invalid tag');
            return;
        }

        fetch(`{{ organisation.id }}/forwarding`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'X-Csrf-Token': getCookie('csrf_access_token')
            },
            body: JSON.stringify({ tag, email })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showModal('Add Tag Forward', data.success, fetchOrganisationForwards);
            } else {
                showModal('Add Tag Forward', data.error);
            }
        })
        .catch(error => console.error('Error adding forwarding tag:', error));
    }

    function removeForwardingTag(id) {
        fetch(`{{ organisation.id }}/forwarding`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'X-Csrf-Token': getCookie('csrf_access_token')
            },
            body: JSON.stringify({ id })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showModal('Remove Tag Forward', data.success, fetchOrganisationForwards);
            } else {
                showModal('Remove Tag Forward', data.error);
            }
        })
        .catch(error => console.error('Error removing forwarding tag:', error));
    }

</script>
<style>
    .context-window {
        position: absolute;
        background: white;
        border: 1px solid #ddd;
        max-height: 200px;
        overflow-y: auto;
        width: 100%;
        z-index: 1000;
    }
    .user-result {
        padding: 10px;
        border-bottom: 1px solid #ddd;
    }
    .user-result:last-child {
        border-bottom: none;
    }
    .user-result p {
        margin: 0;
    }
</style>
