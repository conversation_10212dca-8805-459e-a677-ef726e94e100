<button type="button" class="collapsible">Users</button>
<div class="content">
    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Email</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for user in organisation.users %}
            <tr>
                <td>{{ user.id }}</td>
                <td>{{ user.first_name }} {{ user.last_name }}</td>
                <td>{{ user.email }}</td>
                <td>
                    <button onclick="confirmRemoveUser('{{ user.id }}')">Remove</button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    <button type="button" class="collapsible">Add user</button>
    <div class="content">
        <input type="text" id="searchUser" placeholder="Search user by email">
        <div id="searchResults" class="context-window"></div>
    </div>
        </table>
    <button type="button" class="collapsible">Create and add user</button>
    <div class="content">
        <form id="createAddUser">
            <input type="email" name="email" placeholder="Email" required>
            <input type="text" name="firstName" placeholder="First name" required>
            <input type="text" name="lastName" placeholder="Last name" required>
            <button type="submit">Create</button>
        </form>
    </div>
</div>
<script src="{{ url_for('admin.static', filename='utils.js') }}"></script>
<script>

    let timeout = null;
        document.addEventListener('DOMContentLoaded', function() {


            document.getElementById('createAddUser').addEventListener('submit', function(event) {

                    event.preventDefault();
                    if (confirm("Are you sure you want to create and add this user to the organisation?")) {
                        createAddUser(new FormData(this));
        }
        });

        document.getElementById('searchUser').addEventListener('input', function(event) {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                searchUser(event.target.value);
            }, 500);
        });
    });


    function searchUser(query) {
        if (query.length === 0) {
            document.getElementById('searchResults').innerHTML = '';
            return;
        }

        fetch(`{{ url_for('admin.search_user') }}?email=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                displayResults(data);
            })
            .catch(error => {
                console.error('Error:', error);
            });
    }

    function displayResults(users) {
        const resultsDiv = document.getElementById('searchResults');
        resultsDiv.innerHTML = '';

        if (users.length === 0) {
            resultsDiv.innerHTML = '<p>No users found</p>';
            return;
        }

        users.forEach(user => {
            const userDiv = document.createElement('div');
            userDiv.className = 'user-result';
            userDiv.innerHTML = `
                <p><strong>${user.first_name} ${user.last_name}</strong></p>
                <p>${user.email}</p>
                <button onclick="confirmAddUser('${user.id}')">Add User</button>
            `;
            resultsDiv.appendChild(userDiv);
        });
    }

    function confirmAddUser(userId) {
        if (confirm("Are you sure you want to add this user to the organisation?")) {
            addUser(userId);
        }
    }

    function confirmRemoveUser(userId) {
        if (confirm("Are you sure you want to remove this user from the organisation?")) {
            removeUser(userId);
        }
    }

    function addUser(userId, createAlert = true) {
        return fetch(`{{ organisation.id }}/users/add`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'X-Csrf-Token': getCookie('csrf_access_token')
            },
            body: JSON.stringify({ user_id: userId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (createAlert) {
                    showModal('Add User', data.success, location.reload);
                }
                return true;
            } else {
                    showModal('Add User', data.error);
                return false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            return false;
        });
    }


    function createAddUser(formData) {
        let email = formData.get('email');
        let firstName = formData.get('firstName');
        let lastName = formData.get('lastName');

        fetch('{{ url_for('admin.create_user') }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'X-Csrf-Token': getCookie('csrf_access_token')
            },
            body: JSON.stringify({ email: email, first_name: firstName, last_name: lastName })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let userId = data.user_id;
                let password = data.password;

                addUser(userId, false).then(result => {
                    if (result) {
                        showModal('User Creation', `User created and added successfully.\nPassword: ${password}`);
                    }
                }).catch(error => {
                    console.error('Error adding user:', error);
                });
            } else {
                showModal('User Creation', 'Encountered error adding user: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error creating user:', error);
        });
    }


    function removeUser(userId) {
        fetch(`{{ organisation.id }}/users/remove`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'X-Csrf-Token': getCookie('csrf_access_token')
            },
            body: JSON.stringify({ user_id: userId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showModal('Remove User', data.success);
                location.reload();
            } else {
                showModal('Remove User', data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }
</script>
<style>
    .context-window {
        position: absolute;
        background: white;
        border: 1px solid #ddd;
        max-height: 200px;
        overflow-y: auto;
        width: 100%;
        z-index: 1000;
    }
    .user-result {
        padding: 10px;
        border-bottom: 1px solid #ddd;
    }
    .user-result:last-child {
        border-bottom: none;
    }
    .user-result p {
        margin: 0;
    }
</style>
