from dataclasses import asdict
from datetime import datetime
from functools import wraps
from json import dumps

from dns import resolver
from flask import Blueprint, render_template, request, jsonify, g, redirect, url_for, session
from flask_jwt_extended import jwt_required, current_user, verify_jwt_in_request, get_jwt_identity, create_access_token, \
    set_access_cookies
from tldextract import extract

from src.functions.admin import get_billing_info
from src.functions.org import require_super_user, add_user_to_org, create_new_org
from src.functions.user import register_user, generate_password, UserAlreadyExists
from src.models import Organisation, User, UserOrgRole, db, EmailTagForward, SendingDomain, EmailSender
from src.utils import user_cors_args, sending_service_api
from flask_cors import CORS

bp = Blueprint('admin',
               __name__,
               url_prefix='/admin',
               template_folder='templates',
               static_folder='static')

CORS(bp, **user_cors_args, supports_credentials=True)

FORWARDING_TAGS = ['*', 'schedule meeting', 'other',
                   'not interested', 'interested', 'ask later', 'question', 'contact me']


@bp.before_request
def load_user():
    if current_user:
        g.user = current_user


@bp.route('/', methods=['GET'])
@jwt_required()
@require_super_user()
def home():
    return render_template('home.html')


@bp.route('/login', methods=['GET'])
def login():
    if current_user:
        return redirect(url_for('admin.home'))

    _redirect = request.args.get('redirect')
    return render_template('login.html', redirect=_redirect)


def check_auth_and_refresh(redirect_route='admin.login'):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # Verify the JWT in the request
                verify_jwt_in_request()

                # Optionally, refresh the token here if it's about to expire
                identity = get_jwt_identity()
                user = User.query.filter_by(id=identity['id']).first()

                new_token = create_access_token(identity=user)
                set_access_cookies(jsonify(dict(access_token=new_token)), new_token)

            except Exception as e:
                print(e)
                # If verification fails, redirect to login with a redirect parameter
                return redirect(url_for(redirect_route, redirect=request.path))
            return f(*args, **kwargs)
        return decorated_function
    return decorator


@bp.route('/billing', methods=['GET', 'POST'])
@check_auth_and_refresh()
@require_super_user()
def billing():
    if request.method == 'GET':
        return render_template('billing.html')
    if request.method == 'POST':
        start = request.json.get('start_date')
        # Start is a month selector so we add the last day of the month
        start += '-01'

        try:
            start_dt = datetime.strptime(start, '%Y-%m-%d')
        except ValueError:
            return jsonify({'error': 'Invalid date format'}), 400

        # Select the first day of the next month
        end_dt = start_dt.replace(month=start_dt.month + 1, day=1)

        results = get_billing_info(start_dt, end_dt)
        return render_template('billing_result.html',
                               records=results,
                               start_date=start,
                               end_date=end_dt.strftime('%Y-%m-%d'))


@bp.route('/organisation-management', methods=['GET'])
@check_auth_and_refresh()
@require_super_user()
def organisation_management_page():
    return render_template('organisations_overview.html')


@bp.route('/organisation', methods=['GET'])
@check_auth_and_refresh()
@require_super_user()
def organisations():
    name_query = request.args.get('name')
    page_no = int(request.args.get('page', 1))
    pagination_args = {
        'page': page_no,
        'per_page': 10
    }

    if name_query:
        orgs = Organisation.query.filter(Organisation.org_name.ilike(f'%{name_query}%')).paginate(**pagination_args)
    else:
        orgs = Organisation.query.paginate(**pagination_args)

    page_amount = orgs.pages
    return jsonify(dict(
                           organisations=[org.json_lite for org in orgs.items],
                           next_page=page_no + 1 if page_no < page_amount else page_amount,
                           prev_page=page_no - 1 if page_no > 1 else 1))




@bp.route('/organisation/<organisation_id>', methods=['GET'])
@check_auth_and_refresh()
@require_super_user()
def organisation(organisation_id: str):
    org = Organisation.query.filter_by(id=organisation_id).one_or_none()
    if not org:
        return jsonify({'error': 'Organisation not found'}), 404

    return render_template('organisations_detail.html', organisation=org.json)


@bp.route('/users', methods=['POST'])
@check_auth_and_refresh()
@require_super_user()
def create_user():
    email = request.json.get('email')
    first_name = request.json.get('first_name')
    last_name = request.json.get('last_name')

    pwd = generate_password()
    try:
        usr = register_user(first_name, last_name, pwd, email, True, True)
    except UserAlreadyExists:
        return jsonify({'error': 'User already exists'}), 400
    return jsonify({'password': pwd,
                    'user_id': str(usr.id),
                    'success': 1})


@bp.route('/users/search', methods=['GET'])
@jwt_required()
@require_super_user()
def search_user():
    email_args = request.args.get('email')
    if not email_args:
        return jsonify({'error': 'No email provided'}), 400

    _users = User.query.filter(User.email.ilike(f'%{email_args}%')).all()

    return jsonify([user.json_lite for user in _users])


@bp.route('/organisation/<organisation_id>/users/<action>', methods=['POST'])
@jwt_required()
@require_super_user()
def add_user(organisation_id: str, action: str):
    if action not in ['add', 'remove']:
        return jsonify({'error': 'Invalid action'}), 400

    user_id = request.json.get('user_id')
    if not user_id:
        return jsonify({'error': 'No user_id provided'}), 400

    user = User.query.filter_by(id=user_id).one_or_none()
    if not user:
        return jsonify({'error': 'User not found'}), 404

    org = Organisation.query.filter_by(id=organisation_id).one_or_none()
    if not org:
        return jsonify({'error': 'Organisation not found'}), 404

    if action == 'add':
        add_user_to_org(org.id, user.id, 'editor', True)
        return jsonify({'success': 'User added to organisation'})

    elif action == 'remove':
        role = UserOrgRole.query.filter_by(user_id=user.id, organisation_id=org.id).one_or_none()
        if not role:
            return jsonify({'error': 'User not in organisation'}), 400

        db.session.delete(role)
        db.session.commit()
        return jsonify({'success': 'User removed from organisation'})


@bp.route('organisation/<organisation_id>/forwarding/tags', methods=['GET'])
@jwt_required()
@require_super_user()
def get_forwarding_tags(organisation_id: str):
    return jsonify(FORWARDING_TAGS)


@bp.route('organisation/<organisation_id>/forwarding', methods=['GET'])
@jwt_required()
@require_super_user()
def get_organisation_forwards(organisation_id: str):
    forwards = EmailTagForward.query.filter_by(organisation_id=organisation_id).all()
    return [forward.json for forward in forwards]


@bp.route('organisation/<organisation_id>/forwarding', methods=['POST', 'DELETE'])
@jwt_required()
@require_super_user()
def manage_organisation_forward(organisation_id: str):
    if request.method == 'POST':
        tag = request.json.get('tag')
        email = request.json.get('email')
        if not tag:
            return jsonify({'error': 'No tag provided'}), 400

        if not email:
            return jsonify({'error': 'No email provided'}), 400

        if tag not in FORWARDING_TAGS:
            return jsonify({'error': 'Invalid tag'}), 400

        forward = EmailTagForward(organisation_id=organisation_id, tag=tag, email=email)
        db.session.add(forward)
        db.session.commit()
        return jsonify({'success': 'Tag added to organisation'})

    elif request.method == 'DELETE':
        _id = request.json.get('id')
        forward = EmailTagForward.query.filter_by(organisation_id=organisation_id, id=_id).one_or_none()
        if not forward:
            return jsonify({'error': 'Tag not found'}), 404

        db.session.delete(forward)
        db.session.commit()
        return jsonify({'success': 'Tag removed from organisation'})


@bp.route('organisation', methods=['POST'])
@jwt_required()
@require_super_user()
def create_organisation():
    user_id = request.json.get('user_id')
    if not user_id:
        user_id = str(current_user.id)

    name = request.json.get('name')
    if not name:
        return jsonify({'error': 'No name provided'}), 400

    website = request.json.get('website')
    if not website:
        return jsonify({'error': 'No website provided'}), 400

    # make sure the website is a domain and not a URL
    if 'http' in website:
        return jsonify({'error': 'Invalid website, please provide just the '
                                 'domain(whatson.ai instead of https://whatson.ai/)'}), 400

    org = create_new_org(user_id, name, website)
    return jsonify({'id': str(org.id),
                    'success': 1})


@bp.route('/organisation/<organisation_id>/sending-domains', methods=['GET', 'POST'])
@jwt_required()
@require_super_user()
def manage_sending_domains(organisation_id: str):
    org = Organisation.query.filter_by(id=organisation_id).one_or_none()
    if not org:
        return jsonify({'error': 'Organisation not found'}), 404

    if request.method == 'GET':
        domains = SendingDomain.query.filter_by(organisation_id=organisation_id).all()
        return jsonify([domain.json for domain in domains])

    elif request.method == 'POST':
        domain = request.json.get('domain')
        resp = sending_service_api.create_domain(domain)
        dns_records = sending_service_api.get_dns_records(domain)

        domain = SendingDomain(
            organisation_id=org.id,
            domain=resp.domain,
            is_verified=resp.dns_verified,
            dns_records=[asdict(r) for r in dns_records]
        )

        db.session.add(domain)
        db.session.commit()

        return jsonify(domain.json)


@bp.route('/organisation/<organisation_id>/sending-domains/<domain_id>/dns-records', methods=['GET'])
@jwt_required()
@require_super_user()
def get_sending_domain_dns_records(organisation_id: str, domain_id: str):
    domain = SendingDomain.query.filter_by(id=domain_id, organisation_id=organisation_id).one_or_none()
    if not domain:
        return jsonify({'error': 'Domain not found'}), 404

    dkim = [record for record in domain.dns_records if 'domainkey' in record['hostname']]
    if dkim:
        dkim = dkim[0]
        # Add the DKIM prefix
        dkim['value'] = f"v=DKIM1; k=rsa; h=sha256; p={dkim['value']}"
    return jsonify(domain.dns_records)


@bp.route('/organisation/<organisation_id>/sending-domains/<domain_id>/verify', methods=['GET'])
@jwt_required()
@require_super_user()
def verify_sending_domain(organisation_id: str, domain_id: str):
    domain = SendingDomain.query.filter_by(id=domain_id, organisation_id=organisation_id).one_or_none()
    if not domain:
        return jsonify({'error': 'Domain not found'}), 404
    # This means it is already verified
    if domain.is_verified:
        return jsonify({'message': 'Domain already verified'}), 200

    resp = sending_service_api.verify_domain(domain.domain, organisation_id)
    errors = [x for x in [resp.mx_error, resp.dkim_error, resp.cname_error] if x]
    if errors:
        return jsonify({'error': ', '.join(errors)}), 400

    domain.is_verified = True
    db.session.add(domain)
    db.session.commit()
    return jsonify({'message': 'Domain verified'}), 200


@bp.route('/organisation/<organisation_id>/email-senders', methods=['GET', 'POST'])
@jwt_required()
@require_super_user()
def manage_email_senders(organisation_id: str):
    org = Organisation.query.filter_by(id=organisation_id).one_or_none()
    if not org:
        return jsonify({'error': 'Organisation not found'}), 404

    if request.method == 'GET':
        senders = EmailSender.query.filter_by(organisation_id=organisation_id).all()
        return jsonify([sender.json for sender in senders])

    elif request.method == 'POST':
        name = request.json.get('name')
        email = request.json.get('email')
        domain_id = request.json.get('domain_id')

        if not name or not email or not domain_id:
            return jsonify({'error': 'Missing required fields'}), 400

        domain = SendingDomain.query.filter_by(id=domain_id, organisation_id=organisation_id).one_or_none()
        if not domain:
            return jsonify({'error': 'Domain not found'}), 404

        # Check if the email is from the domain
        if domain.domain != email.split('@')[-1]:
            return jsonify({'error': 'Email domain does not match domain'}), 400

        sender = EmailSender(
            organisation_id=organisation_id,
            domain_id=domain_id,
            user_id=current_user.id,
            name=name,
            email=email
        )

        db.session.add(sender)
        db.session.commit()

        return jsonify(sender.json)
