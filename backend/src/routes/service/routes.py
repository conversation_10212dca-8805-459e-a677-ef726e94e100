from collections import defaultdict
from io import Bytes<PERSON>

from flask import Blueprint, send_file
from flask import jsonify, request
from sqlalchemy import and_, not_

from src.models import Campaign, Audience, EmailSender, SendingDomain
from src.models import Campaign, Audience, EmailSender, db
from src.functions.service import service_required
from flask_cors import CORS
from src.utils import SERVICE_CORS_ARGS, spaces_client

bp = Blueprint('service', __name__, url_prefix='/service')
CORS(bp, **SERVICE_CORS_ARGS)


@bp.route('/', methods=['GET'])
@service_required()
def authorize():
    return jsonify({'message': 'Authorized!'}), 200


@bp.route('/campaigns', methods=['GET', 'POST'])
@service_required()
def get_campaigns():
    if request.method == 'GET':
        additional_filters = {}
        if request.args.get('active') is True:
            additional_filters['published'] = True
            additional_filters['paused'] = False

        campaign_id = request.args.get('campaign_id')

        if request.args.get('organisation_id'):
            campaigns = Campaign.query.filter_by(organisation_id=request.args.get('organisation_id')).all()
            return jsonify({
                request.args['organisation_id']: [campaign.to_dict() for campaign in campaigns]
            })

        if request.args.get('by_organisation'):

            campaigns = defaultdict(list)
            for campaign in Campaign.query.filter_by(**additional_filters).all():
                campaigns[str(campaign.organisation_id)].append(campaign.to_dict())

            return jsonify(campaigns)

        if not campaign_id:
            try:
                page = int(request.args.get('page', 1))
                per_page = int(request.args.get('per_page', 10))
            except ValueError:
                return jsonify({'message': 'Invalid page or per_page value.',
                                'error': f'Expected Int, got {request.args.get("page")} for page'
                                         f'and {request.args.get("per_page")} for per_page'}), 400

            if request.args.get('active') is True:
                campaigns = Campaign.query.filter(Campaign.paused is False, Campaign.published is True)\
                    .paginate(page=page, per_page=per_page, error_out=False)
            else:
                campaigns = Campaign.query.paginate(page=page, per_page=per_page, error_out=False)

            return jsonify([campaign.to_dict() for campaign in campaigns])

        else:
            campaign = Campaign.query.filter_by(id=campaign_id).one_or_none()
            if not campaign:
                return jsonify({'message': 'Campaign_id not found.'}), 404
            return jsonify(campaign.to_dict())

    elif request.method == 'POST':
        campaign_id = request.json.get('campaign_id')
        if not campaign_id:
            return jsonify({'message': 'Missing campaign_id'}), 400

        paused = request.json.get('paused')
        published = request.json.get('published')

        if paused is None and published is None:
            return jsonify({'message': 'Missing paused or published'}), 400

        campaign = Campaign.query.filter_by(id=campaign_id).one_or_none()
        if not campaign:
            return jsonify({'message': 'Campaign_id not found.'}), 404

        if paused is not None and isinstance(paused, bool):
            campaign.paused = paused
        if published is not None and isinstance(published, bool):
            campaign.published = published

        db.session.add(campaign)
        db.session.commit()

        return jsonify('ok!'), 200


@bp.route('/audiences', methods=['GET'])
@service_required()
def get_audiences():
    audience_id = request.args.get('audience_id')
    if not audience_id:
        try:
            page = int(request.args.get('page', 1))
            per_page = int(request.args.get('per_page', 10))
        except ValueError:
            return jsonify({'message': 'Invalid page or per_page value.',
                            'error': f'Expected Int, got {request.args.get("page")} for page'
                                     f'and {request.args.get("per_page")} for per_page'}), 400

        paginate_args = {'page': page, 'per_page': per_page, 'error_out': False}

        if request.args.get('active') is True:
            audiences = Audience.query.filter(not_(Audience.filters == {})).join(Campaign).filter(
                and_(
                    Campaign.paused.is_(False),
                    Campaign.published.is_(True),
                    Audience.campaigns.isnot(None)
                )
            ).paginate(**paginate_args)
        else:
            audiences = Audience.query.filter(not_(Audience.filters == {})).paginate(**paginate_args)

        return jsonify([audience.json(frontend_filters=False) for audience in audiences])

    audience = Audience.query.filter_by(id=audience_id).one_or_none()
    if not audience:
        return jsonify({'message': 'Audience_id not found.'}), 404
    return jsonify(audience.json(frontend_filters=False))


@bp.route('/senders', methods=['GET'])
@service_required()
def get_sender():
    organisation_id = request.args.get('organisation_id')
    sender_id = request.args.get('sender_id')

    if not organisation_id:
        return jsonify({'message': 'Missing organisation_id'}), 400

    if not sender_id:
        senders = EmailSender.query.filter_by(organisation_id=organisation_id).all()
        return jsonify([sender.service_json for sender in senders])

    else:
        sender = EmailSender.query.filter_by(id=sender_id).one_or_none()

        if not sender:
            return jsonify({'message': 'Sender_id not found.'}), 404

        return jsonify(sender.service_json)

@bp.route('/<organisation_id>/content/image/<image_id>', methods=['GET'])
@service_required()
def get_image(organisation_id: str, image_id: str):
    """
    Wrapper of /organisation/content/image/<image_id> for service
    :param organisation_id: Organisation id
    :param image_id: Image id
    :return: File
    """
    org_bucket = spaces_client.create_organisation_bucket(organisation_id=organisation_id)
    result = org_bucket.get_image_by_id(image_id)
    if not result:
        return jsonify({'message': 'Image not found.'}), 404

    image_bytes, file_ext = result
    img_io = BytesIO(image_bytes)
    return send_file(img_io, mimetype=f'image/{file_ext}')


@bp.route('/audiences/<audience_id>/status', methods=['POST'])
@service_required()
def set_audience_status(audience_id: str):
    """
    Sets the status of an audience
    :param audience_id: ID of the Audience
    """

    status = request.json.get('status')
    prospects_added = request.json.get('prospects_added')
    if prospects_added:
        try:
            prospects_added = int(prospects_added)
        except ValueError:
            return jsonify({'message': f'Invalid prospects_added value. Expected Integer, got {prospects_added}.'}), 400

    if status not in ['new', 'generating', 'generated']:
        return jsonify({'message': f'{status} is not a valid status name. '
                                   f'Please choose [new], [generating] or [generated].'}), 400

    audience = Audience.query.filter_by(id=audience_id).first()
    if not audience:
        return jsonify({'message': 'The specified Audience could not be found.'}), 404

    audience.status = status
    if prospects_added:
        audience.prospects_added = prospects_added

    db.session.add(audience)
    db.session.commit()

    return jsonify({'message': 'Success'}), 200


@bp.route('/campaign/<campaign_id>/status', methods=['POST'])
@service_required()
def set_campaign_status(campaign_id: str):
    """
    Sets the status of an audience
    :param campaign_id: ID of the Campaign
    """

    status = request.json.get('status')

    if status not in ['running', 'completed']:
        return jsonify({'message': f'{status} is not a valid status name. '
                                   f'Please choose [running] or [completed].'}), 400

    campaign = Campaign.query.filter_by(id=campaign_id).first()
    if not campaign:
        return jsonify({'message': 'The specified Campaign could not be found.'}), 404

    campaign.status = status.capitalize()
    db.session.add(campaign)
    db.session.commit()

    return jsonify({'message': 'Success'}), 200
