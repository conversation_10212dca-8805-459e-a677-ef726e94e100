import base64
import uuid
from io import Bytes<PERSON>

from Holmes.campaign.flow import Flow
from Holmes.custom_images.fonts import Font<PERSON>oader
from Holmes.email.email_message import EmailMessage
from flask import Blueprint, send_file
from flask import jsonify, request
from flask_jwt_extended import jwt_required
from src.functions.org import require_role
from Holmes.email import merge_fields
from Holmes.custom_images import CustomImage
from settings import ORG_SETTINGS
from src.models import Organisation
from src.utils import spaces_client, user_cors_args, templates_bucket, populate_flow_to_dict
from flask_cors import CORS

bp = Blueprint('organisation_content', __name__, url_prefix='/organisation')
CORS(bp, **user_cors_args, supports_credentials=True)


@bp.route('/content/email/upload', methods=['POST'])
@jwt_required(fresh=True)
@require_role(ORG_SETTINGS['EDITORS'])
def upload_email():
    org_id = request.json.get('organisation_id')
    email = request.json.get('email')
    try:
        email_message = EmailMessage.from_json(email)
    except Exception as E:
        return jsonify({'message': 'Unable to parse Email object',
                        'error': str(E)}), 400

    org_bucket = spaces_client.create_organisation_bucket(organisation_id=org_id)
    org_bucket.upload_email(email_message, email_message.id)

    return jsonify({'message': f'Successfully uploaded email',
                    'id': email_message.id}), 200



@bp.route('/content/image/upload', methods=['POST'])
@jwt_required(fresh=True)
@require_role(ORG_SETTINGS['EDITORS'])
def upload_image():
    org_id = request.json.get('organisation_id')
    image_b64 = request.json.get('image')  # Assuming image is base64 encoded
    image_type = request.json.get('image_type')

    org_bucket = spaces_client.create_organisation_bucket(organisation_id=org_id)
    image_bytes = BytesIO(image_b64.encode('utf-8'))
    image_id = str(uuid.uuid4())

    org_bucket.upload_image(image_bytes, image_type, image_id)

    return jsonify({'message': f'Successfully uploaded image with ID {image_id}',
                    'id': image_id}), 200


@bp.route('/content/file/upload', methods=['POST'])
@jwt_required(fresh=True)
@require_role(ORG_SETTINGS['EDITORS'])
def upload_file():
    """
    Note: Unused endpoint
    """
    org_id = request.json.get('organisation_id')
    file_bytes = request.json.get('file')  # Assuming file is base64 encoded
    file_type = request.json.get('file_type')

    org_bucket = spaces_client.create_organisation_bucket(organisation_id=org_id)
    file_id = str(uuid.uuid4())

    org_bucket.upload_file(file_bytes, file_type, file_id)

    return jsonify({'message': f'Successfully uploaded file with ID {file_id}',
                    'id': {file_id}}), 200


@bp.route('/content/flow/upload', methods=['POST'])
@jwt_required(fresh=True)
@require_role(ORG_SETTINGS['EDITORS'])
def upload_flow():
    org_id = request.json.get('organisation_id')
    flow_dict = request.json.get('flow')

    org_bucket = spaces_client.create_organisation_bucket(organisation_id=org_id)
    try:
        flow = Flow.from_dict(flow_dict)
    except Exception as E:
        return jsonify({'message': 'Invalid flow.'}), 400

    org_bucket.upload_flow(flow)

    return jsonify({'message': f'Successfully uploaded Flow',
                    'id': flow.id}), 200


@bp.route('/content/email/', methods=['POST'], defaults={'email_id': None})
@bp.route('/content/email/<email_id>', methods=['POST', 'DELETE'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_email(email_id):
    if email_id is not None and request.method == 'POST':
        org_id = request.json.get('organisation_id')
        org_bucket = spaces_client.create_organisation_bucket(organisation_id=org_id)
        try:
            email = org_bucket.get_email_by_id(email_id)
        except Exception as E:
            print(E)
            return jsonify({'message': 'Email not found.'}), 404

        email_dict = email.to_dict()
        email_dict['is_used'] = False
        return jsonify(email_dict), 200
    elif email_id is not None and request.method == 'DELETE':
        org_id = request.args.get('organisation_id')
        org_bucket = spaces_client.create_organisation_bucket(organisation_id=org_id)
        try:
            result = org_bucket.delete_email(email_id)
        except Exception as E:
            print(f"Deleting email error: {E}")
            return jsonify({'message': 'Something went wrong, try again later.'}), 400

        return jsonify({'message': 'Email deleted.'}), 200

    else:
        org_id = request.json.get('organisation_id')
        org_bucket = spaces_client.create_organisation_bucket(organisation_id=org_id)
        emails = org_bucket.get_emails_as_dict()
        [email.update({'is_used': False}) for email in emails]
        return jsonify(emails)


@bp.route('/<organisation_id>/content/custom_images/', methods=['GET', 'POST'])
@bp.route('/<organisation_id>/content/custom_images/<custom_image_id>', methods=['GET'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_custom_image(organisation_id: str, custom_image_id: str = None):
    org_bucket = spaces_client.create_organisation_bucket(organisation_id=organisation_id)
    if custom_image_id is not None:
        custom_image = org_bucket.get_custom_image_by_id(custom_image_id)

        if custom_image is None:
            return jsonify({'message': 'Custom image not found.'}), 404
        return jsonify(custom_image.to_dict()), 200

    if request.method == 'POST':
        try:
            data = request.json
            custom_image = CustomImage.from_dict(data)
        except ValueError as VE:
            return {'message': str(VE)}, 400
        except Exception as E:
            return {'message': 'Something went wrong. Try again later',
                    'error': str(E)}, 400

        org_bucket.upload_custom_image(custom_image)

        return jsonify({'message': f'Successfully uploaded file with ID {custom_image.id}',
                        'id': custom_image.id}), 200

    # GET
    return jsonify([custom_image.to_dict() for custom_image in org_bucket.get_custom_images()]), 200


@bp.route('/<organisation_id>/content/custom_images/fonts', methods=['GET'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_fonts(organisation_id: str):
    fl = FontLoader()
    return jsonify(fl.get_fonts())


@bp.route('/<organisation_id>/content/image/<image_id>', methods=['GET'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_image(organisation_id: str, image_id: str):
    org_bucket = spaces_client.create_organisation_bucket(organisation_id=organisation_id)

    image = org_bucket.get_image_by_id(image_id)
    if image is None:
        return jsonify({'message': 'Image not found.'}), 404

    image_bytes, file_ext = image

    img_io = BytesIO(base64.b64decode(image_bytes))
    response = send_file(img_io, mimetype=f'image/{file_ext}')
    response.headers['Content-Disposition'] = 'inline'
    return response


@bp.route('/content/file/<file_id>', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_file(file_id):
    org_id = request.json.get('organisation_id')

    org_bucket = spaces_client.create_organisation_bucket(organisation_id=org_id)

    file_bytes, file_ext = org_bucket.get_file_by_id(file_id)
    file_io = BytesIO(file_bytes)

    return send_file(file_io, mimetype=f'application/{file_ext}')


@bp.route('/content/images', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_image_templates():
    """
    Note: not in use
    """
    images = templates_bucket.get_images()
    return jsonify(images)


@bp.route('/content/emails', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_email_templates():
    emails = templates_bucket.get_emails_as_dict()
    [email.update({'is_used': False}) for email in emails]
    return jsonify(emails)


@bp.route('/content/custom_images', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_custom_image_templates():
    custom_images = templates_bucket.get_custom_images()
    return jsonify(custom_images)


@bp.route('/content/flows', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_flow_templates():
    organisation_id = request.json.get('organisation_id')
    flows = templates_bucket.get_flows()
    flows = [populate_flow_to_dict(flow, templates_bucket) for flow in flows]

    if organisation_id:
        org = Organisation.query.get(organisation_id)
        org_bucket = spaces_client.create_organisation_bucket(organisation_id=org)
        flows.extend([populate_flow_to_dict(flow, org_bucket) for flow in org_bucket.get_flows()])

    return jsonify([flow for flow in flows])


@bp.route('/<organisation_id>/content/merge_fields', methods=['GET'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_merge_fields(organisation_id: str):
    all_merge_fields = merge_fields.get_general()
    org_merge_fields = merge_fields.get_organization(organisation_id)
    all_merge_fields.extend(org_merge_fields)
    return jsonify(all_merge_fields)


@bp.route('/content/audience_merge_fields', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_audience_merge_fields():
    return jsonify({'message': 'Waiting for merge fields based on Audience to be fixed.'})

