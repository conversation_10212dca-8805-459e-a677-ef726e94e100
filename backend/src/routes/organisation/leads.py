from dataclasses import asdict
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from Holmes.audience.prospect import Prospect
from bson import ObjectId
from settings import ORG_SETTINGS
from flask import jsonify, request, Blueprint, Response
from flask_jwt_extended import jwt_required
from src.functions.org import require_role
from src.utils import mail_database, prospects_database
from src.utils import block_list_factory
from Holmes.audience.utils import prospects_to_csv
from src.utils import user_cors_args
from flask_cors import CORS

bp = Blueprint('leads', __name__, url_prefix='/organisation/<organisation_id>/')
CORS(bp, **user_cors_args, supports_credentials=True)


@bp.route('/leads', methods=['GET'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def leads(organisation_id: str):
    page_number = int(request.args.get('page_number', 1))
    page_size = int(request.args.get('page_size', 10))
    audience_id = request.args.get('audience_id', None)

    shard = mail_database[organisation_id]
    _prospects = [Prospect.from_record_doc(doc) for doc in shard.get_leads(page_number, page_size, audience_id)]

    return jsonify({
        'data': [asdict(prospect) for prospect in _prospects],
        'total_count': shard.get_leads(audience_id=audience_id, count=True)})


def get_prospects_wrapper(shard, page_number, page_size, audience_id, responding_only, count):
    return shard.get_prospects(page_number, page_size, audience_id, responses=responding_only, count=count)


@bp.route('/prospects', methods=['GET'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def prospects(organisation_id: str):
    page_number = int(request.args.get('page_number', 1))
    page_size = int(request.args.get('page_size', 10))
    audience_id = request.args.get('audience_id', None)
    responding_only = request.args.get('responded', False)

    shard = mail_database[organisation_id]

    with ThreadPoolExecutor() as executor:
        # Execute both calls
        future_prospects = executor.submit(get_prospects_wrapper, shard, page_number,
                                           page_size, audience_id, responding_only, False)
        future_count = executor.submit(get_prospects_wrapper, shard, page_number,
                                       page_size, audience_id, responding_only, True)

        # Execute the TPE
        executor.shutdown(wait=True)

        # Get the results
        _prospects = future_prospects.result()
        count = future_count.result()

    return jsonify({
        'data': [asdict(Prospect.from_record_doc(prospect)) for prospect in _prospects],
        'total_count': count})


@bp.route('/leads/export', methods=['GET'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def export_leads(organisation_id: str):
    audience_id = request.args.get('audience_id', None)

    shard = mail_database[organisation_id]
    prospects = [Prospect.from_record_doc(doc) for doc in shard.get_leads(1, 9999999, audience_id)]

    csvfile = prospects_to_csv(prospects)
    response = Response(csvfile, content_type='text/csv')
    response.headers.set('Content-Disposition', 'attachment',
                         filename=f'{datetime.today().strftime("%Y-%M-%d")}.csv')


@bp.route('/leads/<objectid:_id>/to_blocklist', methods=['PUT'])
@jwt_required()
@require_role(ORG_SETTINGS['EDITORS'])
def to_blocklist(organisation_id: str, _id: ObjectId):
    org_collection = prospects_database[organisation_id]
    prospect = org_collection.pop_prospect(_id)
    if not prospect:
        return jsonify({'message': 'Lead was not found.'}), 404

    org_blocklist = block_list_factory.get_block_list(organisation_id)
    result = org_blocklist.add_prospects([prospect])
    if result:
        return jsonify({'message': 'Successfully moved lead to blocklist.'}), 200
    else:
        return jsonify({'message': 'Something went wrong moving your lead. Try again later.'}), 400
