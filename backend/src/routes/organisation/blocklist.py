import base64
import csv
from dataclasses import asdict
from datetime import datetime
from io import String<PERSON>

from Holmes.email.io.analytics import Analytics
from bson import ObjectId

from settings import ORG_SETTINGS
from src.models import Organisation, User, UserOrg<PERSON>ole
from flask import Blueprint
from flask import jsonify, request
from flask_jwt_extended import jwt_required, current_user
from src.functions.org import create_new_org, require_role, delete_org, get_unaccepted_roles, add_user_to_org, \
    OrganisationLimitExceeded, OrganisationAlreadyExists
from src.utils import mail_database, prospects_database
from src.utils import block_list_factory
from Holmes.sources.blocklist.utils import parse_imported_contacts
from src.utils import user_cors_args
from flask_cors import CORS

bp = Blueprint('blocklist', __name__, url_prefix='/organisation/<organisation_id>/')
MAX_FILE_SIZE = 10 * 1024 * 1024
CORS(bp, **user_cors_args, supports_credentials=True)

def get_file_size(file):
    # Get the file size in bytes
    file.seek(0, 2)  # Move to the end of the file
    file_size = file.tell()  # Get the current position (file size)
    file.seek(0)  # Reset the file position to the beginning
    return file_size


@bp.route('/blocklist', methods=['GET', 'POST'])
@jwt_required()
@require_role(ORG_SETTINGS['EDITORS'])
def blocklist(organisation_id: str):
    if request.method == 'GET':
        page_number = int(request.args.get('page_number', 1))
        page_size = int(request.args.get('page_size', 10))

        block_list = block_list_factory.get_block_list(organisation_id)
        data = block_list.get_blocked_prospects(page_size=page_size, page_number=page_number)
        prospects = data['data']
        total_count = data['total_count']
        return jsonify({
            'data': prospects,
            'total_count': total_count})

    elif request.method == 'POST':
        try:
            base64_data = request.json.get('data')
            if not base64_data:
                return jsonify({'message': 'No data provided.'}), 400

            csv_data = base64.b64decode(base64_data).decode('utf-8')
            csv_file = StringIO(csv_data)

            if get_file_size(csv_file) > MAX_FILE_SIZE:
                return jsonify({'message': 'File size exceeds the limit.'}), 400

            delimiter = csv.Sniffer().sniff(csv_file.read(1024)).delimiter
            csv_file.seek(0)

            reader = csv.reader(csv_file, delimiter=delimiter)

        except (IOError, ValueError, EOFError):
            return jsonify({'message': 'Unable to parse CSV file'}), 400

        try:
            contacts = parse_imported_contacts(reader)
        except ValueError as Error:
            return jsonify({'message': str(Error)}), 400

        block_list = block_list_factory.get_block_list(organisation_id)
        result = block_list.add_imported_contacts(contacts)

        if result is True:
            return jsonify({'message': 'Successfully uploaded all contacts.'})

        else:
            return jsonify({'message': 'We were unable to correctly upload all contacts. Please try again later.'})


@bp.route('/blocklist/<objectid:_id>/to_leads', methods=['PUT'])
@jwt_required()
@require_role(ORG_SETTINGS['EDITORS'])
def to_leads(organisation_id: str, _id: ObjectId):
    org_blocklist = block_list_factory.get_block_list(organisation_id)
    prospect = org_blocklist.pop_prospect(_id)

    if not prospect:
        return jsonify({'message': 'Blocked prospect was not found.'}), 404

    org_collection = prospects_database[organisation_id]
    result = org_collection.add_prospects([prospect])
    if result:
        return jsonify({'message': 'Successfully moved lead to blocklist.'}), 200
    else:
        return jsonify({'message': 'Something went wrong moving your lead. Try again later.'}), 400
