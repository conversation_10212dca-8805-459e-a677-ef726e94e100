from datetime import datetime
from Holmes.email.io.analytics import Analytics
from settings import ORG_SETTINGS
from src.models import Organisation, User, UserOrg<PERSON>ole
from flask import Blueprint
from flask import jsonify, request
from flask_jwt_extended import jwt_required, current_user
from src.functions.org import create_new_org, require_role, delete_org, get_unaccepted_roles, add_user_to_org, \
    OrganisationLimitExceeded, OrganisationAlreadyExists
from src.utils import mail_database, inbox_manager
from src.utils import user_cors_args
from flask_cors import CORS

bp = Blueprint('organisation_inbox', __name__, url_prefix='/organisation/')
CORS(bp, **user_cors_args, supports_credentials=True)


@bp.route('inbox/notifications', methods=['POST'])
@jwt_required()
def get_notifications():
    org_id = request.json.get('organisation_id')
    campaign_id = request.json.get('campaign_id')
    audience_id = request.json.get('audience_id')
    if not org_id:
        return jsonify({'message': 'Please add an organisation_id to your request body.'}), 400

    read = request.json.get('read')
    archived = request.json.get('archived')
    org_inbox = inbox_manager[org_id]
    notifications = org_inbox.get_notifications(read, archived, campaign_id=campaign_id, audience_id=audience_id)

    return jsonify([notification.as_dict() for notification in notifications]), 200


@bp.route('<organisation_id>/inbox/<notification_id>', methods=['PUT', 'DELETE'])
@jwt_required()
@require_role(ORG_SETTINGS['EDITORS'])
def update_notification(organisation_id: str, notification_id: str):
    org_inbox = inbox_manager[organisation_id]
    if request.method == 'PUT':
        read = request.json.get('read')
        archived = request.json.get('archived')
        result = org_inbox.update_notification(notification_id, read, archived)

        if result:
            return jsonify({'message': 'Notification updated.'}), 200
        else:
            return jsonify({'message': 'Failed to update notification.'}), 400

    if request.method == 'DELETE':
        try:
            result = org_inbox.delete_notification(notification_id)
        except ValueError:
            return jsonify({'message': 'Notification not found.'}), 404

        if result:
            return jsonify({'message': 'Notification deleted.'}), 200
        else:
            return jsonify({'message': 'Failed to delete notification.'}), 400


@bp.route('<organisation_id>/inbox/<n_property>', methods=['GET'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_unread_notifications(organisation_id: str, n_property: str):
    """
    Get the number of unread notifications for a given organisation, optionally filtered by campaign.
    :param organisation_id: ID of the organisation
    :param n_property: Property to filter by, either 'unread', 'archived' or 'read'
    :param campaign_id: ID of the campaign to filter by
    :return: Dictionary containing count of notifications
    """
    if n_property not in ['unread', 'archived', 'read']:
        return jsonify({'message': 'Invalid property, please use unread/archived/read.'}), 400

    data = {
        'campaign_id': request.args.get('campaign_id')
    }

    if n_property == 'unread':
        data['read'] = False
    else:
        data[n_property] = True

    org_inbox = inbox_manager[organisation_id]
    result = org_inbox.count_notifications(**data)
    return jsonify({'count': result}), 200

