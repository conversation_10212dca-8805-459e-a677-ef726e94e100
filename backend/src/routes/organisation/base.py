from datetime import datetime
from Holmes.email.io.analytics import Analytics
from settings import ORG_SETTINGS
from src.models import Organisation, User, UserOrgR<PERSON>, EmailSender
from flask import Blueprint
from flask import jsonify, request
from flask_jwt_extended import jwt_required, current_user
from src.functions.org import create_new_org, require_role, delete_org, get_unaccepted_roles, add_user_to_org, \
    OrganisationLimitExceeded, OrganisationAlreadyExists
from src.utils import mail_database
from src.utils import user_cors_args
from flask_cors import CORS

bp = Blueprint('organisation', __name__, url_prefix='/organisation')
CORS(bp, **user_cors_args, supports_credentials=True)


@bp.route('/invitations', methods=['GET'])
@jwt_required()
def list_invitations():
    user_id = current_user.id
    user_roles = get_unaccepted_roles(user_id)
    invites = []
    for user_role in user_roles:
        # fixme: should this logic be happening outside the endpoint func?
        inviting_org = Organisation.query.filter_by(id=user_role.organisation_id).one_or_none()
        if inviting_org:
            invites.append({
                'organisation_name': inviting_org.org_name,
                'organisation_website': inviting_org.website,
                'role': user_role.role,
                'invited_at': user_role.created_at

            })
    return jsonify(invites), 200


@bp.route('/invite_user', methods=['POST'])
@jwt_required()
@require_role(['admin', 'owner'])
def invite_user():
    user_id = current_user.id
    org_id = request.json.get('organisation_id')
    invited_email = request.json.get('email')
    invited_role = request.json.get('role')

    if current_user.is_superuser:
        accepted = True
    else:
        accepted = False

    curr_user_role = UserOrgRole.query.filter_by(organisation_id=org_id, user_id=user_id)
    if not curr_user_role:
        return jsonify({'message': 'Something went wrong. Please try again later'}), 403

    if invited_role == 'owner':
        return jsonify({'message': 'You currently cannot transfer ownership of an Organisation.'}), 501

    elif invited_role == 'admin':
        if curr_user_role.role == 'owner':
            add_user_to_org(org_id, user_id, invited_role, accepted=accepted)
        else:
            return jsonify({'message': 'You currently cannot transfer ownership of an Organisation.'}), 501

    invited_user = User.query.filter_by(email=invited_email).one_or_none()
    if not invited_user:
        # todo: send email invite here
        return jsonify({'message': f'Sent an invitation to {invited_email}'}), 200

    add_user_to_org(org_id, user_id, invited_role, accepted=accepted)

    return jsonify({'message': f'Sent an invitation to {invited_email}.'}), 200


@bp.route('/create', methods=['POST'])
@jwt_required(fresh=True)
def create_organisation():
    owner_id = current_user.id
    name = request.json.get('org_name')
    website = request.json.get('org_website')

    try:
        create_new_org(user_id=owner_id, org_name=name, org_domain=website)
    except (OrganisationLimitExceeded, OrganisationAlreadyExists) as OrgExc:
        return jsonify({'message': str(OrgExc)}), 403

    return jsonify({'message': f'Succesfully created organisation {name}'}), 200


@bp.route('/analytics', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_analytics():
    org_id = request.json.get('organisation_id')
    start_str = request.json.get('start')
    end_str = request.json.get('end')

    if not start_str or not end_str:
        return jsonify({'message': 'Please give a start and end date string.'})

    try:
        start = datetime.strptime(start_str, '%Y-%m-%d')
        end = datetime.strptime(end_str, '%Y-%m-%d')
    except ValueError:
        return jsonify({'message': 'Please supply date strings formatted as 2022-12-31'})

    to_analyze = request.json.get('to_analyze', None)

    collection = mail_database.get_collection(org_id)
    analytics = Analytics(collection)
    analytics_data = analytics.get_analytics(start, end, to_analyze=to_analyze)
    for date, value in analytics_data.items():
        if analytics_data.get(date):
            analytics_data[date] += value
        else:
            analytics_data[date] = value

    return jsonify(analytics_data)


@bp.route('/delete', methods=['DELETE'])
@jwt_required(refresh=True)
@require_role(['owner'])
def delete_organisation():
    delete_org(request.json.get('organisation_id'))
    return jsonify({'message': f'Successfully deleted organisation.'}), 200



@bp.route('/<organisation_id>/senders', methods=['GET'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_senders(organisation_id):
    senders = EmailSender.query.filter_by(organisation_id=organisation_id).all()
    return jsonify([sender.json for sender in senders])


@bp.route('/<organisation_id>/balance', methods=['GET'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_balance(organisation_id: str):
    org = Organisation.query.filter_by(id=organisation_id).one_or_none()
    wallet = org.wallet
    if not wallet:
        return jsonify({'balance': 0})
    return jsonify({'balance': wallet.balance})

