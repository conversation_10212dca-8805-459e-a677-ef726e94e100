from collections import OrderedDict

from Holmes.campaign import EmailAction
from flask import Blueprint, Response
from flask import jsonify, request
from flask_jwt_extended import jwt_required
from src.functions.org import require_role, has_campaign_id
from datetime import datetime, timedelta
from settings import ORG_SETTINGS
from Holmes.email.io.analyticsmanager import Analy<PERSON><PERSON>ana<PERSON>
from src.models import Campaign
from src.utils import mail_database, spaces_client
from io import StringIO
from src.utils import user_cors_args
from flask_cors import CORS

bp = Blueprint('campaign_analytics', __name__, url_prefix='/campaign/analytics')
CORS(bp, **user_cors_args, supports_credentials=True)


def analytics_to_csv_string(analytics: dict) -> StringIO:
    csv_file = StringIO()
    headers = ['date', 'blocked_count', 'bounced_count', 'clicked_count', 'delivered_count', 'opened_count', 'responses_count']
    csv_file.write(','.join(headers) + '\n')
    for date, counts in analytics.items():
        row = [date]
        for key in headers[1:]:
            try:
                row.append(str(counts[key]))
            except KeyError:
                row.append(f'{0}')
        csv_file.write(','.join(row) + '\n')
    return csv_file


def streamline_data(analytics, target_length=14):
    # If the data is already less than or equal to the target length, return it as is
    if len(analytics) <= target_length:
        data = OrderedDict()
        [data.update({i: analytics[i]}) for i in sorted(analytics.keys())]
        return OrderedDict(sorted(analytics.items(), key=lambda x: x[0]))

    # Get all the dates in sorted order
    sorted_dates = sorted(analytics.keys())

    # Calculate the size of each interval
    total_dates = len(sorted_dates)
    interval_size = max(1, total_dates // target_length)

    streamlined = {}
    for i in range(target_length):
        # Calculate the start and end indices for this interval
        start_index = i * interval_size
        end_index = min(start_index + interval_size, total_dates)

        # Adjust the last interval to include any remaining dates
        if i == target_length - 1:
            end_index = total_dates

        # Extract the dates for this interval
        interval_dates = sorted_dates[start_index:end_index]

        # Skip if the interval is empty (should not happen with this approach)
        if not interval_dates:
            continue

        # Aggregate the data for these dates
        aggregated_data = {
            "bounced_count": sum(analytics[date]["bounced_count"] for date in interval_dates),
            "clicked_count": sum(analytics[date]["clicked_count"] for date in interval_dates),
            "delivered_count": sum(analytics[date]["delivered_count"] for date in interval_dates),
            "opened_count": sum(analytics[date]["opened_count"] for date in interval_dates),
        }

        # Use the first date in the interval as the key for the streamlined data
        streamlined[interval_dates[0]] = aggregated_data

    #streamlined_dates = sorted(streamlined.keys())
    #ordered_analytics = {sd: streamlined[sd] for sd in streamlined_dates}
    data = OrderedDict()
    [data.update({i: streamlined[i]}) for i in sorted(streamlined.keys())]
    return data


@bp.route('', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_analytics():
    org_id = request.json.get('organisation_id')
    try:
        start = datetime.strptime(request.json['start'], '%Y-%m-%d')
        end = datetime.strptime(request.json['end'], '%Y-%m-%d')
    except ValueError:
        return jsonify({'message': 'Incorrect date string formatting. Please format like %Y-%m-%d/2022-12-31'}), 400
    except KeyError:
        return jsonify({'message': 'Missing start and end date-strings'}), 400

    collection = mail_database.get_collection(org_id)
    data = AnalyticsManager.get_global_insights(collection, start, end)
    return jsonify(streamline_data(data))


@bp.route('/export', methods=['GET'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def export_analytics():
    org_id = request.args.get('organisation_id')

    try:
        start = datetime.strptime(request.args['start'], '%Y-%m-%d')
        end = datetime.strptime(request.args['end'], '%Y-%m-%d')
    except ValueError:
        return jsonify({'message': 'Incorrect date string formatting. Please format like %Y-%m-%d/2022-12-31'}), 400
    except KeyError:
        return jsonify({'message': 'Missing start and end date-strings'}), 400

    collection = mail_database.get_collection(org_id)
    analytics_data = AnalyticsManager.get_global_insights(collection, start, end)
    csv_data = analytics_to_csv_string(analytics_data)
    response = Response(csv_data, content_type='text/csv')

    start_str = start.strftime('%Y-%m-%d')
    end_str = end.strftime('%Y-%m-%d')
    response.headers.set('Content-Disposition', 'attachment',
                         filename=f'Analytics {start_str}/{end_str}.csv')

    return response


@bp.route('/general', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_general_analytics():
    org_id = request.json.get('organisation_id')
    start_str = request.json.get('start')
    end_str = request.json.get('end')
    campaign_id = request.json.get('campaign_id', None)

    if start_str and end_str:
        try:
            start = datetime.strptime(start_str, '%Y-%m-%d')
            end = datetime.strptime(end_str, '%Y-%m-%d')
        except ValueError:
            return jsonify({'message': 'Start/end date strings incorrectly formatted. Please use "YYYY-MM-DD"'}), 400
    else:
        start, end = datetime.today(), datetime.today() - timedelta(days=7)

    if campaign_id:
        campaign = Campaign.query.filter_by(id=campaign_id, organisation_id=org_id).one_or_none()
        if not campaign:
            return jsonify({'message': 'Campaign not found.'}), 404

    collection = mail_database[org_id]
    data = AnalyticsManager.get_campaign_insights(collection, start, end, campaign_id)

    return jsonify(data)


@bp.route('/emails', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_email_analytics():
    org_id = request.json.get('organisation_id')
    start_str = request.json.get('start')
    end_str = request.json.get('end')
    campaign_id = request.json.get('campaign_id', None)

    if campaign_id:
        campaign = Campaign.query.filter_by(id=campaign_id, organisation_id=org_id).one_or_none()
        if not campaign:
            return jsonify({'message': 'Campaign not found.'}), 404

    if start_str and end_str:
        try:
            start = datetime.strptime(start_str, '%Y-%m-%d')
            end = datetime.strptime(end_str, '%Y-%m-%d')
        except ValueError:
            return jsonify({'message': 'Start/end date strings incorrectly formatted. Please use "YYYY-MM-DD"'}), 400
    else:
        return jsonify({'message': 'Please supply a start and end string'}), 400

    campaign_id = request.json.get('campaign_id', None)
    if not campaign_id:
        return jsonify({'message': 'Missing Campaign ID'}), 400

    campaign = Campaign.query.filter_by(id=campaign_id, organisation_id=org_id).one_or_none()
    if not campaign:
        return jsonify({'message': 'Campaign not found.'}), 404

    collection = mail_database.get_collection(org_id)
    emails_id_and_name = {}
    if campaign.flow:
        [emails_id_and_name.update({block.mail_id: block.name}) for block in campaign.flow.blocks if isinstance(block, EmailAction)]

    # note: we are no longer trying to add name.
    data = AnalyticsManager.get_emails_insights(collection, start, end, emails_id_and_name, campaign_id)
    return jsonify(data)


# Get events for a specific campaign
@bp.route('analytics/events', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_events():
    # pretty sure this entire function is not used at all.
    return 'ok', 200
    #org_id = request.json.get('organisation_id')
    #try:
    #    start = datetime.strptime(request.json['start'], '%Y-%m-%d')
    #    end = datetime.strptime(request.json['end'], '%Y-%m-%d')
    #except ValueError:
    #    return jsonify({'message': 'Incorrect date string formatting. Please format like %Y-%m-%d/2022-12-31'}), 400
    #except KeyError:
    #    return jsonify({'message': 'Missing start and end date-strings'}), 400
    #
    #end = end + timedelta(days=1)
    #
    #event_types = request.json.get('event_types', None)
    #campaign_id = request.json.get('campaign_id', None)
    #block_id = request.json.get('block_id', None)
    #
    #collection = mail_database.get_collection(org_id)
    #analytics = Analytics(collection)
    #events = analytics.get_events_count(start, end, event_types=event_types, campaign_id=campaign_id, block_id=block_id)
    #
    #return jsonify(events)


# Get responses for a specific campaign
@bp.route('analytics/responses', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_responses():
    # this is unused too im pretty sure
    return '0k', 200
    #org_id = request.json.get('organisation_id')
    #try:
    #    start = datetime.strptime(request.json['start'], '%Y-%m-%d')
    #    end = datetime.strptime(request.json['end'], '%Y-%m-%d')
    #except ValueError:
    #    return jsonify({'message': 'Incorrect date string formatting. Please format like %Y-%m-%d/2022-12-31'}), 400
    #except KeyError:
    #    return jsonify({'message': 'Missing start and end date-strings'}), 400
    #
    #end = end + timedelta(days=1)
    #
    #campaign_id = request.json.get('campaign_id', None)
    #block_id = request.json.get('block_id', None)
    #
    #collection = mail_database.get_collection(org_id)
    #analytics = Analytics(collection)
    #responses = analytics.get_responses_count(start, end, campaign_id=campaign_id, block_id=block_id)
    #
    #return jsonify(responses)


# Get outreaches for a specific campaign
@bp.route('analytics/outreaches', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_outreaches():
    # this is unused too im pretty sure
    return 'ok', 200
    #org_id = request.json.get('organisation_id')
    #try:
    #    start = datetime.strptime(request.json['start'], '%Y-%m-%d')
    #    end = datetime.strptime(request.json['end'], '%Y-%m-%d')
    #except ValueError:
    #    return jsonify({'message': 'Incorrect date string formatting. Please format like %Y-%m-%d/2022-12-31'}), 400
    #except KeyError:
    #    return jsonify({'message': 'Missing start and end date-strings'}), 400
    #
    #end = end + timedelta(days=1)
    #
    #campaign_id = request.json.get('campaign_id', None)
    #block_id = request.json.get('block_id', None)
    #
    #collection = mail_database.get_collection(org_id)
    #analytics = Analytics(collection)
    #outreaches = analytics.get_outreaches_count(start, end, campaign_id=campaign_id, block_id=block_id)
    #
    #return jsonify(outreaches)


@bp.route('/compare', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def compare_campaigns():
    org_id = request.json.get('organisation_id')
    try:
        start = datetime.strptime(request.json['start'], '%Y-%m-%d')
        end = datetime.strptime(request.json['end'], '%Y-%m-%d')
    except ValueError:
        return jsonify({'message': 'Incorrect date string formatting. Please format like %Y-%m-%d/2022-12-31'}), 400
    except KeyError:
        return jsonify({'message': 'Missing start and end date-strings'}), 400

    end = end + timedelta(days=1)

    campaign_ids = request.json.get('campaign_ids', None)
    if not campaign_ids:
        return jsonify({'message': 'Please supply an array of campaign_ids in JSON body.'}), 400

    for campaign_id in campaign_ids:
        if not has_campaign_id(org_id, campaign_id):
            return jsonify({'message': f'Campaign {campaign_id} was not found.'}), 404

    to_compare = request.json.get('to_compare', None)
    if not to_compare or to_compare not in 'opened clicked bounced responses outreaches':
        return jsonify({'message': 'Please supply one of [opened, clicked, bounced, responses, outreaches]'
                                   'as to_compare in JSON body'})

    collection = mail_database.get_collection(org_id)
    data = AnalyticsManager.compare(collection, start, end, campaign_ids, to_compare)
    return jsonify(data)


# Get specific Records with Response object for a specific campaign
@bp.route('analytics/records', methods=['POST'])
@jwt_required()
@require_role(ORG_SETTINGS['VIEWERS'])
def get_record():
    # unused too!
    return 'ok', 200
    #org_id = request.json.get('organisation_id')
    #record_id = request.json.get('record_id')
    #record = mail_database.get_record(record_id, org_id)
    #return jsonify(record.to_dict())
