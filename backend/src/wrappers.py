import requests as r
from requests import Response


class MoriartyWrapper:
    def __init__(self, moriarty_url: str, api_key: str):
        self.moriarty_url = moriarty_url
        self.api_key = api_key
        if not self._check_available():
            raise ConnectionError(f'Moriarty is not available on url {self.moriarty_url}.')

        if not self._check_api_key():
            raise ConnectionError(f'Moriarty API key is not valid.')


    def _request(self, method: str, endpoint: str, **kwargs) -> Response:
        return r.request(method, f'{self.moriarty_url}/{endpoint}', headers=self._auth, **kwargs)

    def _check_available(self) -> bool:
        return self._request('GET', '').status_code == 200

    def _check_api_key(self) -> bool:
        return self._request('GET', 'auth').status_code == 200

    @property
    def _auth(self) -> dict:
        return {'Authorization': f'Bearer {self.api_key}'}

