import json
from logging import error
from os import environ
from typing import Any, List
from uuid import UUID
import redis
from Holmes.campaign.flow import Flow
from flask.json.provider import <PERSON><PERSON><PERSON>rovider
from whatson_wrappers.sendingservice.sparkpost_wrapper import SPSendingService
from settings import MAILDB_URL
from Holmes.email.io import MailDatabase
from Holmes.sources.inbox import InboxManager
from Holmes.sources.content import SpacesClient, OrganisationBucket
from Holmes.sources.blocklist import BlockListFactory
from Holmes.sources.prospects import ProspectDatabase
from settings import SPACES_SETTINGS
from datetime import datetime, date, timedelta
from bson import ObjectId
from werkzeug.routing import BaseConverter
from json import loads, <PERSON><PERSON><PERSON>nco<PERSON>
from math import isnan

from src.models import Organisation, DataPoint

SERVICE_CORS_ARGS = {
    'origins': '*',
}

DEV_ORIGINS = ['https://app.whatson.test', 'https://whatson.akker.dev',
               'https://api-dev.whatson.ai', 'https://app-dev.whatson.ai', 'http://localhost:3000']

STAGING_ORIGINS = ['https://api-staging.whatson.ai', 'https://app-staging.whatson.ai']

PROD_ORIGINS = ['https://app.whatson.ai', 'https://api.whatson.ai']


if not environ['DB_NAME'] == 'whatson_test':
    mail_database = MailDatabase(MAILDB_URL)
    inbox_manager = InboxManager(MAILDB_URL)
    prospects_database = ProspectDatabase(MAILDB_URL)
    spaces_client = SpacesClient(**SPACES_SETTINGS)
    templates_bucket = spaces_client.create_organisation_bucket('whatson-contentbase')
    block_list_factory = BlockListFactory(MAILDB_URL)
    sending_service_api = SPSendingService(
        base_url=environ['SENDING_SERVICE_URL'],
        api_key=environ['SENDING_SERVICE_KEY']
    )
else:
    mail_database = None
    inbox_manager = None
    prospects_database = None
    # the rest of these variables are tested in the Holmes repo
    spaces_client = SpacesClient(**SPACES_SETTINGS)
    templates_bucket = None
    block_list_factory = None
    sending_service_api = None



def populate_flow_to_dict(flow: Flow, bucket: OrganisationBucket, org: Organisation = None) -> dict:
    if isinstance(flow, str):
        flow = Flow.from_dict(loads(flow))

    flow_dict = flow.to_dict()
    for block in flow_dict['blocks']:
        if block.get('action') and block['action'] == 'email':
            mail_id = block.pop('mail_id')
            email = bucket.get_email_by_id(mail_id)
            if email:
                block['email'] = email.to_dict()
            else:
                email = templates_bucket.get_email_by_id(mail_id)
                if email:
                    block['email'] = email.to_dict()

            sender_id = block.pop('sender_id')
            if sender_id and org:
                sender = [sender for sender in org.senders if str(sender.id) == sender_id]
                if sender:
                    block['sender'] = sender[0].json
                else:
                    block['sender'] = {'id': sender_id, 'email': None, 'name': None}

    return flow_dict


def nan_remover(obj):
    if isinstance(obj, float) and isnan(obj):
        return None
    if isinstance(obj, dict):
        return {k: nan_remover(v) for k, v in obj.items()}
    if isinstance(obj, (list, tuple, set)):
        return [nan_remover(x) for x in obj]
    return obj


class CustomJSONEncoder(JSONEncoder):
    def default(self, o):
        if isinstance(o, (datetime, date)):
            return o.isoformat()
        if isinstance(o, ObjectId):
            return str(o)
        if isinstance(o, UUID):
            return str(o)
        if isinstance(o, float) and isnan(o):
            return None
        else:
            return super().default(o)


class CustomJSON(JSONProvider):
    def dumps(self, obj, **kwargs):
        return json.dumps(nan_remover(obj), **kwargs, cls=CustomJSONEncoder)

    def loads(self, s: str | bytes, **kwargs):
        return json.loads(s, **kwargs)


class ObjectIdConverter(BaseConverter):
    def to_python(self, value):
        return ObjectId(value)

    def to_url(self, value):
        return str(value)


class RedisManager:
    def __init__(self, connection_uri: str):
        self.client = redis.StrictRedis.from_url(connection_uri)

    def get(self, key: str) -> str:
        return self.client.get(key)

    def set(self, key: str, value: Any) -> None:
        self.client.set(key, value, ex=timedelta(hours=1))


class MockRedisManager:
    def __init__(self, **kwargs):
        self.data = {}

    def get(self, key: str) -> str:
        return self.data.get(key)

    def set(self, key: str, value: Any) -> None:
        self.data[key] = value


class DataPointValidator:
    @classmethod
    def validate_datapoints(cls, organisation_id: str, slug_names: List[str]) -> (bool, str):
        organisation = Organisation.query.filter_by(id=organisation_id).one_or_none()
        if not organisation:
            raise ValueError(f'No organisation found by id {organisation_id}')

        if organisation.current_package.name == 'pro':
            return True, ""

        for slug_name in slug_names:
            dp = DataPoint.query.filter_by(slug_name=slug_name).one_or_none()
            if not dp:
                return False, f"Datapoint {slug_name} was not found."
            if dp.package.name == 'pro':
                return False, f"Datapoint {slug_name} is not available in the current package."

        return True, ""


try:
    redis_manager = RedisManager(environ['REDIS_URL'])
except KeyError:
    error("No Redis credentials found, continuing with mock Redis Manager.")
    redis_manager = MockRedisManager()


if environ['ENVIRONMENT'] in ['development', 'testing']:
    user_cors_args = {
        'origins': DEV_ORIGINS,
    }

elif environ['ENVIRONMENT'] == 'staging':
    user_cors_args = {
        'origins': STAGING_ORIGINS,
    }

else:
    user_cors_args = {
        'origins': PROD_ORIGINS,
    }
