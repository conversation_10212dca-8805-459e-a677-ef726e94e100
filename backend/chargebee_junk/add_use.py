import chargebee
import json
from datetime import datetime

api_key = 'test_wWAKscu2uJsBcd0BA6uQ2kqXBLoy1xSBP0'
site = 'whatson-test'
chargebee.configure(api_key, site)
result = chargebee.Usage.create("BTTzRcTYU5gHx3e3", {
    "item_price_id": "Agency-pay-per-use-EUR-Monthly",
    "usage_date": 1678801749,
    "note": "Day 1 usage from __test__XpbBrciSCKqKUcN",
    "quantity": "5"
})
usage = result.usage
print(result, usage)
