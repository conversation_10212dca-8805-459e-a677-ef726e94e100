from pprint import pprint

import chargebee
import json

api_key = 'test_wWAKscu2uJsBcd0BA6uQ2kqXBLoy1xSBP0'
site = 'whatson-test'
chargebee.configure(api_key, site)
result = chargebee.Subscription.update_for_items("BTM4o1TYTxFrIB7r", {
    "invoice_immediately": True,
    "subscription_items": [
        {
            "item_price_id": "Large-user-EUR"
        }]
})
subscription = result.subscription
customer = result.customer
card = result.card
invoice = result.invoice
unbilled_charges = result.unbilled_charges
credit_notes = result.credit_notes
pprint(vars(subscription))
