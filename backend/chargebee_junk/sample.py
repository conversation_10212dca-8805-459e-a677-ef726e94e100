import chargebee
import json

api_key = 'test_wWAKscu2uJsBcd0BA6uQ2kqXBLoy1xSBP0'
site = 'whatson-test'
chargebee.configure(api_key, site)
result = chargebee.HostedPage.checkout_new_for_items({
    "subscription_items": [
        {
            "item_price_id": "Agency-pay-per-use-EUR-Monthly",
            "unit_quantity": 100
        }, ],
    'subscription': {
        'cf_organisation_name': 'Whatson B.V.'
    },
    'customer': {
        'first_name': '<PERSON>',
        'email': '<EMAIL>'
    }
})
hosted_page = result.hosted_page
print(hosted_page)
