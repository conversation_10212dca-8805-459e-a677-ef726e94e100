AUTHLIB_INSECURE_TRANSPORT=1
DATA_DB=whatson-test3
DB_NAME=whatson
ELASTIC_HOSTS=["https://*************:9200"]
ELASTIC_PASS=SgxgoWvgkBeiA2C9odU\=
ELASTIC_USER=elastic
ENVIRONMENT=development
FLASK_DEBUG=1
MAILDB_URL=mongodb+srv://doadmin:<EMAIL>/admin?tls\=true&authSource\=admin
MARYM_API_KEY=haha123
MARYM_URL=https://marym.whatson.ai
PG_HOST=db-postgresql-nyc3-06207-aug-23-backup-do-user-12079354-0.b.db.ondigitalocean.com
PG_PASSWORD=AVNS_IpDpoxpF4Eli4uMmhBJ
PG_PORT=25060
PG_USER=doadmin
PYTHONUNBUFFERED=1
REDIS_URL1=rediss://default:<EMAIL>:25061
SECRET_KEY=thisisasupersecretsecretkeyimposteramongussussyfortniteprankstersuwuwhatsthis69420
SENDING_SERVICE_KEY=Aivoo8ohGh0ahL0eupiy7Ohvo
SENDING_SERVICE_URL=https://sending.whatson.ai
SERVICE_HASH=pbkdf2:sha256:260000$UvmgHOohClCvWwxD$bf03635596a383338d3044c47185446d88e0efeabdcbd2bcf9d09275e4acdf2f
SPACES_ACCESS=DO00ZE9CAJ2GQ2JYAQ6U
SPACES_SECRET=32PQ9ThQP9pciXSKXfi54sR7FQ1tOlGKXDxFBaA16g8
TOKEN_LOCATION=["cookies", "headers", "query_string"]