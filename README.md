This is a very bare-bones app to create a simple development environment to run our frontend in.
Usually there are many components such as Redis, MongoDB, S3, etc. Now we want something that is as light as possible.

There is a Flask backend and a Next frontend made with create-next-app.
Fetch the Flask backend from https://github.com/Whatson-B-V/whatson using the access key in build.env
The environment variables for the backend are in backend.env

Fetch the frontend from https://github.com/bramvanuldenn/whatson-web
Here is an old dockerfile i found:
# Docker for easy local development 🔥🔥
FROM node:16.19-alpine3.17 as base
RUN apk update && apk upgrade && \
    apk add --no-cache \
    build-base bash git openssh python3 py3-pip
RUN yarn global add install-peerdeps

FROM base as build
ARG GITHUB_ACCESS_TOKEN=$GITHUB_ACCESS_TOKEN
WORKDIR /opt/app
COPY ./app/package.json ./
COPY ./app/yarn.lock ./
COPY ./app/.npmrc ./
RUN yarn
RUN install-peerdeps -Y @akkeramsterdam/whatson-components

FROM build as dev
ENTRYPOINT ["yarn", "start"]

From build as prod
COPY ./app ./
RUN yarn
RUN install-peerdeps -Y @akkeramsterdam/whatson-components
RUN yarn build --noninteractive
ENTRYPOINT ["yarn", "start:prod"]


