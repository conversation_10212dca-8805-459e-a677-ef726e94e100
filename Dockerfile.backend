FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    git \
    libpq-dev \
    openssh-client \
    && rm -rf /var/lib/apt/lists/*

# Set build args for GitHub token (still needed for requirements.txt)
ARG GITHUB_ACCESS_TOKEN
ENV GITHUB_TOKEN=${GITHUB_ACCESS_TOKEN}

# Copy SSH key and set up SSH
COPY id_github /root/.ssh/id_rsa
RUN chmod 600 /root/.ssh/id_rsa && \
    ssh-keyscan github.com >> /root/.ssh/known_hosts

# Clone the backend repository using SSH
RUN <NAME_EMAIL>:Whatson-B-V/whatson.git /tmp/backend && \
    cp -r /tmp/backend/* /app/ && \
    rm -rf /tmp/backend

# Update requirements.txt to use psycopg2-binary and SSH for git dependencies
RUN sed -i 's/psycopg2==2.9.7/psycopg2-binary==2.9.7/g' requirements.txt && \
    sed -i 's|git+https://\${GITHUB_TOKEN}@github.com/bramvanuldenn/Holmes.git|git+ssh://**************/bramvanuldenn/Holmes.git|g' requirements.txt && \
    sed -i 's|git+https://\${GITHUB_TOKEN}@github.com/Whatson-B-V/wrappers.git|git+ssh://**************/Whatson-B-V/wrappers.git|g' requirements.txt

# Update app.py to disable SSL for development environment
RUN sed -i "s/query={'sslmode': 'require'}/query={'sslmode': 'disable' if environ.get('ENVIRONMENT') == 'development' else 'require'}/g" app.py

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Install spaCy Dutch language model
RUN python -m spacy download nl_core_news_sm

# Expose port
EXPOSE 5000

# Run the application
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "1", "--timeout", "120", "app:app"]
