FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    git \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Set build args for GitHub token
ARG GITHUB_ACCESS_TOKEN
ENV GITHUB_TOKEN=${GITHUB_ACCESS_TOKEN}

# Clone the backend repository
RUN git clone https://bramvanuldenn:${GITHUB_ACCESS_TOKEN}@github.com/Whatson-B-V/whatson.git /tmp/backend && \
    cp -r /tmp/backend/* /app/ && \
    rm -rf /tmp/backend

# Update requirements.txt to use psycopg2-binary and correct GitHub tokens
RUN sed -i 's/psycopg2==2.9.7/psycopg2-binary==2.9.7/g' requirements.txt && \
    sed -i "s/\${GITHUB_TOKEN}/${GITHUB_ACCESS_TOKEN}/g" requirements.txt

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Expose port
EXPOSE 5000

# Run the application
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "1", "--timeout", "120", "app:app"]
