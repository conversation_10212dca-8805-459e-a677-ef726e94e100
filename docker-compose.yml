version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: whatson
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      args:
        GITHUB_ACCESS_TOKEN: ${GITHUB_ACCESS_TOKEN}
    env_file:
      - backend.env
    environment:
      # Override database settings to use local postgres
      PG_HOST: postgres
      PG_PORT: 5432
      PG_USER: postgres
      PG_PASSWORD: postgres
      DB_NAME: whatson
    ports:
      - "5000:5000"
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./backend:/app
    working_dir: /app

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        GITHUB_ACCESS_TOKEN: ${GITHUB_ACCESS_TOKEN}
    env_file:
      - frontend.env
    environment:
      - NODE_ENV=development
    ports:
      - "4444:3000"
      - "4040:4040"
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    working_dir: /app

volumes:
  postgres_data:
