{"root": true, "parser": "@typescript-eslint/parser", "parserOptions": {"project": "./tsconfig.json", "sourceType": "module"}, "env": {"browser": true, "node": true}, "plugins": ["@typescript-eslint", "prettier"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "plugin:@typescript-eslint/recommended-requiring-type-checking", "plugin:react-hooks/recommended", "plugin:import/recommended", "plugin:import/typescript", "prettier"], "rules": {"@typescript-eslint/default-param-last": ["error"], "@typescript-eslint/no-empty-function": ["error"], "@typescript-eslint/no-unused-vars": ["error"], "@typescript-eslint/require-await": ["off"], "@typescript-eslint/return-await": ["error"], "@typescript-eslint/no-explicit-any": ["off"], "@typescript-eslint/no-unsafe-argument": "off", "@typescript-eslint/no-unsafe-call": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/no-unsafe-member-access": "off", "@typescript-eslint/restrict-template-expressions": "off", "@next/next/consistent-return": "off", "arrow-body-style": ["error", "as-needed"], "react-hooks/exhaustive-deps": "off", "complexity": ["warn", 100], "consistent-return": ["off"], "curly": ["error", "all"], "default-case": ["error"], "default-case-last": ["error"], "default-param-last": ["off"], "dot-notation": ["error"], "eqeqeq": ["error"], "import/order": ["error", {"groups": [["builtin", "external", "internal"], ["parent", "sibling", "index"], ["object", "type"]]}], "max-depth": ["warn", 4], "max-len": ["off", {"code": 200, "tabWidth": 2, "comments": 100, "ignoreComments": true}], "no-console": ["warn"], "no-else-return": ["error"], "no-empty": ["error"], "no-empty-function": ["off"], "no-extra-semi": ["error"], "no-lonely-if": ["error"], "no-return-assign": ["error"], "no-return-await": ["off"], "no-param-reassign": ["error"], "no-self-compare": ["error"], "no-template-curly-in-string": ["error"], "no-unmodified-loop-condition": ["error"], "no-unneeded-ternary": ["error"], "no-unreachable-loop": ["error"], "no-use-before-define": ["error"], "object-shorthand": ["error"], "one-var": ["error", "never"], "padding-line-between-statements": ["error", {"blankLine": "always", "prev": ["*"], "next": ["return"]}, {"blankLine": "always", "prev": ["import"], "next": ["*"]}, {"blankLine": "never", "prev": ["import"], "next": ["import"]}, {"blankLine": "always", "prev": ["multiline-const", "switch", "throw", "try", "function", "multiline-expression"], "next": ["multiline-const", "export", "function", "expression"]}, {"blankLine": "always", "prev": ["export"], "next": ["multiline-block-like", "multiline-expression", "multiline-const"]}, {"blankLine": "always", "prev": ["const"], "next": ["multiline-block-like", "multiline-expression"]}, {"blankLine": "always", "prev": ["*"], "next": ["block-like"]}, {"blankLine": "never", "prev": ["case"], "next": ["default"]}, {"blankLine": "always", "prev": ["block-like"], "next": ["*"]}], "prefer-rest-params": ["error"], "prefer-spread": ["error"], "prefer-template": ["error"], "prettier/prettier": ["error", {"endOfLine": "auto"}], "require-await": ["off"], "spaced-comment": ["error", "always", {"markers": ["/"]}]}, "settings": {"import/resolver": {"typescript": {}, "node": {"extensions": [".ts", ".tsx", ".js", ".jsx", ".json"], "paths": ["src"]}}}}