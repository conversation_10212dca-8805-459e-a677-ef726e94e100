@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}
body {
  -webkit-overflow-scrolling: touch;
  -webkit-font-smoothing: antialiased;
}

html,
body {
  scroll-behavior: smooth !important;
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(calc(-250px * 7));
  }
}

.slide-track {
  width: calc(250px * 14);
  animation: scroll 40s linear infinite;
}

.slide-pause {
  animation-play-state: paused;
}
.custom-list {
  counter-reset: orderedlist;
}

.custom-list .custom-list-item {
  display: grid;
  grid-template-columns: 0 1fr;
  grid-gap: 76px;
  align-items: baseline;
}

.custom-list .custom-list-item::before {
  counter-increment: orderedlist;
  content: counter(orderedlist);
  font-size: 14px;
  display: flex;
  width: 36px;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: #fff;
  background-color: #404040;
  border-radius: 8px 0 8px 0;
}

/* SubpageHeader mobile view swiper */
.swiper-container-slider-subpage-header .swiper-slide {
  width: 239px;
}

.swiper-container-our-history .swiper-slide {
  width: 30%;
}

.image-swiper-button-disabled {
  background: #fafafa !important;
  color: #b8b5b5 !important;
  cursor: not-allowed !important;
}
.quote-swiper-button-disabled {
  background: #3d3d3d !important;
  color: #979797 !important;
  cursor: not-allowed !important;
}

@keyframes progressBar {
  from {
    right: -100%;
  }
  to {
    right: 0%;
  }
}

.progress-moved div {
  width: 100%;
  background-color: #404040;
  animation: progressAnimation 5s;
}

@keyframes progressAnimation {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

.container:hover .progress-moved div {
  -webkit-animation-play-state: paused;
  -moz-animation-play-state: paused;
  -o-animation-play-state: paused;
  animation-play-state: paused;
}

.swiper-slide {
  height: auto !important;
}

.swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: #171717 !important;
}

.swiper-pagination-bullet {
  background-color: #bbb9b9 !important;
  margin: 0 !important;
}
