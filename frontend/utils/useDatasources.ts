import { getStoryblokApi } from '@storyblok/react';
import { useEffect, useRef, useState } from 'react';
import isDeepEqual from 'fast-deep-equal/react';

export const useDatasources = (label: string, params?: any) => {
  const [datasources, setDatasources] = useState([]);
  const paramsRef = useRef(params);
  const storyblokApi = getStoryblokApi();

  if (!isDeepEqual(paramsRef.current, params)) {
    paramsRef.current = params;
  }

  useEffect(() => {
    async function fetchDatasources() {
      try {
        const fetchedDatasources = await storyblokApi.get(`cdn/datasource_entries`, {
          datasource: label,
          ...params,
        });

        setDatasources(fetchedDatasources.data.datasource_entries);
      } catch (e) {
        return e;
      }
    }

    if (label) {
      void fetchDatasources();
    }
  }, [label, paramsRef.current]);

  return datasources;
};
