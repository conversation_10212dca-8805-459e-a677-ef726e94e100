import React, { useState, useEffect } from 'react';

export const useInView = (refs: React.RefObject<HTMLElement>[]) => {
  const [elements, setElements] = useState<{
    [key: string]: { isInView: boolean };
  }>({});

  useEffect(() => {
    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach((entry) => {
        const name = entry.target.getAttribute('data-index');

        if (!name) {
          console.warn(
            'Encountered entry with no name. You should add data-index to every element passed to the isInView hook.'
          );
        } else if (entry.isIntersecting) {
          setElements(() => ({
            [name]: {
              isInView: true,
            },
          }));
        }
      });
    };

    const observer = new IntersectionObserver(observerCallback);

    refs.forEach((ref) => {
      if (ref.current) {
        observer.observe(ref.current);
      }
    });

    return () => {
      observer.disconnect();
    };
  }, [refs]);

  return elements;
};
