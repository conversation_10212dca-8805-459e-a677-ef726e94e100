import { MutableRefObject, useEffect, useRef, useState } from 'react';

export const useRatioHeight = (aspect_ratio: string | undefined) => {
  const ref = useRef() as MutableRefObject<HTMLDivElement> | any; // TBD
  const [width, setWidth] = useState(0);

  useEffect(() => {
    if (ref.current) {
      setWidth(ref.current.offsetWidth);
    }
  }, [ref]);

  useEffect(() => {
    function handleResize() {
      if (ref.current) {
        setWidth(ref.current.offsetWidth);
      }
    }

    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const height =
    aspect_ratio === 'square'
      ? width
      : aspect_ratio === 'landscape'
      ? (width / 3) * 2
      : aspect_ratio === 'widescreen'
      ? (width / 16) * 9
      : (4 / 3) * width + 24;

  return { ref, height };
};
