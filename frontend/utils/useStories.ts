import { getStoryblokApi } from '@storyblok/react';
import { useEffect, useState } from 'react';

export const useStories = (slug: string) => {
  const [stories, setStories] = useState([]);
  const storyblokApi = getStoryblokApi();

  useEffect(() => {
    async function fetchStories() {
      try {
        const fetchedStories = await storyblokApi.get(`cdn/stories`, {
          starts_with: slug,
          version: 'draft',
        });

        setStories(fetchedStories.data.stories);
      } catch (e) {
        return e;
      }
    }

    if (slug && slug !== '' && slug !== '/') {
      void fetchStories();
    }
  }, [slug]);

  return stories;
};
