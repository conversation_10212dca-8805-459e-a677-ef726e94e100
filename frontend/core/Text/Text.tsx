import { Button } from '@bramvanuldenn/whatson-components';
import { storyblokEditable } from '@storyblok/react';
import React, { useState } from 'react';
import { PopupModal } from 'react-calendly';
import Container from '../../components/Container';
import NavLink from '../../components/NavLink';
import RichText from '../../components/RichText';
import { Props } from './types';

const Text = ({ blok }: Props) => {
  const [open, setOpen] = useState(false);

  return (
    <div {...storyblokEditable(blok)} className="my-[72px] lg:my-28">
      <Container>
        <div className="col-span-full lg:col-start-3 lg:col-end-11 flex flex-col items-center text-center">
          <h2 className="text-3xl lg:text-5xl font-medium text-neutral-900">{blok.title}</h2>
          <RichText
            document={blok.description}
            className="mt-6 text-base lg:text-xl text-neutral-600"
          />
          <div className="mt-8 lg:flex lg:gap-3 lg:items-center w-full lg:w-fit">
            {blok.actions?.map((action) => {
              const href = action.target?.cached_url;
              const page = href;
              const urlLink = action?.target?.anchor
                ? `/${page}#${action?.target?.anchor}`
                : `/${page}`;

              if (action.isDemo) {
                return (
                  <div key={action._uid}>
                    <div className="hidden lg:block">
                      <Button
                        size={action.size}
                        variant={action.variant}
                        RenderAs="button"
                        onClick={() => {
                          setOpen(true);
                        }}
                      >
                        {action.text}
                      </Button>
                    </div>

                    <div className="mb-3 lg:hidden">
                      <Button
                        size={action.size}
                        variant={action.variant}
                        RenderAs="button"
                        onClick={() => {
                          setOpen(true);
                        }}
                        expand={true}
                      >
                        {action.text}
                      </Button>
                    </div>
                    {typeof window !== 'undefined' && (
                      <PopupModal
                        url={action.target.url}
                        onModalClose={() => {
                          setOpen(false);
                        }}
                        open={open}
                        rootElement={document.getElementById('__next') as HTMLElement}
                      />
                    )}
                  </div>
                );
              }

              return (
                <div key={action._uid}>
                  <div className="hidden lg:block">
                    <NavLink to={urlLink} key={action._uid}>
                      <Button size={action.size} variant={action.variant} RenderAs="div">
                        {action.text}
                      </Button>
                    </NavLink>
                  </div>
                  <div className="mb-3 lg:hidden">
                    <NavLink to={urlLink} key={action._uid}>
                      <Button
                        size={action.size}
                        variant={action.variant}
                        RenderAs="div"
                        expand={true}
                      >
                        {action.text}
                      </Button>
                    </NavLink>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </Container>
    </div>
  );
};

export default Text;
