import React from 'react';
import Script from 'next/script';
import Head from '../../components/Head';
import Navigation from '../Navigation';
import Footer from '../Footer';
import { Props } from './types';

const Layout = ({ children }: Props) => {
  const title = children?.props?.blok?.meta?.title;

  const seo_title = `${title ? `${title[0].toUpperCase()}${title.substring(1)} | ` : ''}Whatson`;
  const seo_description = children?.props?.blok?.meta?.description
    ? `${children.props.blok.meta?.description}`
    : '';

  const og_title = children?.props?.blok?.meta?.og_title
    ? `${children.props.blok.meta?.og_title} | Whatson`
    : '';

  const og_description = children?.props?.blok?.meta?.og_description
    ? `${children.props.blok.meta?.og_description} | Whatson`
    : '';

  const og_image = children?.props?.blok?.meta?.og_image
    ? `${children.props.blok.meta?.og_image}`
    : '';

  const twitter_title = children?.props?.blok?.meta?.twitter_title
    ? `${children.props.blok.meta?.twitter_title} | Whatson`
    : '';

  const twitter_image = children?.props?.blok?.meta?.twitter_image
    ? `${children.props.blok.meta?.twitter_image} | Whatson`
    : '';

  const twitter_description = children?.props?.blok?.meta?.twitter_description
    ? `${children.props.blok.meta?.twitter_image} | Whatson`
    : '';

  return (
    <div>
      <Head
        title={seo_title}
        description={seo_description}
        og_title={og_title}
        og_description={og_description}
        og_image={og_image}
        twitter_title={twitter_title}
        twitter_image={twitter_image}
        twitter_description={twitter_description}
      />
      <noscript>
        <iframe
          src={`https://www.googletagmanager.com/ns.html?id=GTM-NCZRKDQ`}
          height="0"
          width="0"
          style={{ display: 'none', visibility: 'hidden' }}
        ></iframe>
      </noscript>
      <Navigation />
      <div className="z-50"> {children}</div>
      <Footer />
    </div>
  );
};

export default Layout;
