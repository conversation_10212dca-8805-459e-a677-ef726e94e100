import { storyblokEditable } from '@storyblok/react';
import classNames from 'classnames';
import RichText from '../../components/RichText';
import { Props } from './types';

const ArticleText = ({ blok }: Props) => (
  <div
    className={classNames({
      'grid grid-cols-6 lg:grid-cols-12 gap-x-5 lg:gap-x-10 max-w-screen-xl': blok.isInArticle,
    })}
    {...storyblokEditable(blok)}
    id={blok.anchor_id}
  >
    <div
      className={classNames({
        'w-full flex flex-col gap-3': true,
        'lg:col-start-3 lg:col-end-11 col-span-full': blok.isInArticle,
      })}
    >
      {blok.title && (
        <h2 className="text-2xl lg:text-3xl text-neutral-900 font-medium">{blok.title}</h2>
      )}
      {blok.description && (
        <RichText document={blok.description} className="text-neutral-900 text-lg" />
      )}
    </div>
  </div>
);

export default ArticleText;
