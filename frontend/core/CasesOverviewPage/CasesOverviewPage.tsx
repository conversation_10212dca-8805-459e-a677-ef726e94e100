import { storyblokEditable, StoryblokComponent } from '@storyblok/react';
import { useState, useEffect } from 'react';
import { useStories } from '../../utils/useStories';
import { Props, Option } from './type';
import ArticleCard from '../../components/ArticleCard/ArticleCard';
import Container from '../../components/Container';
import { useDatasources } from '../../utils/useDatasources';
import FiltersCasesOverview from '../../components/FiltersCasesOverview';
import type { Link } from '../../types/types';

const CasesOverviewPage = ({ blok }: Props) => {
  const [filter, setFilter] = useState('All');
  const [options, setOptions] = useState<Option[]>();
  const labels: Option[] = useDatasources('cases');
  const stories = useStories('cases/');

  const caseCards: Link[] = stories.filter((card: Link) => {
    if (card.content.component === 'case_page') {
      if (filter === 'All') {
        return card;
      }

      return card.content?.tags.find((tag: any) => tag.text === filter);
    }
  });

  const buildFilterOptions = () => {
    if (stories.length > 0) {
      const _options = [{ id: 1, name: 'All', hits: stories.length - 1 }];

      labels.map((label) => {
        const numberOfHits = stories.filter((story: any) => {
          if (story.content.component === 'case_page') {
            return story.content?.tags.find((tag: any) => tag.text === label.name);
          }
        });

        _options.push({
          id: label.id,
          name: label.name,
          hits: numberOfHits.length,
        });
      });

      setOptions(_options);
    }
  };

  useEffect(() => {
    buildFilterOptions();
  }, [labels, stories]);

  const updateFilter = (selected: string) => {
    setFilter(selected);
  };

  return (
    <div {...storyblokEditable(blok)} className="py-24 md:py-32">
      <Container>
        {blok.header && (
          <div className="col-span-full flex justify-center items-center pb-12 lg:pb-24">
            {blok.header?.map((nestedBlok) => (
              <StoryblokComponent blok={nestedBlok} key={nestedBlok._uid} />
            ))}
          </div>
        )}
        <FiltersCasesOverview options={options ? options : labels} updateFilter={updateFilter} />
        {caseCards && (
          <div className="lg:col-start-2 lg:col-end-12 col-span-full grid grid-cols-6 md:grid-cols-12 gap-7 md:gap-8 ">
            {caseCards.length > 3 ? (
              <>
                {caseCards.slice(0, 3).map((article) => (
                  // TBD update when add another locale
                  // const link = locale === 'en' ? article.full_slug : article.translated_slugs[0].path;
                  <div className=" col-span-full md:col-span-4" key={article.uuid}>
                    <ArticleCard
                      desktop_media={article.content?.desktop_media}
                      mobile_media={article.content?.mobile_media}
                      title={article.content?.title}
                      tags={article.content?.tags}
                      to={article.full_slug}
                      logo={article.content?.logo}
                      isCaseCard
                      media_type={article.content?.media_type}
                    />
                  </div>
                ))}
              </>
            ) : (
              <>
                {caseCards.map((article) => (
                  <div className="col-span-full md:col-span-4" key={article.uuid}>
                    <ArticleCard
                      desktop_media={article.content?.desktop_media}
                      mobile_media={article.content?.mobile_media}
                      title={article.content?.title}
                      tags={article.content?.tags}
                      to={article.full_slug}
                      logo={article.content?.logo}
                      isCaseCard
                      media_type={article.content?.media_type}
                    />
                  </div>
                ))}
              </>
            )}
          </div>
        )}
        {blok.quote && (
          <div className="lg:col-start-2 lg:col-end-12 col-span-full py-32">
            {blok.quote?.map((nestedBlok) => (
              <StoryblokComponent blok={nestedBlok} key={nestedBlok._uid} />
            ))}
          </div>
        )}
        {caseCards && (
          <>
            {caseCards.length > 3 && (
              <div className="lg:col-start-2 lg:col-end-12 col-span-full grid grid-cols-6 md:grid-cols-12 gap-7 md:gap-8 ">
                {caseCards.slice(3, caseCards.length).map((article) => (
                  <div className=" col-span-full md:col-span-4" key={article.uuid}>
                    <ArticleCard
                      desktop_media={article.content?.desktop_media}
                      mobile_media={article.content?.mobile_media}
                      title={article.content?.title}
                      tags={article.content?.tags}
                      to={article.full_slug}
                      logo={article.content?.logo}
                      isCaseCard
                      media_type={article.content?.media_type}
                    />
                  </div>
                ))}
              </div>
            )}
          </>
        )}
      </Container>
    </div>
  );
};

export default CasesOverviewPage;
