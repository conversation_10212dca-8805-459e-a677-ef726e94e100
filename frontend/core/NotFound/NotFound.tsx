import React from 'react';
import { useStoryblok } from '@storyblok/react';
import { Button } from '@bramvanuldenn/whatson-components';
import Container from '../../components/Container';
import RichText from '../../components/RichText';
import { useRatioHeight } from '../../utils/useRatioHeight';
import Image from '../../components/Image';
import NavLink from '../../components/NavLink';
import { Action } from '../../types/types';

const NotFound = () => {
  const story = useStoryblok('global/not-found', {
    version: 'draft',
  });

  const notFoundContents = story?.content;
  const { ref: assetRef, height: assetHeight } = useRatioHeight('square');

  return (
    <div className="lg:my-36 my-24">
      <Container>
        <div className="col-span-full lg:col-start-2 lg:col-end-7 h-full flex flex-col justify-center order-2 lg:order-1">
          <span className="text-center mt-6 lg:mt-0 lg:text-left text-xs text-neutral-500 font-bold">
            {notFoundContents?.tag.toUpperCase()}
          </span>
          <h1 className="mt-6 text-4xl text-center lg:text-left lg:text-7xl font-medium text-neutral-900">
            {notFoundContents?.title}
          </h1>
          <RichText
            document={notFoundContents?.description}
            className="mt-6 text-lg text-center lg:text-left text-neutral-600"
          />
          {notFoundContents?.action?.map((item: Action) => (
            <div className="mt-9 flex justify-center lg:block" key={item._uid}>
              <NavLink to={item.target.cached_url}>
                <Button
                  size={item.size}
                  variant={item.variant}
                  RenderAs="div"
                  icon={item.icon}
                  className="!gap-3"
                >
                  {item.text}
                </Button>
              </NavLink>
            </div>
          ))}
        </div>
        <div className="col-span-full lg:col-start-7 lg:col-end-13 order-1 lg:order-2">
          <div ref={assetRef} style={{ height: assetHeight }}>
            <Image src={notFoundContents?.image?.filename} alt={notFoundContents?.image?.alt} />
          </div>
        </div>
      </Container>
    </div>
  );
};

export default NotFound;
