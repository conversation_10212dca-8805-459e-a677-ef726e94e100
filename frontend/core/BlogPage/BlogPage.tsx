import { storyblokEditable, useStoryblo<PERSON>, StoryblokComponent } from '@storyblok/react';
import { useRouter } from 'next/router';
import { FacebookShareButton, TwitterShareButton, WhatsappShareButton } from 'react-share';
import { <PERSON><PERSON>, <PERSON><PERSON>, Badge } from '@bramvanuldenn/whatson-components';
import { useEffect, useState } from 'react';
import { useTranslation } from 'next-i18next';
import { Props } from './type';
import Image from '../../components/Image';
import NavLink from '../../components/NavLink';
import Container from '../../components/Container/Container';
import { ReactComponent as Twitter } from '../../public/svg/twitter.svg';
import { ReactComponent as Facebook } from '../../public/svg/facebook.svg';
import { ReactComponent as Linkedin } from '../../public/svg/linkedin-black.svg';
import { useScreenWidth } from '../../utils/useScreenWidth';
import { CONSTANTS } from '../../constants/constants';
import VideoPlayer from '../../components/VideoPlayer';

const BlogPage = ({ blok }: Props) => {
  const { asPath } = useRouter();
  const { t } = useTranslation('common');
  const [href, setHref] = useState('');
  const [copySuccess, setCopySuccess] = useState(false);

  const story = useStoryblok(asPath.slice(1), {
    version: 'draft',
  });

  let publishedDate;

  if (story.published_at) {
    publishedDate = new Date(story.published_at);
  }

  const date = publishedDate?.toLocaleDateString('en-GB', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });

  useEffect(() => {
    setHref(window.location.href);
  }, []);

  const copyToClip = async () => {
    await navigator.clipboard.writeText(href);
    setCopySuccess(true);
  };

  const screenWidth = useScreenWidth();
  const media = blok.mobile_media?.filename
    ? screenWidth < CONSTANTS.mdWidth
      ? blok.mobile_media
      : blok.desktop_media
    : blok.desktop_media;

  return (
    <>
      {blok && (
        <Container className="mt-24 lg:mt-32" {...storyblokEditable(blok)}>
          <div className="lg:col-start-3 lg:col-end-10 mb-20 flex flex-col gap-7 col-span-full">
            <NavLink to="/blog">
              <Button variant="secondary" size="sm" RenderAs="div">
                <div className="flex items-center gap-1">
                  <Icon name="HiArrowLeft" size="xs" />
                  {t('back-blog')}
                </div>
              </Button>
            </NavLink>

            <div className="flex gap-3 items-center">
              <p className="text-sm font-medium text-neutral-500">
                {story.published_at ? `${date}` : 'story is not published yet'}
              </p>
              <span className="text-neutral-500">&bull;</span>
              {blok.tags &&
                blok.tags.map((tag) => (
                  <Badge variant={tag.variant} size={tag.size} iconName={tag.icon} key={tag._uid}>
                    {tag.text}
                  </Badge>
                ))}
            </div>
            <h1 className="text-2xl font-medium lg:text-5xl">{blok.title}</h1>
            <div className="flex items-center gap-3">
              <Button variant="secondary" size="sm" RenderAs="button" onClick={copyToClip}>
                <div className="flex items-center gap-1">
                  {copySuccess ? (
                    <div className="flex items-center gap-1">
                      <Icon name="HiCheck" size="xs" />
                      {t('copied')}
                    </div>
                  ) : (
                    <div className="flex items-center gap-1">
                      <Icon name="HiLink" size="xs" />
                      {t('copy-link')}
                    </div>
                  )}
                </div>
              </Button>
              <Button variant="secondary" size="sm" RenderAs="div" className="!p-[9px]">
                <WhatsappShareButton url={href} title={blok.title}>
                  <div className="w-[15px] h-[15px]">
                    <Linkedin />
                  </div>
                </WhatsappShareButton>
              </Button>
              <Button variant="secondary" size="sm" RenderAs="div" className="!p-[9px]">
                <FacebookShareButton url={href} quote={blok.title}>
                  <div className="w-[15px] h-[15px]">
                    <Facebook />
                  </div>
                </FacebookShareButton>
              </Button>
              <Button variant="secondary" size="sm" RenderAs="div" className="!p-[9px]">
                <TwitterShareButton url={href} title={blok.title}>
                  <div className="w-[15px] h-[15px] flex items-center">
                    <Twitter />
                  </div>
                </TwitterShareButton>
              </Button>
            </div>
          </div>

          {blok.media_type === 'video' ? (
            <div className="lg:h-[535px] lg:col-start-2 lg:col-end-12 w-full h-[290px] col-span-full rounded-xl overflow-hidden relative">
              <VideoPlayer videoLink={media.filename} buttonPosition="center" />
            </div>
          ) : (
            <Image
              src={media.filename}
              alt={media.alt}
              classname="lg:h-[535px] lg:col-start-2 lg:col-end-12 w-full h-[290px] col-span-full"
              imageClassName="rounded-xl"
            />
          )}
          <div className="mt-20 lg:col-start-2 lg:col-end-12 col-span-full flex-col lg:gap-14 gap-12 flex">
            {blok.body?.map((nestedBlok: any) => (
              <StoryblokComponent blok={nestedBlok} key={nestedBlok._uid} />
            ))}
          </div>
          <div className="lg:col-start-2 lg:col-end-12 col-span-full">
            {blok.more_articles?.map((nestedBlok) => (
              <StoryblokComponent blok={nestedBlok} key={nestedBlok._uid} />
            ))}
          </div>
        </Container>
      )}
    </>
  );
};

export default BlogPage;
