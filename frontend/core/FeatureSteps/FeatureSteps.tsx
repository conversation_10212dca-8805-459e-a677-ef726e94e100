import { storyblokEditable } from '@storyblok/react';
import React from 'react';
import Container from '../../components/Container';
import RichText from '../../components/RichText';
import StepImage from '../../components/StepImage';
import { Props, Step } from './types';

const FeatureSteps = ({ blok }: Props) => (
  <div {...storyblokEditable(blok)} className="my-[72px] lg:my-28">
    <div id="how-it-works" className="block relative -top-44 invisible" />
    <Container>
      <div className="col-span-full text-center">
        <h2 className="text-3xl lg:text-5xl font-medium mb-10 lg:mb-16">{blok.title}</h2>
      </div>
      {blok.steps?.map((step: Step, index: number) => (
        <div
          className="col-span-full lg:col-span-4 flex flex-col items-center mb-12 lg:mb-12 last:mb-0"
          key={step._uid}
        >
          <StepImage number={index + 1} image={step.image} color={blok.color} />
          <h5 className="text-2xl font-bold text-neutral-900 text-center mt-4">{step.title}</h5>
          <RichText
            document={step.description}
            className="text-xl text-neutral-700 text-center mt-4"
          />
        </div>
      ))}
    </Container>
  </div>
);

export default FeatureSteps;
