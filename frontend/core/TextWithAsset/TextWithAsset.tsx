import { Hyperlink, Icon } from '@bramvanuldenn/whatson-components';
import { storyblokEditable } from '@storyblok/react';
import classNames from 'classnames';
import { useRouter } from 'next/router';
import React from 'react';
import Image from '../../components/Image';
import NavLink from '../../components/NavLink';
import RichText from '../../components/RichText';
import Tag from '../../components/Tag';
import VideoPlayer from '../../components/VideoPlayer';
import { CONSTANTS } from '../../constants/constants';
import { Action } from '../../types/types';
import { useRatioHeight } from '../../utils/useRatioHeight';
import { useScreenWidth } from '../../utils/useScreenWidth';
import { Props } from './types';

const TextWithAsset = ({ blok }: Props) => {
  const { ref: assetRef, height: assetHeight } = useRatioHeight('square');
  const router = useRouter();

  const screenWidth = useScreenWidth();
  const media = blok.asset_mobile?.filename
    ? screenWidth < CONSTANTS.mdWidth
      ? blok.asset_mobile
      : blok.asset
    : blok.asset;

  return (
    <div {...storyblokEditable(blok)} className="my-[72px] lg:my-28">
      <div
        className={classNames({
          'grid grid-cols-6 lg:grid-cols-12 relative gap-x-5 lg:gap-x-10 lg:px-10 lg:mx-auto max-w-screen-xl':
            true,
          'mx-5': !blok.isBackground,
          'py-4 lg:py-16 bg-text-asset-background-mobile lg:bg-brand-orange-100': blok.isBackground,
        })}
      >
        <div
          className={classNames({
            'mx-5': blok.isBackground,
            'col-span-full order-2 lg:col-start-1 lg:col-end-6 lg:order-1':
              blok.isTextLeft && !blok.isBackground,
            'col-span-full order-2 lg:col-start-2 lg:col-end-7 lg:order-1':
              blok.isTextLeft && blok.isBackground,
            'col-span-full lg:col-start-8 lg:col-end-13 order-2':
              !blok.isTextLeft && !blok.isBackground,
            'col-span-full lg:col-start-7 lg:col-end-12 order-2':
              !blok.isTextLeft && blok.isBackground,
          })}
        >
          <div className="flex flex-col justify-center h-full">
            {blok.tag && !blok.isBackground && (
              <Tag color={blok.tag_color} label={blok.tag} classname="mb-2.5 lg:mb-5" />
            )}
            {blok.tag && blok.isBackground && (
              <span className="text-teal-700 font-medium text-lg">{blok.tag}</span>
            )}
            <div className="text-3xl lg:text-4xl text-neutral-900 font-medium mb-4 lg:mb-9">
              {blok.title}
            </div>
            <RichText
              document={blok.description}
              className="text-base lg:text-xl text-neutral-600"
            />

            {blok.icon_texts?.map((item) => (
              <div className="flex items-start gap-4 mb-10 last:mb-0" key={item._uid}>
                <div className="p-1 text-teal-500">
                  <Icon name={item.icon} />
                </div>
                <div>
                  <span className="text-lg lg:text-xl font-bold text-neutral-900">
                    {item.title}
                  </span>
                  <RichText
                    document={item.description}
                    className="mt-3 lg:text-xl text-neutral-800"
                  />
                </div>
              </div>
            ))}
            <div className="mt-4 lg:mt-10">
              {blok.action?.map((link: Action) => (
                <NavLink to={link.target.cached_url} key={link._uid}>
                  <Hyperlink
                    className="text-sm lg:text-xl font-bold px-0 text-brand-pink-500 group"
                    size={link.size}
                    RenderAs="div"
                    onClick={() => {
                      void router.push(link.target.cached_url);
                    }}
                  >
                    <div className="flex gap-2 items-center">
                      <span className="lg:text-xl font-medium ">{link.text}</span>
                      <Icon size="sm" name="HiArrowRight" className="" />
                    </div>
                  </Hyperlink>
                </NavLink>
              ))}
            </div>
          </div>
        </div>
        <div
          className={classNames({
            'mb-6 lg:mb-0': true,
            'col-span-full lg:col-start-7 lg:col-end-13 order-1 lg:order-2': blok.isTextLeft,
            'col-span-full  lg:col-start-1 lg:col-end-7 order-1': !blok.isTextLeft,
          })}
        >
          {blok.asset_isVideo ? (
            <div
              ref={assetRef}
              className="rounded-tr-[20px] rounded-bl-[20px] rounded-br-[20px] overflow-hidden relative"
              style={{ height: assetHeight }}
            >
              <VideoPlayer
                videoLink={media.filename}
                buttonPosition="bottom-left"
                autoPlay={blok.isVideoAutoPlay}
              />
            </div>
          ) : (
            <div
              ref={assetRef}
              className="rounded-tr-[20px] rounded-bl-[20px] rounded-br-[20px] overflow-hidden"
              style={{ height: assetHeight }}
            >
              <Image src={media.filename} alt={media.alt} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TextWithAsset;
