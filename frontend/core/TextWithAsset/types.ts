import { IconName } from '@bramvanuldenn/whatson-components/build/components/Icon/types';
import { Action, Media } from '../../types/types';

type IconText = {
  icon: IconName;
  title: string;
  description: any;
  _uid: string;
};

type TextWithAssetProp = {
  action: Action[];
  asset: Media;
  asset_mobile: Media;
  asset_isVideo: boolean;
  isTextLeft: boolean;
  tag: string;
  tag_color: 'lime' | 'blue' | 'teal' | 'violet';
  title: string;
  description: any;
  _uid: string;
  isBackground: boolean;
  icon_texts: IconText[];
  isVideoAutoPlay: boolean;
};

export type Props = {
  blok: TextWithAssetProp;
};
