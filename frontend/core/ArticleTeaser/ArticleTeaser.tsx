import { storyblokEditable, StoryblokComponent } from '@storyblok/react';
import classNames from 'classnames';
import ArticleCard from '../../components/ArticleCard';
import { Props } from './type';

const ArticleTeaser = ({ blok, isInCase }: Props) => (
  <div {...storyblokEditable(blok)} className="py-12 lg:py-40">
    {blok.header?.map((nestedBlok: any) => (
      <StoryblokComponent blok={nestedBlok} key={nestedBlok._uid} />
    ))}
    <div
      className={classNames({
        'flex flex-col md:flex-row': true,
        'gap-6 lg:gap-8': blok?.articles.length > 2,
        'gap-6': blok?.articles.length <= 2,
      })}
    >
      {blok?.articles &&
        blok.articles.map((article) => {
          // TBD update when add another locale
          // const link = locale === 'en' ? article.full_slug : article.translated_slugs[0].path;
          let publishedDate;

          if (article.published_at) {
            publishedDate = new Date(article.published_at);
          }

          const date = publishedDate?.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: 'short',
          });

          return (
            <div className="w-full md:max-w-xs" key={article.uuid}>
              <ArticleCard
                time_to_read={article.content?.time_to_read}
                desktop_media={article.content?.desktop_media}
                mobile_media={article.content?.mobile_media}
                title={article.content?.title}
                tags={article.content?.tags}
                to={article.full_slug}
                published_date={date}
                logo={article.content?.logo}
                media_type={article.content?.media_type}
                isCaseCard={isInCase}
              />
            </div>
          );
        })}
    </div>
  </div>
);

export default ArticleTeaser;
