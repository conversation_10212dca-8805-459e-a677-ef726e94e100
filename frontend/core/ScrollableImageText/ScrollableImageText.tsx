import { Transition } from '@headlessui/react';
import { storyblokEditable } from '@storyblok/react';
import React, { useEffect, useRef, useState, createRef, RefObject } from 'react';
import { Hyperlink, Icon } from '@bramvanuldenn/whatson-components';
import classNames from 'classnames';
import { Action } from '../../types/types';
import NavLink from '../../components/NavLink';
import RichText from '../../components/RichText';
import Container from '../../components/Container';
import { Props } from './types';
import { CONSTANTS } from '../../constants/constants';
import { useRatioHeight } from '../../utils/useRatioHeight';
import { useScreenWidth } from '../../utils/useScreenWidth';
import { useInView } from '../../utils/useInView';
import Image from '../../components/Image';
import VideoPlayer from '../../components/VideoPlayer';

const ScrollableImageText = ({ blok }: Props) => {
  const { ref: assetRef, height: assetHeight } = useRatioHeight('landscape');
  const [active, setActive] = useState('0');
  const screenWidth = useScreenWidth();
  const elementsRef: React.MutableRefObject<RefObject<HTMLHeadingElement>[]> = useRef(
    blok.Scrollable_item.map(() => createRef())
  );

  const arrayForHook: RefObject<HTMLHeadingElement>[] =
    elementsRef.current && elementsRef.current.map((item) => item);

  const observedElements = useInView(arrayForHook);

  useEffect(() => {
    Object.keys(observedElements).map((key) => {
      if (observedElements[key].isInView) {
        setActive(key);
      }
    });
  }, [observedElements]);

  return (
    <>
      <div id="scrollable-text-image" className="block relative -top-20 invisible" />
      {screenWidth < CONSTANTS.mdWidth ? (
        <Container {...storyblokEditable(blok)} className="my-[72px]">
          {blok.Scrollable_item?.map((item, index) => {
            const media = item.mobile_asset?.filename ? item.mobile_asset : item.desktop_asset;

            return (
              <div className="col-span-full" key={index} id={item.anchor_id}>
                <div className="w-full relative mb-8">
                  {item.media_type === 'video' ? (
                    <div ref={assetRef} style={{ height: assetHeight }}>
                      <VideoPlayer videoLink={media.filename} buttonPosition="bottom-left" />
                    </div>
                  ) : (
                    <div ref={assetRef} style={{ height: assetHeight }}>
                      <Image src={media.filename} alt={media.alt} />
                    </div>
                  )}
                </div>
                <div className="flex flex-col justify-start mb-14 w-full " key={index}>
                  <div className="flex flex-col items-start">
                    <span className="text-neutral-500 font-medium uppercase text-xs mb-6">
                      {item.label}
                    </span>

                    <div
                      className={classNames({
                        'p-3 w-fit rounded-tl-lg rounded-br-lg mb-3': true,
                        'bg-teal-100 text-teal-600': item.icon_color === 'teal',
                        'bg-brand-blue-100 text-brand-blue-600': item.icon_color === 'blue',
                        'bg-lime-100 text-lime-600': item.icon_color === 'lime',
                        'bg-violet-100 text-violet-600': item.icon_color === 'violet',
                        'bg-brand-orange-100 text-brand-orange-600': item.icon_color === 'orange',
                      })}
                    >
                      <Icon name={item.icon} />
                    </div>
                    <div className="flex-col flex gap-4">
                      <h3 className="text-2xl font-bold">{item.title}</h3>

                      <RichText document={item.description} className="text-lg text-neutral-500" />

                      {item.action?.map((link: Action) => {
                        const href = link.target?.cached_url;
                        const page = href;
                        const urlLink = link?.target?.anchor
                          ? `/${page}#${link?.target?.anchor}`
                          : `/${page}`;

                        return (
                          <NavLink to={urlLink} key={link._uid}>
                            <Hyperlink
                              className="text-brand-pink-500 group"
                              size={link.size}
                              RenderAs="div"
                            >
                              <div className="flex gap-2 items-center">
                                <span className="lg:text-xl font-medium ">{link.text}</span>
                                <Icon size="sm" name="HiArrowRight" />
                              </div>
                            </Hyperlink>
                          </NavLink>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </Container>
      ) : (
        <div
          {...storyblokEditable(blok)}
          className="lg:mb-28 mx-5 md:mx-0 flex w-full justify-center items-start"
        >
          <div className="sticky w-1/2 h-[calc(100vh_-_90px)] top-[90px]">
            <div className="sticky overflow-hidden top-0 h-[calc(100vh_-_90px)] grid box-border">
              {blok.Scrollable_item?.map((item, index) => {
                const media = item.desktop_asset;

                return (
                  <Transition
                    show={active === index.toString()}
                    key={index}
                    enter="transition-opacity duration-700 ease-in-out"
                    enterFrom="transform opacity-0"
                    enterTo="transform opacity-100"
                    leave="transition-opacity duration-500 ease-in-out"
                    leaveFrom="transform opacity-100"
                    leaveTo="transform opacity-0"
                  >
                    <div className="col-start-1 col-end-2 relative top-1/2 transform -translate-y-1/2 h-[calc(100vh_-_90px)]">
                      {item.media_type === 'video' ? (
                        <div ref={assetRef} style={{ height: assetHeight }}>
                          <VideoPlayer videoLink={media.filename} buttonPosition="bottom-left" />
                        </div>
                      ) : (
                        <div
                          ref={assetRef}
                          style={{ height: assetHeight }}
                          className="min-h-[calc(100vh_-_40px)]"
                        >
                          <Image src={media.filename} alt={media.alt} />
                        </div>
                      )}
                    </div>
                  </Transition>
                );
              })}
            </div>
          </div>
          <div className="w-1/2 block ">
            <div className="px-12 max-w-[40rem] box-border">
              {blok.Scrollable_item?.map((item, index) => (
                <div className="h-screen flex items-center" key={index} id={item.anchor_id}>
                  <div className="flex flex-col items-start">
                    <span className="text-neutral-500 font-medium uppercase text-xs">
                      {item.label}
                    </span>
                    <div className="flex gap-2 md:gap-4 items-start py-7 max-w-3xl">
                      <div
                        className={classNames({
                          'p-3 w-fit rounded-tl-lg rounded-br-lg': true,
                          'bg-teal-100 text-teal-600': item.icon_color === 'teal',
                          'bg-brand-blue-100 text-brand-blue-600': item.icon_color === 'blue',
                          'bg-lime-100 text-lime-600': item.icon_color === 'lime',
                          'bg-violet-100 text-violet-600': item.icon_color === 'violet',
                          'bg-yellow-100 text-yellow-600': item.icon_color === 'orange',
                        })}
                      >
                        <Icon name={item.icon} />
                      </div>
                      <div className="flex-col flex gap-4">
                        <h3
                          className="text-2xl font-bold"
                          ref={elementsRef.current[index]}
                          data-index={index.toString()}
                        >
                          {item.title}
                        </h3>

                        <RichText
                          document={item.description}
                          className="text-lg text-neutral-500 mb-7"
                        />

                        <div>
                          {item.action?.map((link: Action) => {
                            const href = link.target?.cached_url;
                            const page = href;
                            const urlLink = link?.target?.anchor
                              ? `/${page}#${link?.target?.anchor}`
                              : `/${page}`;

                            return (
                              <NavLink to={urlLink} key={link._uid}>
                                <Hyperlink
                                  className="text-brand-pink-500 group"
                                  size={link.size}
                                  RenderAs="div"
                                >
                                  <div className="flex gap-2 items-center">
                                    <span className="lg:text-xl font-medium ">{link.text}</span>
                                    <Icon size="sm" name="HiArrowRight" />
                                  </div>
                                </Hyperlink>
                              </NavLink>
                            );
                          })}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ScrollableImageText;
