import { IconName } from '@bramvanuldenn/whatson-components/build/components/Icon/types';
import { Media, Action } from '../../types/types';

export type ScrollableItemProps = {
  label: number;
  title: string;
  description: any;
  desktop_asset: Media;
  mobile_asset: Media;
  icon_color: 'teal' | 'violet' | 'blue' | 'lime' | 'orange';
  icon: IconName;
  media_type: 'video' | 'image';
  action: Action[];
  anchor_id?: string;
};

type ScrollableImageTextProp = {
  _uid: string;
  component: string;
  Scrollable_item: ScrollableItemProps[];
};

export type Props = {
  blok: ScrollableImageTextProp;
};
