import { Icon } from '@bramvanuldenn/whatson-components';
import { storyblokEditable } from '@storyblok/react';
import classNames from 'classnames';
import React from 'react';
import Container from '../../components/Container';
import RichText from '../../components/RichText';
import { Props } from './types';

const Usps = ({ blok }: Props) => {
  const isFullWidth = blok.usps?.length > 4;

  return (
    <div {...storyblokEditable(blok)} className="my-[72px] lg:my-28">
      <div id="usps" className="block relative -top-44 invisible" />
      <Container>
        <div
          className={classNames({
            'col-span-full lg:col-start-1 lg:col-end-5': !isFullWidth,
            'col-span-full': isFullWidth,
          })}
        >
          <h2 className="text-3xl lg:text-5xl font-medium text-neutral-900">{blok.title}</h2>
        </div>
        <div
          className={classNames({
            'mt-12 lg:mt-0': true,
            'lg:grid lg:grid-cols-2 lg:gap-10 col-span-full lg:col-start-6 lg:col-end-13':
              !isFullWidth,
            'lg:grid lg:grid-cols-3 lg:gap-10 col-span-full lg:mt-16': isFullWidth,
          })}
        >
          {blok.usps?.map((usp) => (
            <div className="col-span-1 mb-8 lg:mb-0 last:mb-0" key={usp._uid}>
              <div
                className={classNames({
                  'p-3 rounded-tl-lg rounded-br-lg w-fit mb-4': true,
                  'bg-teal-100 text-teal-600': usp.icon_color === 'teal',
                  'bg-brand-blue-100 text-brand-blue-600': usp.icon_color === 'blue',
                  'bg-lime-100 text-lime-600': usp.icon_color === 'lime',
                  'bg-violet-100 text-violet-600': usp.icon_color === 'violet',
                  'bg-brand-orange-100 text-brand-orange-600': usp.icon_color === 'orange',
                  'bg-yellow-100 text-yellow-600': usp.icon_color === 'yellow',
                })}
              >
                <Icon name={usp.icon} />
              </div>
              <h3 className="text-xl font-bold text-neutral-900">{usp.title}</h3>
              <RichText document={usp.description} className="mt-4 text-base text-neutral-500" />
            </div>
          ))}
        </div>
      </Container>
    </div>
  );
};

export default Usps;
