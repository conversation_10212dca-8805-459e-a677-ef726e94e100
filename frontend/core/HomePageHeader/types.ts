import type { Media, Action, Badge } from '../../types/types';
import type { LogoBlok } from '../LogoSlider/types';

type HomePageHeaderBlok = {
  title: string;
  description: any;
  component: string;
  desktop_background_image: Media;
  mobile_background_image: Media;
  actions: Action[];
  feature_link: Action[];
  feature_label: Badge[];
  _uid: string;
  logo_slider: LogoBlok[];
};

export type Props = {
  blok: HomePageHeaderBlok;
};
