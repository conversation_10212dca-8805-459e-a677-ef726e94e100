import React, { useState } from 'react';
import { storyblokEditable, StoryblokComponent } from '@storyblok/react';
import classNames from 'classnames';
import { Button, Hyperlink, Icon, Badge } from '@bramvanuldenn/whatson-components';
import { PopupModal } from 'react-calendly';
import Container from '../../components/Container';
import RichText from '../../components/RichText';
import NavLink from '../../components/NavLink';
import Image from '../../components/Image';
import { Props } from './types';

const HomePageHeader = ({ blok }: Props) => {
  const [open, setOpen] = useState(false);

  return (
    <div className="relative lg:mt-20 mt-24 pb-12" {...storyblokEditable(blok)}>
      {blok.desktop_background_image?.filename && (
        <div className="right-0 hidden lg:block absolute top-0 left-1/2 -translate-x-1/2 w-full max-w-screen-2xl h-[610px] 2xl:h-[700px]">
          <Image
            src={blok.desktop_background_image?.filename}
            alt={blok.desktop_background_image?.alt}
            isContain
          />
        </div>
      )}
      {blok.mobile_background_image?.filename && (
        <div className="lg:hidden">
          <Image
            src={blok.mobile_background_image?.filename}
            alt={blok.mobile_background_image?.alt}
            isContain
            classname="w-full h-[221px] !min-h-[221px]"
          />
        </div>
      )}

      <Container>
        <div className="flex w-full flex-col col-span-full items-center py-9 lg:z-[100] lg:py-28 2xl:pb-44">
          <div className="flex gap-3 items-center mb-4">
            {blok.feature_label.length > 0 && (
              <Badge
                variant={blok.feature_label[0].variant}
                size={blok.feature_label[0].size}
                iconName={blok.feature_label[0].icon}
              >
                {blok.feature_label[0].text}
              </Badge>
            )}
            {blok.feature_link && (
              <div className="">
                {blok.feature_link.map((link) => {
                  const href = link.target?.cached_url;
                  const page = href;
                  const featureLink = link.target?.anchor
                    ? `/${page}#${link.target?.anchor}`
                    : `/${page}`;

                  return (
                    <NavLink to={featureLink} key={link._uid}>
                      <Hyperlink
                        className="text-sm font-bold px-0 !text-neutral-700 hover:!text-neutral-800 group"
                        size={link.size}
                        RenderAs="div"
                      >
                        <div className="flex gap-2 items-center">
                          {link.text}
                          <Icon
                            size="sm"
                            name="HiChevronLeft"
                            className="rotate-180 !text-neutral-400 group-hover:!text-neutral-800"
                          />
                        </div>
                      </Hyperlink>
                    </NavLink>
                  );
                })}
              </div>
            )}
          </div>

          <div className="max-w-sm text-center lg:max-w-2xl">
            <h1 className="text-4xl font-medium text-neutral-900 lg:text-6xl">{blok.title}</h1>
            <div className="mt-7 text-neutral-600 lg:text-lg">
              <RichText document={blok.description} />
            </div>
          </div>

          {blok.actions && (
            <div
              className={classNames({
                'flex gap-4 flex-col md:flex-row items-center mt-5 w-full md:w-auto':
                  blok.actions.length > 0,
              })}
            >
              {blok.actions.map((button) => {
                const href = button.target?.cached_url;
                const page = href;
                const urlLink = button?.target?.anchor
                  ? `/${page}#${button?.target?.anchor}`
                  : `/${page}`;

                if (button.isDemo) {
                  return (
                    <div key={button._uid} className="w-full lg:w-auto">
                      <Button
                        size={button.size}
                        variant={button.variant}
                        RenderAs="button"
                        onClick={() => {
                          setOpen(true);
                        }}
                        expand
                      >
                        {button.text}
                      </Button>

                      {typeof window !== 'undefined' && (
                        <PopupModal
                          url={button.target.url}
                          onModalClose={() => {
                            setOpen(false);
                          }}
                          open={open}
                          rootElement={document.getElementById('__next') as HTMLElement}
                        />
                      )}
                    </div>
                  );
                }

                return (
                  <NavLink to={urlLink} key={button._uid} linkClassName="w-full md:w-auto">
                    <Button RenderAs="div" size={button.size} variant={button.variant} expand>
                      {button.text}
                    </Button>
                  </NavLink>
                );
              })}
            </div>
          )}
        </div>
      </Container>
      {blok.logo_slider && (
        <div className="md:mt-4">
          {blok.logo_slider?.map((nestedBlok) => (
            <StoryblokComponent blok={nestedBlok} key={nestedBlok._uid} />
          ))}
        </div>
      )}
    </div>
  );
};

export default HomePageHeader;
