import { Button } from '@bramvanuldenn/whatson-components';
import { storyblokEditable } from '@storyblok/react';
import classNames from 'classnames';
import Container from '../../components/Container';
import Image from '../../components/Image';
import NavLink from '../../components/NavLink';
import RichText from '../../components/RichText';
import Tag from '../../components/Tag';
import { Action } from '../../types/types';
import { Props } from './types';

const FeatureHeader = ({ blok }: Props) => (
  <div
    {...storyblokEditable(blok)}
    className={classNames({
      'pt-16 pb-56 mb-40 lg:mb-0 lg:pt-32 lg:pb-0': true,
      'bg-brand-orange-50': blok.background_color === 'orange',
      'bg-yellow-50': blok.background_color === 'yellow',
      'bg-violet-50': blok.background_color === 'violet',
      'bg-teal-50': blok.background_color === 'teal',
    })}
  >
    <Container>
      <div className="col-span-full mt-8 lg:mt-0 flex flex-col items-center lg:flex-none lg:items-start lg:col-start-2 lg:col-end-7">
        <Tag color={blok.tag_color} label={blok.tag} classname="mb-2.5 lg:mb-5" />
        <h1 className="mt-7 lg:mt-0 text-4xl text-center lg:text-left lg:text-6xl font-medium text-neutral-900">
          {blok.title}
        </h1>
        <RichText
          document={blok.description}
          className="mt-5 text-xl text-center lg:text-left text-neutral-900"
        />
        <div className="mt-9 lg:flex lg:items-center lg:gap-3 lg:mb-32 w-full">
          {blok.actions?.map((action: Action) => {
            const href = action.target?.cached_url;
            const page = href;
            const urlLink = action?.target?.anchor
              ? `/${page}#${action?.target?.anchor}`
              : `/${page}`;

            return (
              <div key={action._uid}>
                <div className="hidden lg:block">
                  <NavLink to={urlLink} key={action._uid}>
                    <Button size={action.size} variant={action.variant} RenderAs="div">
                      {action.text}
                    </Button>
                  </NavLink>
                </div>
                <div className="mb-3 lg:hidden">
                  <NavLink to={urlLink} key={action._uid}>
                    <Button
                      size={action.size}
                      variant={action.variant}
                      RenderAs="div"
                      expand={true}
                    >
                      {action.text}
                    </Button>
                  </NavLink>
                </div>
              </div>
            );
          })}
        </div>
      </div>
      <div className="col-span-full lg:col-start-8 lg:col-end-13">
        <div className="w-full h-full relative">
          <div className="hidden lg:block absolute top-0 left-0 right-0 lg:-bottom-14">
            <Image src={blok.image?.filename} alt={blok.image?.alt} isContain />
          </div>
          <div className="lg:hidden absolute left-0 right-0 top-10 -bottom-[330px]">
            <Image src={blok.image?.filename} alt={blok.image?.alt} isContain />
          </div>
        </div>
      </div>
    </Container>
  </div>
);

export default FeatureHeader;
