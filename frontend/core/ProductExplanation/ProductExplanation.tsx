import { storyblokEditable } from '@storyblok/react';
import React from 'react';
import Container from '../../components/Container';
import RichText from '../../components/RichText';
import { ReactComponent as PersonTag } from '../../public/svg/person-tag.svg';
import { ReactComponent as WebsiteTag } from '../../public/svg/website-tag.svg';
import { ReactComponent as CompanyTag } from '../../public/svg/company-tag.svg';
import { ReactComponent as PlaceTag } from '../../public/svg/place-tag.svg';
import { Props, LabelItemProp } from './types';

const LabelItem = ({ items, tag }: LabelItemProp) => {
  const TagToShow = {
    person: <PersonTag />,
    website: <WebsiteTag />,
    company: <CompanyTag />,
    place: <PlaceTag />,
  };

  return (
    <div className="col-span-full mt-10 lg:mt-20 lg:col-span-3">
      <div className="w-[130px] mb-4 lg:mb-8">{TagToShow[tag]}</div>
      {items.map((item) => (
        <div className="mb-4 lg:mb-10 last:mb-0 ml-1.5" key={item._uid}>
          <span className="block lg:text-xl font-bold text-neutral-900">{item.title}</span>
          <span className="block lg:text-base text-neutral-500">{item.example}</span>
        </div>
      ))}
    </div>
  );
};

const ProductExplanation = ({ blok }: Props) => (
  <div {...storyblokEditable(blok)} className="my-[72px] lg:my-28">
    <div id="product-explanation" className="block relative -top-44 invisible" />
    <Container>
      <div className="col-span-full">
        <div className="flex flex-col items-center text-center">
          <span className="text-sm lg:text-lg font-medium text-neutral-600">{blok.tag}</span>
        </div>
      </div>
      <div className="col-span-full lg:col-start-3 lg:col-end-11 flex flex-col items-center text-center">
        <RichText
          document={blok.title}
          className="mt-2 text-2xl lg:text-5xl font-medium text-neutral-900"
        />
        <RichText
          document={blok.description}
          className="mt-6 text-base lg:text-xl text-neutral-600"
        />
      </div>
      {blok.person_items && <LabelItem items={blok.person_items} tag="person" />}
      {blok.website_items && <LabelItem items={blok.website_items} tag="website" />}
      {blok.company_items && <LabelItem items={blok.company_items} tag="company" />}
      {blok.place_items && <LabelItem items={blok.place_items} tag="place" />}
    </Container>
  </div>
);

export default ProductExplanation;
