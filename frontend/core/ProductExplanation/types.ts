type LabelItem = {
  _uid: string;
  example: string;
  title: string;
};

type ProductExplanationBlok = {
  tag: string;
  title: any;
  description: any;
  company_items: LabelItem[];
  person_items: LabelItem[];
  place_items: LabelItem[];
  website_items: LabelItem[];
  _uid: string;
};

export type LabelItemProp = {
  tag: 'person' | 'company' | 'website' | 'place';
  items: LabelItem[];
};

export type Props = {
  blok: ProductExplanationBlok;
};
