import { storyblokEditable } from '@storyblok/react';
import { Button, inputs } from '@bramvanuldenn/whatson-components';
import React, { useState, ChangeEvent } from 'react';
import Image from 'next/image';
import { Props } from './types';
import RichText from '../../components/RichText';
import { validateEmail } from '../../utils/validation';

const { Input, InputFeedback } = inputs;

const ArticleCta = ({ blok }: Props) => {
  const [email, setEmail] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  const handleClick = (event: React.MouseEvent<HTMLElement, MouseEvent>) => {
    event.preventDefault();

    setErrorMessage('');

    if (!email) {
      setErrorMessage('Please fill in your email address');

      return;
    }

    const emailValidation = validateEmail(email);

    if (!emailValidation) {
      setErrorMessage('Please fill in your correct email address');

      return;
    }

    if (email && emailValidation) {
      setErrorMessage('');

      const href = blok.actions[0].target?.cached_url;
      const page = href;
      const urlLink = blok.actions[0]?.target?.anchor
        ? `/${page}#${blok.actions[0]?.target?.anchor}`
        : page;

      document.location.href = urlLink;
    }
  };

  return (
    <div
      className="grid grid-cols-6 lg:grid-cols-12 max-w-screen-xl relative md:py-20 "
      {...storyblokEditable(blok)}
      id="article-cta"
    >
      <div className="absolute -top-10 md:top-0 lg:right-12 right-0 md:min-w-[184px] md:min-h-[184px] min-w-[74px] min-h-[74px]">
        <Image
          src="/images/Lightbulb.png"
          alt="bulb"
          fill
          sizes="(max-width: 184px) 100vw,
              (max-width: 184px) 50vw,
              33vw"
        />
      </div>
      <div className="lg:left-16 left-0 md:bottom-0 -bottom-10 absolute md:min-w-[160px] md:min-h-[160px] min-w-[80px] min-h-[80px] -rotate-6 md:rotate-0 transform">
        <Image
          src="/images/To-do.png"
          alt="to do"
          fill
          sizes="(max-width: 160px) 100vw,
              (max-width: 160px) 50vw,
              33vw"
        />
      </div>

      <div className="lg:col-start-2 lg:col-end-12 col-span-full flex justify-center items-center flex-col bg-brand-orange-500 w-full py-12 rounded-xl px-7 lg:px-0">
        <h2 className="text-2xl font-medium text-white mb-2">{blok.title}</h2>
        {blok.description && (
          <RichText
            document={blok.description}
            className="text-white text-sm"
            listType="checkList"
          />
        )}

        <div className="flex flex-col lg:flex-row gap-2 items-baseline mt-6">
          <div className="w-[280px]">
            <Input
              type="email"
              placeholder="Enter your work email"
              hasError={!!errorMessage}
              value={email}
              onChange={(e: ChangeEvent<HTMLInputElement>) => setEmail(e.target.value)}
            />
            {errorMessage && (
              <InputFeedback
                type="error"
                message={errorMessage}
                className="text-xs text-left !text-white"
              />
            )}
          </div>

          {blok.actions.map((button) => (
            <div className="w-full lg:w-auto" key={button._uid}>
              <Button
                RenderAs="button"
                size={button.size}
                variant={button.variant}
                onClick={handleClick}
                expand
              >
                {button.text}
              </Button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ArticleCta;
