import { storyblokEditable } from '@storyblok/react';
import classNames from 'classnames';
import Container from '../../components/Container/Container';
import ProfileCard from '../../components/ProfileCard';
import { Props } from './type';

const Team = ({ blok }: Props) => (
  <Container {...storyblokEditable(blok)} className="my-[72px] lg:my-28">
    {blok.profile_cards && (
      <div
        className={classNames({
          'lg:col-start-2 lg:col-end-12 col-span-full': blok.isFoundersBlok,
          'lg:col-start-3 lg:col-end-11 col-span-full': !blok.isFoundersBlok,
        })}
      >
        {blok.title && <h3 className="md:text-4xl text-3xl mb-16 md:text-center">{blok.title}</h3>}
        <div className="flex flex-col md:flex-row gap-x-10 gap-y-14 flex-wrap">
          {blok.profile_cards.map((item) => (
            <ProfileCard
              key={item._uid}
              image={item.image}
              name={item.name}
              position={item.position}
              bio={item.bio}
              hover_image={item.hover_image}
              background_color={item.background_color}
              about_actions={item.about_actions}
              card_link={item.card_link}
            />
          ))}
        </div>
      </div>
    )}
  </Container>
);

export default Team;
