import type { Media } from '../../types/types';

export type ImageBlok = {
  component: string;
  desktop_image: Media;
  mobile_image: Media;
  _uid: string;
  placeholder?: boolean;
};

export type ArticleImagesBlok = {
  component: string;
  _uid: string;
  images: ImageBlok[];
  caption: any;
  layout: 'vertical' | 'horizontal';
  anchor_id?: string;
  isInArticle: boolean;
};
export type Props = {
  blok: ArticleImagesBlok;
};
