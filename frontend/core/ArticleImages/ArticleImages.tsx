import { storyblokEditable } from '@storyblok/react';
import classNames from 'classnames';
import Image from '../../components/Image';
import { Props } from './types';
import { useRatioHeight } from '../../utils/useRatioHeight';
import { useScreenWidth } from '../../utils/useScreenWidth';
import { CONSTANTS } from '../../constants/constants';

const ArticleImages = ({ blok }: Props) => {
  const { ref: assetRef, height: assetHeight } = useRatioHeight('landscape');
  const screenWidth = useScreenWidth();

  return (
    <div
      className={classNames({
        'grid grid-cols-6 lg:grid-cols-12 gap-x-5 lg:gap-x-10 max-w-screen-xl': blok.isInArticle,
      })}
      {...storyblokEditable(blok)}
      id={blok.anchor_id}
    >
      <div
        className={classNames({
          'flex flex-wrap gap-5 lg:gap-10': blok.images.length !== 1,
          'lg:col-start-3 lg:col-end-11 col-span-full':
            blok.isInArticle && blok.images.length !== 1,
          'lg:col-start-2 lg:col-end-12 col-span-full':
            blok.isInArticle && blok.images.length === 1,
        })}
      >
        {blok.images &&
          blok.images.map((image, index) => {
            const media = image.mobile_image?.filename
              ? screenWidth < CONSTANTS.mdWidth
                ? image.mobile_image
                : image.desktop_image
              : image.desktop_image;

            return (
              <div
                className={classNames({
                  'w-[calc(50%_-_10px)] md:w-[calc(50%_-_20px)]': blok.images.length !== 1,
                  'w-full': blok.images.length === 1,
                })}
                key={index}
              >
                <div
                  ref={assetRef}
                  className={classNames({
                    'rounded-tl-0 rounded-tr-[20px] rounded-b-[20px] overflow-hidden': true,
                    'md:min-h-[222px] min-h-[113px]':
                      blok.layout === 'horizontal' && blok.images.length !== 1,
                    'md:min-h-[474px] min-h-[242px]':
                      blok.layout === 'vertical' && blok.images.length !== 1,
                    'md:max-h-[558px] min-h-[222px]': blok.images.length === 1,
                  })}
                  style={{ height: blok.images.length === 1 ? assetHeight : '100%' }}
                >
                  <Image src={media?.filename} alt={media?.alt} />
                </div>
              </div>
            );
          })}
      </div>
      {blok.caption && (
        <span className="text-neutral-500 text-sm text-center items-center flex justify-center w-full col-span-full mt-3">
          {blok.caption}
        </span>
      )}
    </div>
  );
};

export default ArticleImages;
