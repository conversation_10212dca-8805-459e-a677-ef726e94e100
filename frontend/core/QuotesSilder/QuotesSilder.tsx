import { storyblokEditable } from '@storyblok/react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination } from 'swiper';
import classNames from 'classnames';
import { Button } from '@bramvanuldenn/whatson-components';
import { Props } from './types';
// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import QuoteSliderItem from '../../components/QuoteSliderItem';
import Container from '../../components/Container';

const QuotesSilder = ({ blok }: Props) => (
  <div {...storyblokEditable(blok)} className="w-full" id={blok.anchor_id}>
    {blok.component === 'case_header' ? (
      <div className="w-full relative">
        <>
          {blok.highlighted_cases?.length > 0 && (
            <div className="md:absolute md:top-1/2 md:-translate-y-1/2 z-10 flex w-full justify-between left-0">
              <Button
                size="xl"
                variant="secondary"
                RenderAs="div"
                icon="HiArrowLeft"
                className="image-swiper-button-prev absolute md:relative -left-5 z-10 top-28 md:top-auto md:left-auto"
              />
              <Button
                size="xl"
                variant="secondary"
                RenderAs="div"
                className="image-swiper-button-next absolute md:relative -right-5 z-10 top-28 md:top-auto md:right-auto"
                icon="HiArrowRight"
              />
            </div>
          )}
          <Swiper
            grabCursor={true}
            slidesPerView={1}
            navigation={{
              disabledClass: 'image-swiper-button-disabled',
              nextEl: '.image-swiper-button-next',
              prevEl: '.image-swiper-button-prev',
            }}
            modules={[Navigation]}
            centeredSlides={true}
          >
            {blok.highlighted_cases?.map((item) => (
              <SwiperSlide className="md:px-20 lg:px-36" key={item.uuid}>
                <QuoteSliderItem
                  author={item?.content?.author}
                  quote={item?.content?.quote}
                  mobile_media={item?.content?.mobile_media}
                  desktop_media={item?.content?.desktop_media}
                  media_type={item?.content?.media_type}
                  position={item?.content?.position}
                  isInHeader
                  to={item.full_slug}
                  logo={item?.content?.logo}
                />
              </SwiperSlide>
            ))}
          </Swiper>
        </>
      </div>
    ) : blok.component === 'quote_with_background' ? (
      <Container
        {...storyblokEditable(blok)}
        className={classNames({
          'py-[72px] lg:py-28': !blok.isInCase,
        })}
        no_margin={blok.isInCase}
      >
        <div
          className={classNames({
            'lg:col-start-2 lg:col-end-12 col-span-full': !blok.isInCase,
            'col-span-full': blok.isInCase,
            'rounded-xl relative': true,
          })}
        >
          <>
            {blok.quotes?.length > 0 && (
              <div
                className={classNames({
                  'absolute -bottom-20 right-1/2 translate-x-1/2 lg:-top-8 lg:right-0 z-50 flex lg:translate-x-0 lg:bottom-auto gap-3':
                    true,
                })}
              >
                <Button
                  size="xl"
                  variant="primary"
                  RenderAs="div"
                  icon="HiArrowLeft"
                  className="quote-swiper-button-prev"
                />
                <Button
                  size="xl"
                  variant="primary"
                  RenderAs="div"
                  className="quote-swiper-button-next"
                  icon="HiArrowRight"
                />
              </div>
            )}
            <div className="quote-swiper-pagination absolute !-bottom-8 z-50 items-center w-full lg:flex justify-center gap-2 hidden"></div>
            <Swiper
              grabCursor={true}
              className="quotes-swiper"
              slidesPerView={1}
              navigation={{
                disabledClass: 'quote-swiper-button-disabled',
                nextEl: '.quote-swiper-button-next',
                prevEl: '.quote-swiper-button-prev',
              }}
              modules={[Navigation, Pagination]}
              centeredSlides={true}
              pagination={{
                clickable: true,
                el: `.quote-swiper-pagination`,
              }}
            >
              {blok.quotes?.map((quote) => (
                <SwiperSlide key={quote._uid}>
                  <QuoteSliderItem
                    author={quote?.author}
                    quote={quote?.text}
                    mobile_media={quote.mobile_media}
                    desktop_media={quote.desktop_media}
                    media_type={quote.media_type}
                    position={quote?.position}
                    logo={quote?.logo}
                    numbers={quote.numbers}
                    actions={quote.actions}
                    withBackground
                    className="lg:mt-12 mt-24"
                  />
                </SwiperSlide>
              ))}
            </Swiper>
          </>
        </div>
      </Container>
    ) : null}
  </div>
);

export default QuotesSilder;
