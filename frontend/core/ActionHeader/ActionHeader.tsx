import { storyblokEditable } from '@storyblok/react';
import { Button, Icon } from '@bramvanuldenn/whatson-components';
import Container from '../../components/Container/Container';
import NavLink from '../../components/NavLink/NavLink';
import { HeaderProps } from './types';

const ActionHeader = ({ blok }: HeaderProps) => (
  <main {...storyblokEditable(blok)} className="mb-10">
    <Container no_margin className="!px-0">
      <div className="col-span-full flex justify-between md:items-center flex-col md:flex-row gap-4 md:gap-0">
        <h2 className="text-neutral-900 font-medium text-xl md:text-2xl lg:text-4xl">
          {blok.title}
        </h2>
        {blok.actions.map((button) => {
          const href = button.target?.cached_url;
          const page = href;
          const urlLink = button?.target?.anchor
            ? `/${page}#${button?.target?.anchor}`
            : `/${page}`;

          return (
            <NavLink to={urlLink} key={button._uid}>
              <Button RenderAs="div" size={button.size} variant={button.variant}>
                <div className="flex items-center gap-2">
                  {button.text}
                  <Icon name="HiArrowRight" size="base" />
                </div>
              </Button>
            </NavLink>
          );
        })}
      </div>
    </Container>
  </main>
);

export default ActionHeader;
