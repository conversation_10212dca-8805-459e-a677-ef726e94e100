import React, { useState } from 'react';
import classNames from 'classnames';
import { storyblokEditable } from '@storyblok/react';
import { Props } from './types';
import Image from '../../components/Image';

const LogoSlider = ({ blok }: Props) => {
  const [hover, setHover] = useState(false);

  return (
    <div
      className={classNames({
        'w-full': true,
        'my-[72px] lg:my-28': blok.title,
      })}
    >
      {blok.title && <h2 className="text-center text-xl font-medium mb-6">{blok.title}</h2>}
      <div
        className="relative overflow-hidden h-20 "
        onMouseEnter={() => setHover(true)}
        onMouseLeave={() => setHover(false)}
        {...storyblokEditable(blok)}
      >
        <div className="relative z-20">
          <div className="bg-gradient-to-r from-white via-white/60 to-transparent absolute top-0 left-0 w-1/2 h-20"></div>
          <div className="bg-gradient-to-l from-white via-white/60 to-transparent absolute top-0 right-0 w-1/2 h-20"></div>
        </div>

        {blok.logos && (
          <div
            className={classNames({
              'flex items-center col-span-full relative slide-track': true,
              'slide-pause': hover,
            })}
          >
            {[...blok.logos, ...blok.logos].map((logo, index) => (
              <div key={index} className="flex-1">
                <Image
                  src={logo.filename}
                  alt={logo.alt}
                  isContain
                  classname="w-full h-20 !min-h-[80px]"
                />
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default LogoSlider;
