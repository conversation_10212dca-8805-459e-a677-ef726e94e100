import React, { ChangeEvent, useState } from 'react';
import { storyblokEditable } from '@storyblok/react';
import { Button, Logo, inputs, Alert } from '@bramvanuldenn/whatson-components';
import Container from '../../components/Container';
import RichText from '../../components/RichText';
import { Props } from './types';
import Image from '../../components/Image';
import { validateEmail } from '../../utils/validation';

const { Input, InputFeedback } = inputs;

type GeneralMessage = {
  variant: 'positive' | 'negative' | 'warning' | undefined;
  message: string;
};

const PreLaunch = ({ blok }: Props) => {
  const [userInput, setUserInput] = useState({
    name: '',
    email: '',
  });

  const [errorMessages, setErrorMessages] = useState({
    name: '',
    email: '',
  });

  const [generalMessage, setGeneralMessage] = useState<GeneralMessage>({
    variant: undefined,
    message: '',
  });

  const handleClick = (event: React.MouseEvent<HTMLElement, MouseEvent>) => {
    event.preventDefault();

    setErrorMessages({ name: '', email: '' });

    if (!userInput.name) {
      setErrorMessages({ ...errorMessages, name: 'Please fill in your name' });

      return;
    }

    if (!userInput.email) {
      setErrorMessages({ ...errorMessages, email: 'Please fill in your email address' });

      return;
    }

    const emailValidation = validateEmail(userInput.email);

    if (!emailValidation) {
      setErrorMessages({ ...errorMessages, email: 'Please fill in your correct email address' });

      return;
    }

    if (userInput.name && userInput.email && emailValidation) {
      setErrorMessages({
        name: '',
        email: '',
      });

      // call API
      try {
        fetch('/api/launch-event', {
          body: JSON.stringify({
            email: userInput.email,
            name: userInput.name,
          }),
          headers: {
            Accept: 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
          },
          method: 'POST',
        })
          .then((response) => response.json())
          .then((data) => {
            const result = data;

            if (result.ok) {
              setUserInput({ name: '', email: '' });
              setGeneralMessage({ variant: 'positive', message: result.message });
            } else {
              setUserInput({ name: '', email: '' });
              setGeneralMessage({ variant: 'negative', message: result.message });
            }
          })
          .catch((error) => {
            // eslint-disable-next-line no-console
            console.log(error);
            setGeneralMessage({ variant: 'negative', message: error.message });
          });
      } catch (error: any) {
        setGeneralMessage({ variant: 'negative', message: error.message });
      }
    }
  };

  return (
    <div {...storyblokEditable(blok)} className="max-w-screen-xl mx-auto">
      <Container>
        <div className="col-span-full md:col-start-4 md:col-end-10 flex flex-col items-center">
          <div className="w-[318px] h-[229px] mt-[42px]">
            <Image src={blok.image?.filename} alt={blok.image?.alt} />
          </div>
          <h1 className="mt-5 text-[32px] font-medium text-center text-neutral-900">
            {blok.title}
          </h1>
          <div className="mt-5 text-center text-base text-neutral-600 mx-20 md:mx-4">
            <RichText document={blok.description} />
          </div>
          <div className="mt-9 w-[250px]">
            <Input
              type="text"
              required={true}
              label="Name"
              placeholder="Your name"
              hasError={!!errorMessages.name}
              value={userInput.name}
              onChange={(e: ChangeEvent<HTMLInputElement>) =>
                setUserInput({
                  ...userInput,
                  name: e.target.value,
                })
              }
            />

            {errorMessages.name && (
              <InputFeedback
                type="error"
                message={errorMessages.name}
                className="text-xs text-left"
              />
            )}

            <Input
              className="mt-3"
              type="email"
              label="Email address"
              placeholder="<EMAIL>"
              hasError={!!errorMessages.email}
              value={userInput.email}
              onChange={(e: ChangeEvent<HTMLInputElement>) =>
                setUserInput({
                  ...userInput,
                  email: e.target.value,
                })
              }
            />
            {errorMessages.email && (
              <InputFeedback
                type="error"
                message={errorMessages.email}
                className="text-xs text-left"
              />
            )}
            {generalMessage.message && (
              <Alert className="mt-5" size="large" variant={generalMessage.variant}>
                {generalMessage.message}
              </Alert>
            )}
            <div className="mt-5">
              <Button size="base" variant="primary" RenderAs="button" onClick={handleClick} expand>
                {blok.button_label}
              </Button>
            </div>
          </div>

          <div className="mt-[72px]">
            <Logo />
          </div>

          <div className="mt-6 text-center text-base text-neutral-600 mx-20 md:mx-4">
            <RichText document={blok.description_after_form} />
          </div>
          <div className="mt-6">
            <span className="text-xs font-bold text-center">
              Whatson B.V. <span className="font-normal">©</span>
            </span>
          </div>
          <div className="mt-4 mb-4 text-xs text-neutral-600">
            <span className="block text-center">Savannahweg 66 | 3542AW Utrecht</span>
            <span className="block text-center">+31 (0) 302003467</span>
            <span className="block text-center"><EMAIL></span>
          </div>
        </div>
      </Container>
    </div>
  );
};

export default PreLaunch;
