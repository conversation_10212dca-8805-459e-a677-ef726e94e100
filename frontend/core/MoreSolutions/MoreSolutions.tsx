import { Button } from '@bramvanuldenn/whatson-components';
import { storyblokEditable } from '@storyblok/react';
import classNames from 'classnames';
import React, { useState } from 'react';
import Image from '../../components/Image';
import NavLink from '../../components/NavLink';
import Tag from '../../components/Tag';
import { CONSTANTS } from '../../constants/constants';
import { useRatioHeight } from '../../utils/useRatioHeight';
import { useScreenWidth } from '../../utils/useScreenWidth';
import { Props, Solution } from './types';

const SolutionCard = ({ image, tag_color, tag, link, title }: Solution) => {
  const { ref: assetRef, height: assetHeight } = useRatioHeight('');
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      ref={assetRef}
      style={{ height: assetHeight }}
      className="w-full relative"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Image src={image?.filename} alt={image?.alt} />
      <div className="absolute top-9 left-8 right-8 z-10">
        <div className="w-full flex justify-between items-center">
          <Tag color={tag_color} label={tag} />
          <NavLink to={link.cached_url}>
            <Button
              icon="HiArrowRight"
              size="lg"
              variant="secondary"
              RenderAs="div"
              className={classNames({
                '!rounded-full !border-none': true,
                '!bg-neutral-300 !text-neutral-50': !isHovered,
                '!bg-brand-pink-500 !text-white': isHovered,
              })}
            />
          </NavLink>
        </div>
        <div className="mt-5 text-white text-3xl font-medium">{title}</div>
      </div>
      <div
        className={classNames({
          'absolute top-0 bottom-0 left-0 right-0 bg-gradient-to-b': true,
          'from-gradient-black': !isHovered,
          'from-brand-pink-500 opacity-50': isHovered,
        })}
      ></div>
    </div>
  );
};

const MoreSolutions = ({ blok }: Props) => {
  const { ref: assetRef, height: assetHeight } = useRatioHeight('');
  const screenWidth = useScreenWidth();
  const isMobile = screenWidth < CONSTANTS.mdWidth;
  const isTablet = screenWidth < CONSTANTS.lgWidth;
  const divHeight = isMobile ? assetHeight : assetHeight + 112 - 10;

  return (
    <div
      {...storyblokEditable(blok)}
      className="mt-[72px] mb-28 lg:mb-0 lg:mt-28 z-0"
      style={{ height: isMobile || isTablet ? '100%' : divHeight }}
    >
      <div className="flex flex-col items-start mx-5 lg:mx-0 lg:items-center text-4xl lg:text-5xl font-bold text-neutral-900">
        {blok.title}
      </div>
      <div className="w-full mt-8 lg:mt-20 lg:flex">
        {blok.solutions?.map((item) => (
          <div
            ref={assetRef}
            style={{ height: assetHeight }}
            className="w-full relative"
            key={item._uid}
          >
            <SolutionCard
              image={item.image}
              tag_color={item.tag_color}
              tag={item.tag}
              link={item.link}
              title={item.title}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default MoreSolutions;
