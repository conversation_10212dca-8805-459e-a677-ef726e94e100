import { storyblokEditable } from '@storyblok/react';
import React from 'react';
import Container from '../../components/Container';
import TimerSteps from '../../components/TimerSteps';
import { Props } from './types';

const TimerImageText = ({ blok }: Props) => {
  const steps = blok.steps
    ? blok.steps.map((item: any, index: number) => ({
        desktop_image: item.desktop_image,
        mobile_image: item.mobile_image,
        isOpen: index === 0,
        label: index,
        title: item.title,
        description: item.description,
        icon: item.icon,
        icon_color: item.icon_color,
      }))
    : [];

  return (
    <>
      <div id="timer-with-asset" className="block relative -top-44 invisible" />
      <Container {...storyblokEditable(blok)} className="my-[72px] lg:my-28">
        <TimerSteps steps={steps} title={blok.title} />
      </Container>
    </>
  );
};

export default TimerImageText;
