import { storyblokEditable } from '@storyblok/react';
import { Button } from '@bramvanuldenn/whatson-components';
import { useState } from 'react';
import { PopupModal } from 'react-calendly';
import { Props } from './types';
import Container from '../../components/Container';
import RichText from '../../components/RichText';
import NavLink from '../../components/NavLink';

const Cta = ({ blok }: Props) => {
  const [open, setOpen] = useState(false);

  return (
    <Container {...storyblokEditable(blok)} className="my-[72px] lg:my-28">
      <div
        className="lg:col-start-2 lg:col-end-12 col-span-full flex flex-col lg:flex-row lg:items-center gap-8 lg:gap-0 bg-neutral-900 justify-between w-full py-8 px-12 rounded-xl"
        id="cta"
      >
        <div>
          <h2 className="text-2xl font-medium text-white mb-4">{blok.title}</h2>
          <span className="text-sm text-white">{blok.subTitle}</span>
        </div>
        {blok.description && (
          <RichText
            document={blok.description}
            className="text-white text-sm"
            listType="checkList"
          />
        )}

        <div className="flex flex-col lg:flex-row lg:items-center gap-8 h-full items-center">
          <div className="lg:border-l border-t lg:border-neutral-700 w-full lg:w-auto lg:h-full"></div>

          {blok.actions?.map((action) => {
            const href = action.target?.cached_url;
            const page = href;
            const urlLink = action?.target?.anchor
              ? `/${page}#${action?.target?.anchor}`
              : `/${page}`;

            if (action.isDemo) {
              return (
                <div key={action._uid} className="w-full lg:w-auto">
                  <Button
                    size={action.size}
                    variant={action.variant}
                    RenderAs="button"
                    onClick={() => {
                      setOpen(true);
                    }}
                    expand
                  >
                    {action.text}
                  </Button>

                  {typeof window !== 'undefined' && (
                    <PopupModal
                      url={action.target.url}
                      onModalClose={() => {
                        setOpen(false);
                      }}
                      open={open}
                      rootElement={document.getElementById('__next') as HTMLElement}
                    />
                  )}
                </div>
              );
            }

            return (
              <NavLink to={urlLink} key={action._uid} linkClassName="w-full lg:w-auto">
                <Button
                  RenderAs="div"
                  size={action.size}
                  variant={action.variant}
                  className="!px-14"
                  expand
                >
                  {action.text}
                </Button>
              </NavLink>
            );
          })}
        </div>
      </div>
    </Container>
  );
};

export default Cta;
