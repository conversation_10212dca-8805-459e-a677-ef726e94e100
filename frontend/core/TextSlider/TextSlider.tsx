import { storyblokEditable } from '@storyblok/react';
import classNames from 'classnames';
import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper';
import Container from '../../components/Container';
import RichText from '../../components/RichText';
import { Props } from './types';
import { ReactComponent as Ellipse } from '../../public/svg/ellipse.svg';
// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import { useScreenWidth } from '../../utils/useScreenWidth';

const TextSlider = ({ blok }: Props) => {
  const screenWidth = useScreenWidth();
  const isBiggerThanMaxWidth = screenWidth > 1280;
  const desktopSliderMarginLeft = isBiggerThanMaxWidth ? (screenWidth - 1280) / 2 + 40 : 40;

  return (
    <div {...storyblokEditable(blok)} className="my-[72px] lg:my-28">
      <Container>
        <div className="col-span-full lg:col-start-3 lg:col-end-11 flex flex-col lg:items-center">
          <h2 className="text-3xl lg:text-5xl font-medium text-neutral-900">{blok.title}</h2>
          <RichText
            document={blok.description}
            className="mt-4 lg:mt-6 text-base lg:text-xl text-neutral-600 lg:text-center"
          />
        </div>
      </Container>
      <div className="mt-16 hidden lg:block relative">
        <Swiper
          grabCursor={true}
          navigation={{
            disabledClass: 'swiper-button-disabled',
            nextEl: '.image-swiper-button-next',
            prevEl: '.image-swiper-button-prev',
          }}
          slidesPerView={'auto'}
          modules={[Navigation]}
          className="swiper-container-our-history"
        >
          {blok.text_boxes &&
            blok.text_boxes.map((item, index) => (
              <SwiperSlide
                key={item._uid}
                style={{ marginLeft: index === 0 ? desktopSliderMarginLeft : 0 }}
              >
                <div className="flex items-center">
                  <div className="z-20">
                    <Ellipse />
                  </div>
                  {blok.text_boxes?.length - 1 !== index && (
                    <div className="border border-neutral-200 w-full" />
                  )}
                </div>
                <div className="mr-14 mt-6">
                  <h6 className="text-2xl font-bold text-neutral-900">{item.year}</h6>
                  <RichText document={item.description} className="mt-4 text-lg text-neutral-500" />
                </div>
              </SwiperSlide>
            ))}
        </Swiper>
      </div>
      <div className="lg:hidden mx-5 mt-6">
        {blok.text_boxes?.map((item, index) => (
          <div
            className={classNames({
              'flex gap-6 items-start relative ml-2': true,
              'border border-l border-r-0 border-t-0 border-b-0 border-neutral-200':
                blok.text_boxes?.length - 1 !== index,
            })}
            key={item._uid}
          >
            <div className="absolute top-0 -left-2">
              <Ellipse />
            </div>
            <div className="ml-8 pb-10">
              <h6 className="text-2xl font-bold text-neutral-900">{item.year}</h6>
              <RichText document={item.description} className="mt-4 text-lg text-neutral-500" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TextSlider;
