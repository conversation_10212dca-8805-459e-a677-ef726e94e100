import { storyblokEditable } from '@storyblok/react';
import { useStories } from '../../utils/useStories';
import { Props } from './type';
import ArticleCard from '../../components/ArticleCard/ArticleCard';
import Container from '../../components/Container/Container';
import type { Link } from '../../types/types';

const BlogOverviewPage = ({ blok }: Props) => {
  const stories = useStories('blog/');
  const articleCards: Link[] = stories.filter((card: Link) => {
    if (card.content.component === 'blog_page' && card.name !== blok.highlighted_article.name) {
      return card;
    }
  });

  const highlightedDate = new Date(blok.highlighted_article?.published_at)?.toLocaleDateString(
    'en-GB',
    {
      day: '2-digit',
      month: 'short',
    }
  );

  return (
    <div {...storyblokEditable(blok)} className="bg-white py-24 md:py-32">
      <Container>
        <>
          {blok.highlighted_article && (
            <div className="col-span-full lg:col-start-2 lg:col-end-12">
              <ArticleCard
                time_to_read={blok.highlighted_article.content?.time_to_read}
                desktop_media={blok.highlighted_article.content?.desktop_media}
                mobile_media={blok.highlighted_article.content?.mobile_media}
                title={blok.highlighted_article.content?.title}
                tags={blok.highlighted_article.content?.tags}
                to={blok.highlighted_article.full_slug}
                published_date={highlightedDate}
                inHeader
                media_type={blok.highlighted_article.content?.media_type}
                logo={blok.highlighted_article.content?.logo}
              />
            </div>
          )}
          {articleCards && (
            <div className="lg:col-start-2 lg:col-end-12 col-span-full mt-20 grid grid-cols-6 md:grid-cols-12 gap-7 md:gap-8">
              {articleCards.map((article) => {
                // TBD update when add another locale
                // const link = locale === 'en' ? article.full_slug : article.translated_slugs[0].path;
                let publishedDate;

                if (article.published_at) {
                  publishedDate = new Date(article.published_at);
                }

                const date = publishedDate?.toLocaleDateString('en-GB', {
                  day: '2-digit',
                  month: 'short',
                });

                return (
                  <div className=" col-span-full lg:col-span-4" key={article.uuid}>
                    <ArticleCard
                      time_to_read={article.content?.time_to_read}
                      desktop_media={article.content?.desktop_media}
                      mobile_media={article.content?.mobile_media}
                      title={article.content?.title}
                      tags={article.content?.tags}
                      to={article.full_slug}
                      published_date={date}
                      media_type={article.content?.media_type}
                      logo={article.content?.logo}
                    />
                  </div>
                );
              })}
            </div>
          )}
        </>
      </Container>
    </div>
  );
};

export default BlogOverviewPage;
