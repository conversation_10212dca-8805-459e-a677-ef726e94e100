import { Button, Icon } from '@bramvanuldenn/whatson-components';
import { storyblokEditable } from '@storyblok/react';
import classNames from 'classnames';
import React from 'react';
import Container from '../../components/Container';
import Image from '../../components/Image';
import NavLink from '../../components/NavLink';
import RichText from '../../components/RichText';
import StepImage from '../../components/StepImage';
import { Action } from '../../types/types';
import { useRatioHeight } from '../../utils/useRatioHeight';
import { Props } from './types';

const StepImageText = ({ blok }: Props) => {
  const { ref: assetRef, height: assetHeight } = useRatioHeight('landscape');

  return (
    <div
      {...storyblokEditable(blok)}
      className="mt-[72px] mb-[200px] sm:mb-[400px] md:mb-[500px] lg:my-28"
    >
      <div id={blok.anchor_id} className="block relative -top-44 invisible" />
      <Container className="relative">
        <div
          className={classNames({
            'col-span-full lg:col-start-1 lg:col-end-12 rounded-3xl': true,
            'bg-teal-50': blok.color === 'teal',
            'bg-brand-blue-50': blok.color === 'blue',
            'bg-lime-50': blok.color === 'lime',
            'bg-violet-50': blok.color === 'violet',
          })}
        >
          <div className="mx-8 lg:grid lg:grid-cols-11 lg:gap-x-10 pt-8 lg:py-[74px]">
            <div className="lg:col-start-2 lg:col-end-7">
              <StepImage number={blok.step_number} image={blok.step_image} color={blok.color} />
              <h3 className="mt-7 text-3xl lg:text-4xl text-medium text-neutral-900">
                {blok.title}
              </h3>
              <RichText
                document={blok.description}
                className="mt-7 text-lg lg:text-xl text-neutral-600"
              />
              <div className="mt-12">
                <span className="text-base font-bold text-neutral-700">Relevant solutions</span>
                <div className="mt-4 pb-44 lg:pb-0 lg:flex lg:gap-3 lg:items-center">
                  {blok.action?.map((action: Action) => {
                    const href = action.target?.cached_url;
                    const page = href;
                    const urlLink = action?.target?.anchor
                      ? `/${page}#${action?.target?.anchor}`
                      : `/${page}`;

                    return (
                      <div key={action._uid} className="mb-3 lg:mb-0 last:mb-0">
                        <div className="block">
                          <NavLink to={urlLink}>
                            <Button size={action.size} variant={action.variant} RenderAs="div">
                              <div className="flex items-center gap-1">
                                <span>{action.text}</span>
                                <Icon name="HiArrowRight" size="lg" />
                              </div>
                            </Button>
                          </NavLink>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-span-full lg:col-start-7 lg:col-end-13 absolute -bottom-28 2-sm:-bottom-52 sm:-bottom-80 md:-bottom-96 2-md:-bottom-[430px] left-0 right-0 lg:top-1/2 lg:-translate-y-1/2 lg:transform lg:left-0 lg:right-0 lg:py-[74px]">
          <div ref={assetRef} style={{ height: assetHeight }}>
            <Image src={blok.image?.filename} alt={blok.image?.alt} />
          </div>
        </div>
      </Container>
    </div>
  );
};

export default StepImageText;
