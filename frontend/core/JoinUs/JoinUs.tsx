import { storyblokEditable } from '@storyblok/react';
import { Button } from '@bramvanuldenn/whatson-components';
import { Props } from './types';
import Image from '../../components/Image';
import Container from '../../components/Container';
import RichText from '../../components/RichText';
import NavLink from '../../components/NavLink';
import { useScreenWidth } from '../../utils/useScreenWidth';
import { CONSTANTS } from '../../constants/constants';

const JoinUs = ({ blok }: Props) => {
  const screenWidth = useScreenWidth();
  const media = blok.mobile_image?.filename
    ? screenWidth < CONSTANTS.mdWidth
      ? blok.mobile_image
      : blok.desktop_image
    : blok.desktop_image;

  return (
    <Container {...storyblokEditable(blok)} className="my-[72px] lg:mt-28 lg:mb-56 px-0">
      <div className="lg:col-start-3 lg:col-end-12 col-span-full flex flex-col lg:border border-neutral-200 rounded-xl items-center">
        <div className="lg:left-0 lg:-top-20 lg:absolute md:w-[520px] md:min-h-[500px] min-h-[260px]">
          <Image
            src={media.filename}
            alt={media.alt}
            classname="md:w-[520px] md:min-h-[500px] min-h-[260px] min-w-[400px]"
          />
        </div>
        <div className="grid grid-cols-6 lg:grid-cols-12 lg:gap-x-10  text-center">
          <div className="flex flex-col gap-5 py-9 col-span-full lg:col-start-4 lg:col-end-12 lg:pl-8 xl:pl-0">
            <h2 className="text-3xl text-neutral-900 font-medium">{blok.title}</h2>

            {blok.description && (
              <RichText document={blok.description} className="text-lg text-neutral-500" />
            )}

            <div className="flex flex-col lg:flex-row lg:items-center gap-8 h-full items-center">
              {blok.actions.map((button) => {
                const href = button.target?.cached_url;
                const page = href;
                const urlLink = button?.target?.anchor
                  ? `/${page}#${button?.target?.anchor}`
                  : `/${page}`;

                return (
                  <NavLink to={urlLink} key={button._uid}>
                    <Button RenderAs="div" size={button.size} variant={button.variant}>
                      {button.text}
                    </Button>
                  </NavLink>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </Container>
  );
};

export default JoinUs;
