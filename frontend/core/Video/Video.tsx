import { storyblokEditable } from '@storyblok/react';
import classNames from 'classnames';
import VideoPlayer from '../../components/VideoPlayer/VideoPlayer';
import { Props } from './type';
import { useScreenWidth } from '../../utils/useScreenWidth';
import { useRatioHeight } from '../../utils/useRatioHeight';
import { CONSTANTS } from '../../constants/constants';
import RichText from '../../components/RichText';

const Video = ({ blok }: Props) => {
  const { ref: assetRef, height: assetHeight } = useRatioHeight('landscape');
  const screenWidth = useScreenWidth();
  const VideoSrc =
    screenWidth < CONSTANTS.mdWidth ? blok?.mobile_media?.filename : blok?.desktop_media?.filename;

  return (
    <div
      {...storyblokEditable(blok)}
      className={classNames({
        'my-16 lg:my-24': !blok.isInArticle,
      })}
      id={blok.anchor_id}
    >
      <div
        className={classNames({
          'grid grid-cols-6 lg:grid-cols-12 gap-x-5 lg:gap-x-10 max-w-screen-xl': true,
          'mx-5 lg:mx-auto': !blok.isInArticle,
        })}
      >
        <div
          className={classNames({
            'col-span-full': !blok.isInArticle,
            'lg:col-start-2 lg:col-end-12 col-span-full': blok.isInArticle,
          })}
        >
          {blok.title && <h3 className="text-2xl font-medium text-center mb-4">{blok.title}</h3>}
          <div
            className={classNames({
              'rounded-tl-0 rounded-tr-[20px] rounded-b-[20px] overflow-hidden w-full relative':
                true,
            })}
            ref={assetRef}
            style={{ height: assetHeight }}
          >
            {blok.isYoutubeEmbeded ? (
              <iframe
                width="100%"
                height="100%"
                src={`https://www.youtube.com/embed/${blok.embed_id}`}
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
                title={blok.title}
              />
            ) : (
              <VideoPlayer
                buttonPosition="center"
                videoLink={VideoSrc}
                autoPlay={blok.isAutoPlay}
              />
            )}
          </div>
          <div className="flex items-baseline gap-1 justify-start mt-4">
            <RichText document={blok.caption} className="mt-4 text-xl text-neutral-900" />
            {blok.duration && <span className="text-neutral-500">{blok.duration}</span>}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Video;
