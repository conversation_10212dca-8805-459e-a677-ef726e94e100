import { storyblokEditable } from '@storyblok/react';
import React from 'react';
import Container from '../../components/Container';
import Image from '../../components/Image';
import Numbers from '../../components/Numbers';
import RichText from '../../components/RichText';
import { NumberItem, Props } from './types';

const TextWithNumbers = ({ blok }: Props) => (
  <div {...storyblokEditable(blok)} className="my-[72px] lg:my-28">
    <Container>
      <div className="col-span-full lg:col-start-2 lg:col-end-12">
        <div className="grid grid-cols-6 lg:grid-cols-10 gap-x-5 lg:gap-x-10">
          <div className="col-span-full lg:col-start-1 lg:col-end-7 order-2 lg:order-1">
            <h3 className="text-neutral-900 font-medium text-3xl lg:text-5xl">{blok.title}</h3>
            <RichText
              document={blok.description}
              className="mt-4 text-neutral-500 text-base lg:text-xl"
            />
          </div>
          <div className="col-start-3 col-end-5 lg:col-start-9 lg:col-end-11 order-1 lg:order-2">
            <div>
              <Image src={blok.image?.filename} alt={blok.image?.alt} isContain />
            </div>
          </div>
        </div>
        <div className="mt-11 lg:mt-16 lg:flex lg:justify-between lg:items-center">
          {blok.numbers?.map((item: NumberItem) => (
            <Numbers number={item.number} label={item.label} key={item._uid} />
          ))}
        </div>
      </div>
    </Container>
  </div>
);

export default TextWithNumbers;
