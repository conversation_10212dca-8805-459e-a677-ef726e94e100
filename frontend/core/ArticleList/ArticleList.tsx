import { storyblokEditable } from '@storyblok/react';
import classNames from 'classnames';
import RichText from '../../components/RichText';
import { Props } from './types';

const ArticleText = ({ blok }: Props) => (
  <div
    className={classNames({
      'grid grid-cols-6 lg:grid-cols-12 gap-x-5 lg:gap-x-10 max-w-screen-xl': blok.isInArticle,
    })}
    {...storyblokEditable(blok)}
    id={blok.anchor_id}
  >
    <div
      className={classNames({
        'w-full flex flex-col gap-10': true,
        'lg:col-start-3 lg:col-end-11 col-span-full': blok.isInArticle,
      })}
    >
      {blok.title && <h3 className="text-xl lg:text-3xl text-neutral-900">{blok.title}</h3>}

      {blok.list_items && (
        <div className="divide-y divide-neutral-200 grid grid-cols-1 custom-list">
          {blok.list_items.map((item) => (
            <div
              className="text-neutral-900 text-lg pt-8 first:pt-0 pb-4 custom-list-item"
              key={item._uid}
            >
              <div>
                <span className="font-medium">{item.label}</span>
                <RichText document={item.description} className="text-neutral-900 text-lg" />
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  </div>
);

export default ArticleText;
