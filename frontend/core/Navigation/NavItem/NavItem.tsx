import classNames from 'classnames';
import React from 'react';
import NavLink from '../../../components/NavLink';
import { Props } from './type';

const NavItem = ({ label, path, isActive }: Props) => (
  <div className="cursor-pointer hover:underline hover:underline-offset-8 decoration-neutral-900">
    <NavLink to={path}>
      <div
        className={classNames({
          'font-medium': true,
          'text-brand-pink-500': isActive,
          'text-neutral-900': !isActive,
        })}
      >
        <span>{label}</span>
      </div>
    </NavLink>
  </div>
);

export default NavItem;
