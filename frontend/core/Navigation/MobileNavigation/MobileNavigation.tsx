import React, { useRef, useState, useEffect, useCallback } from 'react';
import { Popover, Transition } from '@headlessui/react';
import { Button, Hyperlink, Icon, Logo } from '@bramvanuldenn/whatson-components';
import classNames from 'classnames';
import { useRouter } from 'next/router';
import { disableBodyScroll, enableBodyScroll } from 'body-scroll-lock';
import { PopupModal } from 'react-calendly';
import NavLink from '../../../components/NavLink/NavLink';
import { NavigationContent, Props } from './type';
import DropdownMenu from '../DropdownMenu/DropdownMenu';

const genericHamburgerLine = `h-[1.5px] w-6 my-1 bg-neutral-900 transition ease transform duration-300`;

const MobileNavigation = ({ contents, actions }: Props) => {
  const [open, setOpen] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [subMenu, setSubMenu] = useState(false);
  const mobileNavigationRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const asPathArray = router.asPath.split('/');
  const urlStartWith = asPathArray[1].split('-').join(' ');
  const buttonRef = useRef<HTMLButtonElement>(null);

  const [textOffset, setTextOffset] = useState(0);
  const [blur, setBlur] = useState(false);

  const handleScroll = useCallback(
    (e: any) => {
      const window = e.currentTarget;
      const pageYOffset = window.pageYOffset;

      if (pageYOffset > 0) {
        setTextOffset(pageYOffset);
        setBlur(true);
      }

      setBlur(false);
    },
    [textOffset]
  );

  useEffect(() => {
    const offset = window.scrollY / window.innerHeight;

    if (offset >= 0) {
      setBlur(true);
    }

    setTextOffset(textOffset);
    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll]);

  useEffect(() => {
    if (open) {
      disableBodyScroll(mobileNavigationRef.current as HTMLElement);
    } else {
      enableBodyScroll(mobileNavigationRef.current as HTMLElement);
    }
  }, [open]);

  // Make sure body scroll is enabled after navigating
  useEffect(() => {
    const handleRouteChange = () => {
      enableBodyScroll(mobileNavigationRef.current as HTMLElement);
    };

    router.events.on('routeChangeStart', handleRouteChange);

    // If the component is unmounted, unsubscribe
    // from the event with the `off` method:
    return () => {
      router.events.off('routeChangeStart', handleRouteChange);
    };
  }, []);

  return (
    <Popover>
      <div
        className={classNames({
          'fixed inset-x-0 top-0 z-50 h-16 w-full ': true,
          'bg-white': open,
          'bg-transparent': !open,
          'bg-white/90': blur && !open,
        })}
        ref={mobileNavigationRef}
      >
        <div className="flex h-full items-center justify-between pl-5">
          <NavLink to="/">
            <div className="w-20 cursor-pointer">
              <Logo />
            </div>
          </NavLink>

          <Popover.Button
            className="flex items-center gap-3 bg-white pr-5"
            onClick={() => setOpen(!open)}
            ref={buttonRef}
          >
            <div className="flex flex-col h-8 w-8 justify-center items-center group">
              <div
                className={`${genericHamburgerLine} ${
                  open ? 'rotate-45 translate-y-[7.5px] opacity-100' : 'opacity-100'
                }`}
              />
              <div className={`${genericHamburgerLine} ${open ? 'opacity-0' : 'opacity-100'}`} />
              <div
                className={`${genericHamburgerLine} ${
                  open ? '-rotate-45 -translate-y-[11.5px] opacity-100' : 'opacity-100'
                }`}
              />
            </div>
          </Popover.Button>
        </div>

        <Transition
          enter="transition duration-100 ease-out"
          enterFrom="transform -translate-y-6 opacity-0"
          enterTo="transform translate-y-0 opacity-100"
          leave="transition duration-75 ease-out"
          leaveFrom="transform translate-y-0 opacity-100"
          leaveTo="transform translate-y-1 opacity-0"
        >
          <Popover.Panel>
            <div className="absolute inset-0 w-full overflow-scroll bg-white h-[calc(100vh_-_64px)] px-5">
              <div className="relative w-full h-full flex justify-between flex-col ">
                <div className="flex gap-1 flex-col">
                  {contents &&
                    contents.map((content: NavigationContent) => (
                      <div
                        key={content._uid}
                        className={classNames({
                          'py-4 text-lg font-medium': true,
                          'text-brand-pink-500': content.label.toLowerCase() === urlStartWith,
                          'text-neutral-900': content.label.toLowerCase() !== urlStartWith,
                        })}
                      >
                        {content.is_dropdown && content.dropdown_item ? (
                          <div>
                            <button
                              className="flex gap-1 items-center"
                              onClick={() => setSubMenu(!subMenu)}
                            >
                              <span>{content.label}</span>
                              <Icon
                                size="sm"
                                name="HiChevronDown"
                                className={classNames({
                                  'transform text-neutral-400 transition duration-300 mt-1': true,
                                  'rotate-180': subMenu,
                                  'rotate-0': !subMenu,
                                })}
                              />
                            </button>
                            {subMenu && (
                              <div className="py-3">
                                {content.dropdown_item.map((item) => (
                                  <div onClick={() => buttonRef.current?.click()}>
                                    <DropdownMenu menu={item} key={item._uid} />
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        ) : (
                          <NavLink to={content.link?.full_slug}>
                            <span onClick={() => buttonRef.current?.click()}>{content.label}</span>
                          </NavLink>
                        )}
                      </div>
                    ))}
                </div>
                <div className="flex flex-col gap-3 py-6">
                  {actions &&
                    actions.map((action) => {
                      const href = action.target?.cached_url;
                      const page = href;
                      const slash = page.indexOf('http') === 0 ? '' : '/';
                      const urlLink = action?.target?.anchor
                        ? `${slash}${page}#${action?.target?.anchor}`
                        : `${slash}${page}`;

                      if (action.isDemo) {
                        return (
                          <div
                            key={action._uid}
                            className={classNames({
                              'w-full': true,
                              'order-1': action.component === 'button',
                              'order-2': action.component !== 'button',
                            })}
                          >
                            {action.component === 'button' ? (
                              <Button
                                size={action.size}
                                variant={action.variant}
                                RenderAs="button"
                                expand
                                onClick={() => {
                                  setModalOpen(true);
                                }}
                              >
                                {action.text}
                              </Button>
                            ) : (
                              <Hyperlink
                                className="px-5 text-sm font-bold"
                                size={action.size}
                                RenderAs="button"
                                onClick={() => {
                                  setModalOpen(true);
                                }}
                              >
                                {action.text}
                              </Hyperlink>
                            )}
                            {typeof window !== 'undefined' && (
                              <PopupModal
                                url={action.target.url}
                                onModalClose={() => {
                                  setModalOpen(false);
                                }}
                                open={modalOpen}
                                rootElement={document.getElementById('__next') as HTMLElement}
                              />
                            )}
                          </div>
                        );
                      }

                      return (
                        <NavLink
                          to={urlLink}
                          key={action._uid}
                          linkClassName={classNames({
                            'w-full': true,
                            'order-1': action.component === 'button',
                            'order-2': action.component !== 'button',
                          })}
                        >
                          {action.component === 'button' ? (
                            <Button
                              size={action.size}
                              variant={action.variant}
                              RenderAs="div"
                              expand
                            >
                              {action.text}
                            </Button>
                          ) : (
                            <Hyperlink
                              className="px-5 text-base font-bold w-full text-center py-3 justify-center items-center"
                              size={action.size}
                              RenderAs="div"
                            >
                              {action.text}
                            </Hyperlink>
                          )}
                        </NavLink>
                      );
                    })}
                </div>
              </div>
            </div>
          </Popover.Panel>
        </Transition>
      </div>
    </Popover>
  );
};

export default MobileNavigation;
