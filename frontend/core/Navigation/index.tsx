import React from 'react';
import { useStoryblok } from '@storyblok/react';
import DesktopNavigation from './DesktopNavigation';
import MobileNavigation from './MobileNavigation';
import Container from '../../components/Container';

const resolveRelations = 'navigation_item.link,navigation_dropdown_item.link';

const Navigation = () => {
  const story = useStoryblok('global/navigation', {
    version: 'draft',
    resolve_relations: resolveRelations,
  });
  const navigationContents = story?.content?.nav_items;
  const navigationActions = story?.content?.actions;

  return (
    <>
      <Container>
        <div className="hidden lg:block col-span-full">
          <DesktopNavigation contents={navigationContents} actions={navigationActions} />
        </div>
      </Container>

      <div className="block lg:hidden w-full">
        <MobileNavigation contents={navigationContents} actions={navigationActions} />
      </div>
    </>
  );
};

export default Navigation;
