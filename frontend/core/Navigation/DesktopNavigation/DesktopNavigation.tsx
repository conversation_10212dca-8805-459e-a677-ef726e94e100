import React, { useCallback, useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { Logo, Button, Hyperlink } from '@bramvanuldenn/whatson-components';
import { PopupModal } from 'react-calendly';
import classNames from 'classnames';
import { NavigationContent, Props } from './type';
import NavLink from '../../../components/NavLink';
import NavItem from '../NavItem';
import NavItemWithDropdown from '../NavItemWithDropdown';

const DesktopNavigation = ({ contents, actions }: Props) => {
  const { asPath } = useRouter();
  const asPathArray = asPath.split('/');
  const urlStartWith = asPathArray[1].split('-').join(' ');
  const [open, setOpen] = useState(false);
  const [textOffset, setTextOffset] = useState(0);
  const [blur, setBlur] = useState(false);

  const handleScroll = useCallback(
    (e: any) => {
      const window = e.currentTarget;
      const pageYOffset = window.pageYOffset;

      if (pageYOffset > 0) {
        setTextOffset(pageYOffset);
        setBlur(true);
      }

      setBlur(false);
    },
    [textOffset]
  );

  useEffect(() => {
    const offset = window.scrollY / window.innerHeight;

    if (offset >= 0) {
      setBlur(true);
    }

    setTextOffset(textOffset);
    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll]);

  return (
    <div
      className={classNames({
        'py-5 bg-transparent fixed w-full left-1/2 -translate-x-1/2 z-[1000] top-0 lg:px-4 xl:px-0':
          true,
        'bg-white/90': blur,
      })}
    >
      <div className="max-w-screen-xl mx-auto w-full flex items-center justify-between">
        <NavLink to="/">
          <div className="w-40 cursor-pointer">
            <Logo />
          </div>
        </NavLink>
        <div className="flex gap-7 px-6 py-3 border-neutral-100 border rounded-full bg-white">
          {contents &&
            contents.map((content: NavigationContent) => (
              <div key={content._uid}>
                {content.is_dropdown ? (
                  <NavItemWithDropdown
                    label={content.label}
                    panelLabel={content.label}
                    isActive={content.label.toLowerCase() === urlStartWith}
                    dropdownMenu={content.dropdown_item}
                  />
                ) : (
                  <NavItem
                    label={content.label}
                    path={content.link?.full_slug}
                    isActive={content.label.toLowerCase() === urlStartWith}
                  />
                )}
              </div>
            ))}
        </div>
        <div className="flex items-center">
          {actions &&
            actions.map((action) => {
              const href = action.target?.cached_url;
              const page = href;
              const slash = page.indexOf('http') === 0 ? '' : '/';
              const urlLink = action?.target?.anchor
                ? `${slash}${page}#${action?.target?.anchor}`
                : `${slash}${page}`;

              if (action.isDemo) {
                return (
                  <div key={action._uid}>
                    {action.component === 'button' ? (
                      <Button
                        size={action.size}
                        variant={action.variant}
                        RenderAs="button"
                        onClick={() => {
                          setOpen(true);
                        }}
                      >
                        {action.text}
                      </Button>
                    ) : (
                      <Hyperlink
                        className="px-5 text-sm font-bold"
                        size={action.size}
                        RenderAs="button"
                        onClick={() => {
                          setOpen(true);
                        }}
                      >
                        {action.text}
                      </Hyperlink>
                    )}
                    {typeof window !== 'undefined' && (
                      <PopupModal
                        url={action.target.url}
                        onModalClose={() => {
                          setOpen(false);
                        }}
                        open={open}
                        rootElement={document.getElementById('__next') as HTMLElement}
                      />
                    )}
                  </div>
                );
              }

              return (
                <NavLink to={urlLink} key={action._uid}>
                  {action.component === 'button' ? (
                    <Button size={action.size} variant={action.variant} RenderAs="div">
                      {action.text}
                    </Button>
                  ) : (
                    <Hyperlink className="px-5 text-sm font-bold" size={action.size} RenderAs="div">
                      {action.text}
                    </Hyperlink>
                  )}
                </NavLink>
              );
            })}
        </div>
      </div>
    </div>
  );
};

export default DesktopNavigation;
