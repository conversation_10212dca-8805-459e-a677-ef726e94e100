import { Popover } from '@headlessui/react';
import classNames from 'classnames';
import React, { useState } from 'react';
import { Icon } from '@bramvanuldenn/whatson-components';
import DropdownMenu from '../DropdownMenu';
import { Props } from './type';

const SectionWithDropdown = ({ label, dropdownMenu, panelLabel, isActive }: Props) => {
  const [hover, setHover] = useState(false);

  return (
    <div
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
      className="relative"
    >
      <Popover>
        {({ open }) => (
          <>
            <Popover.Button
              className={classNames({
                'flex items-center gap-1 font-medium focus:outline-none  cursor-pointer': true,
                'text-neutral-900': !open || !hover,
                'text-pink-500': isActive,
                'hover:underline-offset-8 hover:underline hover:decoration-neutral-900': hover,
              })}
            >
              <span>{label}</span>
              <Icon size="sm" name="HiChevronDown" />
            </Popover.Button>

            {open && (
              <div className="absolute top-10 -left-5 w-[680px] border border-neutral-100 shadow-2xl rounded-2xl z-[1000] bg-white">
                <Popover.Panel static className="relative p-4">
                  {({ close }) => (
                    <div>
                      <span className="text-neutral-400 text-xs uppercase font-bold ml-4">
                        {panelLabel}
                      </span>
                      {dropdownMenu && (
                        <div className="mt-3">
                          {dropdownMenu.map((menu: any) => (
                            <div
                              onClick={() => {
                                close();
                              }}
                            >
                              <DropdownMenu menu={menu} key={menu._uid} />
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </Popover.Panel>
              </div>
            )}
          </>
        )}
      </Popover>
    </div>
  );
};

export default SectionWithDropdown;
