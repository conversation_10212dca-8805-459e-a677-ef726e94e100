import classNames from 'classnames';
import React, { useState } from 'react';
import { Icon } from '@bramvanuldenn/whatson-components';
import NavLink from '../../../components/NavLink';
import { Props } from './type';

const DropdownMenu = ({ menu }: Props) => {
  const [hover, setHover] = useState(false);
  const fullSlug = menu?.link?.full_slug;

  return (
    <div
      className={classNames({
        'cursor-pointer w-full mb-3 last:mb-0': true,
      })}
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
    >
      <div
        className={classNames({
          'lg:pr-3 lg:py-2.5 rounded-md': true,
          'bg-white': !hover,
          'bg-lime-100': hover && menu?.color === 'green',
          'bg-violet-100': hover && menu?.color === 'purple',
          'bg-blue-100': hover && menu?.color === 'blue',
          'bg-teal-100': hover && menu?.color === 'teal',
        })}
      >
        <NavLink to={fullSlug}>
          <div className="flex items-center w-full justify-between">
            <div className="flex items-center gap-2">
              <div
                className={classNames({
                  'rounded-md lg:px-5 px-3 py-4': true,
                })}
              >
                <Icon
                  size="xl"
                  name={menu?.iconName}
                  className={classNames({
                    'text-neutral-400': !hover,
                    'text-lime-600': hover && menu?.color === 'green',
                    'text-violet-600': hover && menu?.color === 'purple',
                    'text-blue-600': hover && menu?.color === 'blue',
                    'text-teal-600': hover && menu?.color === 'teal',
                  })}
                />
              </div>
              <div className="flex flex-col gap-1">
                <span
                  className={classNames({
                    'text-lg font-medium': true,
                    'text-neutral-800': !hover,
                    'text-lime-800': hover && menu?.color === 'green',
                    'text-violet-800': hover && menu?.color === 'purple',
                    'text-blue-800': hover && menu?.color === 'blue',
                    'text-teal-800': hover && menu?.color === 'teal',
                  })}
                >
                  {menu?.label}
                </span>
                <span
                  className={classNames({
                    'text-sm hidden lg:block': true,
                    'text-neutral-500': !hover,
                    'text-lime-600': hover && menu?.color === 'green',
                    'text-violet-600': hover && menu?.color === 'purple',
                    'text-blue-600': hover && menu?.color === 'blue',
                    'text-teal-600': hover && menu?.color === 'teal',
                  })}
                >
                  {menu?.subText}
                </span>
              </div>
            </div>
            {hover && (
              <Icon
                size="xl"
                name="HiChevronLeft"
                className={classNames({
                  'rotate-180': true,
                  'text-lime-500': hover && menu?.color === 'green',
                  'text-violet-500': hover && menu?.color === 'purple',
                  'text-blue-500': hover && menu?.color === 'blue',
                  'text-teal-500': hover && menu?.color === 'teal',
                })}
              />
            )}
          </div>
        </NavLink>
      </div>
    </div>
  );
};

export default DropdownMenu;
