import { Badge, Link, Media } from '../../types/types';
import { NumberItem } from '../TextWithNumbers/types';

type CasePageBlok = {
  title: string;
  component: string;
  tags: Badge[];
  body: any;
  logo: Media;
  more_cases: Link[];
  author: string;
  desktop_media: Media;
  mobile_media?: Media;
  media_type: 'video' | 'image';
  position: string;
  quote: any;
  _uid: string;
  anchor_id: string;
  numbers?: NumberItem[];
  isInHeader?: boolean;
};

export type Props = {
  blok: CasePageBlok;
};
