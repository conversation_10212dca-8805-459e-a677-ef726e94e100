import { storyblokEditable, StoryblokComponent } from '@storyblok/react';
import { Button, Icon, Badge } from '@bramvanuldenn/whatson-components';
import { useTranslation } from 'next-i18next';
import { Props } from './type';
import NavLink from '../../components/NavLink';
import ArticleTeaser from '../ArticleTeaser';
import Container from '../../components/Container/Container';
import Image from '../../components/Image';
import QuoteSliderItem from '../../components/QuoteSliderItem';

const CasePage = ({ blok }: Props) => {
  const { t } = useTranslation('common');

  return (
    <>
      {blok && (
        <Container className="mt-24 lg:mt-32" {...storyblokEditable(blok)}>
          <div className="lg:col-start-3 lg:col-end-11 mb-16 flex flex-col gap-7 col-span-full">
            <NavLink to="/cases">
              <Button variant="secondary" size="sm" RenderAs="div">
                <div className="flex items-center gap-1">
                  <Icon name="HiArrowLeft" size="xs" />
                  {t('back-case')}
                </div>
              </Button>
            </NavLink>
            <div className="flex gap-3">
              {blok.logo.filename && (
                <div className="min-w-[100px] ">
                  <Image src={blok.logo.filename} alt={blok.logo.alt} classname="!min-h-[32px]" />
                </div>
              )}

              {blok.tags && (
                <div className="flex gap-2">
                  {blok.tags.map((tag) => (
                    <Badge variant={tag.variant} size={tag.size} iconName={tag.icon} key={tag._uid}>
                      {tag.text}
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            <h1 className="text-2xl font-medium lg:text-4xl">{blok.title}</h1>
          </div>
          <div className="lg:col-start-2 lg:col-end-12 col-span-full">
            <QuoteSliderItem
              author={blok.author}
              quote={blok.quote}
              mobile_media={blok.mobile_media}
              desktop_media={blok.desktop_media}
              media_type={blok.media_type}
              position={blok.position}
              numbers={blok.numbers}
            />
          </div>

          <div className="mt-20 lg:col-start-2 lg:col-end-12 col-span-full flex-col lg:gap-14 gap-12 flex">
            {blok.body?.map((nestedBlok: any) => (
              <StoryblokComponent blok={nestedBlok} key={nestedBlok._uid} />
            ))}
          </div>
          <div className="lg:col-start-2 lg:col-end-12 col-span-full">
            {blok.more_cases?.map((nestedBlok: any) => (
              <ArticleTeaser blok={nestedBlok} key={nestedBlok._uid} isInCase />
            ))}
          </div>
        </Container>
      )}
    </>
  );
};

export default CasePage;
