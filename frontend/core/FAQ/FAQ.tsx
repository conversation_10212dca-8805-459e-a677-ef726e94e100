import { storyblokEditable } from '@storyblok/react';
import { Props } from './types';
import Container from '../../components/Container';
import Questions from '../../components/Questions';

const FAQ = ({ blok }: Props) => (
  <Container {...storyblokEditable(blok)}>
    <div className="lg:col-start-4 lg:col-end-9 col-span-full my-[72px] lg:my-28" id="faq">
      <div className=" w-full mb-9">
        <h2 className="lg:text-4xl text-3xl font-medium text-neutral-900">{blok.title}</h2>
      </div>
      <div className="overflow-hidden w-full">
        {blok.questions &&
          blok.questions.map((question) => (
            <Questions
              label={question.label}
              key={question._uid}
              description={question.description}
            />
          ))}
      </div>
    </div>
  </Container>
);

export default FAQ;
