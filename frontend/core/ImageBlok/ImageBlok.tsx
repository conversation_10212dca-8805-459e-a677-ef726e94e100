import { storyblokEditable } from '@storyblok/react';
import React from 'react';
import Container from '../../components/Container';
import Image from '../../components/Image';
import { CONSTANTS } from '../../constants/constants';
import { useRatioHeight } from '../../utils/useRatioHeight';
import { useScreenWidth } from '../../utils/useScreenWidth';
import { Props } from './types';

const ImageBlok = ({ blok }: Props) => {
  const { ref: assetRef, height: assetHeight } = useRatioHeight('landscape');

  const screenWidth = useScreenWidth();
  const media = blok.mobile_image?.filename
    ? screenWidth < CONSTANTS.mdWidth
      ? blok.mobile_image
      : blok.desktop_image
    : blok.desktop_image;

  return (
    <Container {...storyblokEditable(blok)} className="my-[72px] lg:my-28">
      <div
        ref={assetRef}
        className="rounded-[20px] overflow-hidden lg:col-start-2 lg:col-end-12 col-span-full"
        style={{ height: assetHeight }}
      >
        <Image src={media.filename} alt={media.alt} />
      </div>
    </Container>
  );
};

export default ImageBlok;
