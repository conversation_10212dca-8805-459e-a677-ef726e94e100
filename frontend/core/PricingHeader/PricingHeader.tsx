import { storyblokEditable } from '@storyblok/react';
import React from 'react';
import Container from '../../components/Container';
import PricingCard from '../../components/PricingCard';
import RichText from '../../components/RichText';
import { Props } from './types';

const PricingHeader = ({ blok }: Props) => (
  <div {...storyblokEditable(blok)} className="lg:pt-32 mb-96 pt-24 pb-64 bg-teal-50">
    <Container className="relative">
      <div className="col-span-full lg:col-start-4 lg:col-end-10 text-center">
        <div className="flex justify-center w-full lg:block">
          <span className="block bg-teal-500 text-white py-1 px-3 rounded lg:rounded-none lg:py-0 lg:px-0 lg:w-full lg:bg-transparent text-xs lg:text-neutral-500 font-bold">
            {blok.tag.toUpperCase()}
          </span>
        </div>
        <h1 className="mt-6 text-4xl lg:text-5xl font-medium text-neutral-900">{blok.title}</h1>
        <RichText document={blok.description} className="mt-6 text-base text-neutral-900" />
      </div>
      {blok.pricing_cards && (
        <div className="col-span-full flex justify-center items-center w-full mt-16 -mb-[500px]">
          <div className="lg:flex lg:items-baseline lg:gap-3 lg:justify-between">
            {blok.pricing_cards?.map((card) => (
              <PricingCard
                key={card._uid}
                isMostPopular={card.isMostPopular}
                title={card.title}
                description={card.description}
                price={card.price}
                price_label={card.price_label}
                price_description={card.price_description}
                usps={card.usps}
                action={card.action}
                number_of_items={blok.pricing_cards?.length}
              />
            ))}
          </div>
        </div>
      )}
    </Container>
  </div>
);

export default PricingHeader;
