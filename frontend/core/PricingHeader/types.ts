import { PricingUsp } from '../../components/PricingCard/types';
import { Action } from '../../types/types';

type PricingCard = {
  _uid: string;
  isMostPopular: boolean;
  title: string;
  description: any;
  price: string;
  price_label: string;
  price_description: string;
  usps: PricingUsp[];
  action: Action[];
};

type PricingHeaderBlok = {
  title: string;
  description: any;
  tag: string;
  pricing_cards: PricingCard[];
};

export type Props = {
  blok: PricingHeaderBlok;
};
