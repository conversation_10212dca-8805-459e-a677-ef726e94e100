import React, { useState } from 'react';
import { useStoryblok } from '@storyblok/react';
import { But<PERSON> } from '@bramvanuldenn/whatson-components';
import { PopupModal } from 'react-calendly';
import Container from '../../components/Container';
import { ReactComponent as LinkedIn } from '../../public/svg/linkedin.svg';
import { ReactComponent as Instagram } from '../../public/svg/instagram.svg';
import { ReactComponent as Twitter } from '../../public/svg/twitter.svg';
import { ReactComponent as Facebook } from '../../public/svg/facebook.svg';
import { FooterItem, FooterItems } from './types';
import NavLink from '../../components/NavLink';
import type { Action } from '../../types/types';

const Footer = () => {
  const story = useStoryblok('global/footer', {
    version: 'draft',
  });

  const footerContents = story?.content;
  const year = new Date().getFullYear();
  const [open, setOpen] = useState(false);

  return (
    <div className="bg-neutral-800 rounded-tl-[28px] text-white z-50 relative">
      <Container>
        <div className="mt-9 mb-6 col-span-full lg:mt-14 lg:mb-[98px]">
          <div className="lg:flex lg:justify-between lg:items-center">
            <div>
              <h4 className="text-3xl lg:text-5xl font-medium">{footerContents?.title}</h4>
              <span className="mt-2 text-neutral-400 text-base lg:text-lg">
                {footerContents?.subtitle}
              </span>
            </div>
            <div className="mt-6 lg:hidden">
              {footerContents?.actions?.map((action: Action) => {
                const href = action.target?.cached_url;
                const page = href;
                const urlLink = action?.target?.anchor
                  ? `/${page}#${action?.target?.anchor}`
                  : `/${page}`;

                if (action.isDemo) {
                  return (
                    <div key={action._uid} className="mb-6">
                      <Button
                        size={action.size}
                        variant={action.variant}
                        RenderAs="button"
                        onClick={() => {
                          setOpen(true);
                        }}
                        expand
                      >
                        {action.text}
                      </Button>

                      {typeof window !== 'undefined' && (
                        <PopupModal
                          url={action.target.url}
                          onModalClose={() => {
                            setOpen(false);
                          }}
                          open={open}
                          rootElement={document.getElementById('__next') as HTMLElement}
                        />
                      )}
                    </div>
                  );
                }

                return (
                  <NavLink to={urlLink} key={action._uid} linkClassName="mb-6">
                    <Button size={action.size} variant={action.variant} RenderAs="div" expand>
                      {action.text}
                    </Button>
                  </NavLink>
                );
              })}
            </div>
            <div className="hidden lg:mt-0 lg:flex lg:items-center lg:gap-6">
              {footerContents?.actions?.map((action: Action) => {
                const href = action.target?.cached_url;
                const page = href;
                const urlLink = action?.target?.anchor
                  ? `/${page}#${action?.target?.anchor}`
                  : `/${page}`;

                if (action.isDemo) {
                  return (
                    <div key={action._uid}>
                      <Button
                        size={action.size}
                        variant={action.variant}
                        RenderAs="button"
                        onClick={() => {
                          setOpen(true);
                        }}
                      >
                        {action.text}
                      </Button>

                      {typeof window !== 'undefined' && (
                        <PopupModal
                          url={action.target.url}
                          onModalClose={() => {
                            setOpen(false);
                          }}
                          open={open}
                          rootElement={document.getElementById('__next') as HTMLElement}
                        />
                      )}
                    </div>
                  );
                }

                return (
                  <NavLink to={urlLink} key={action._uid}>
                    <Button size={action.size} variant={action.variant} RenderAs="div">
                      {action.text}
                    </Button>
                  </NavLink>
                );
              })}
            </div>
          </div>
        </div>
        {footerContents?.footer_items?.map((item: FooterItems) => (
          <div className="col-span-full mt-7 lg:mt-0 lg:col-span-4 lg:mb-12" key={item._uid}>
            <span className="block mb-3 text-lg lg:text-base font-bold">{item.label}</span>
            {item.footer_items?.map((footer_item: FooterItem) => (
              <NavLink to={footer_item.link.cached_url} key={footer_item._uid}>
                <div className="mb-3 cursor-pointer text-base font-normal">{footer_item.label}</div>
              </NavLink>
            ))}
          </div>
        ))}
        <div className="col-span-full flex flex-col mt-12 mb-7 lg:mt-0 lg:flex-row lg:justify-between lg:items-center lg:mb-10">
          <span className="block text-neutral-400 mt-5 order-2 lg:mt-0 lg:order-1">
            © {year} Whatson
          </span>
          <div className="flex items-center gap-3 order-1 lg:order-2">
            <a href="https://www.linkedin.com/company/whatsonbv/" className="p-3">
              <LinkedIn />
            </a>
            <a href="https://www.instagram.com/whatsonbv/" className="p-3">
              <Instagram />
            </a>
            <a href=" https://twitter.com/WhatsonBV" className="p-3 text-white ">
              <div className="w-[18px] h-[18px]">
                <Twitter />
              </div>
            </a>
            <a
              href="https://www.facebook.com/profile.php?id=100091305266849"
              className="p-3 text-white "
            >
              <div className="w-[18px] h-[18px]">
                <Facebook />
              </div>
            </a>
          </div>
          <div className="flex items-center gap-5 text-neutral-400 order-3 mt-5 lg:mt-0">
            <a href="/terms-conditions">
              <span>Terms & Conditions</span>
            </a>
            {/* <a href="/">
              <span>Privacy Policy</span>
            </a> */}
          </div>
        </div>
      </Container>
    </div>
  );
};

export default Footer;
