import { Icon } from '@bramvanuldenn/whatson-components';
import { storyblokEditable } from '@storyblok/react';
import classNames from 'classnames';
import React from 'react';
import Container from '../../components/Container';
import RichText from '../../components/RichText';
import { Usp } from '../Usps/types';
import { Props } from './types';

const HomeSteps = ({ blok }: Props) => (
  <div {...storyblokEditable(blok)} className="py-[72px] lg:py-28 bg-neutral-50">
    <div id="how-it-works" className="block relative -top-44 invisible" />
    <Container>
      <div className="col-span-full lg:col-start-1 lg:col-end-8">
        <h2 className="text-3xl lg:text-5xl font-medium">{blok.title}</h2>
      </div>
      <div className="col-span-full lg:col-start-8 lg:col-end-13 mt-4 mb-0.5 lg:mb-0 lg:mt-0">
        <RichText document={blok.description} className="text-lg text-neutral-500" />
      </div>
      <>
        {blok.usps?.map((usp: Usp) => (
          <div className="col-span-full lg:col-span-3 mt-10 lg:mt-28" key={usp._uid}>
            <div className="flex gap-3 items-center">
              <div
                className={classNames({
                  'text-white w-fit p-3 rounded-tl-lg rounded-br-lg': true,
                  'bg-teal-500': usp.icon_color === 'teal',
                  'bg-lime-500': usp.icon_color === 'lime',
                  'bg-brand-blue-600': usp.icon_color === 'blue',
                  'bg-violet-500': usp.icon_color === 'violet',
                  'bg-brand-orange-500': usp.icon_color === 'orange',
                })}
              >
                <Icon name={usp.icon} size="2xl" />
              </div>
              <div className="h-0.5 w-full bg-neutral-200"></div>
            </div>
            <h6 className="text-xl font-bold text-neutral-900 mt-5">{usp.title}</h6>
            <RichText document={usp.description} className="text-base text-neutral-600 mt-2" />
          </div>
        ))}
      </>
    </Container>
  </div>
);

export default HomeSteps;
