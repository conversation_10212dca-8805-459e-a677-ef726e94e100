import { Media } from '../../types/types';
import { NumberItem } from '../TextWithNumbers/types';

type BoxButtonItem = {
  title: string;
  tag: string;
  link: any;
  tag_color: 'blue' | 'teal' | 'violet' | 'lime';
  _uid: string;
};

type SubpageHeaderBlok = {
  tag: string;
  title: string;
  description: any;
  desktop_image: Media;
  mobile_image: Media;
  actions: BoxButtonItem[];
  numbers: NumberItem[];
};

export type Props = {
  blok: SubpageHeaderBlok;
};
