import { storyblokEditable } from '@storyblok/react';
import classNames from 'classnames';
import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { FreeMode } from 'swiper';
// Import Swiper styles
import 'swiper/css';
import BoxButton from '../../components/BoxButton';
import Container from '../../components/Container';
import Image from '../../components/Image';
import Numbers from '../../components/Numbers';
import RichText from '../../components/RichText';
import { useRatioHeight } from '../../utils/useRatioHeight';
import { Props } from './types';
import { CONSTANTS } from '../../constants/constants';
import { useScreenWidth } from '../../utils/useScreenWidth';

const SubpageHeader = ({ blok }: Props) => {
  const { ref: assetRef, height: assetHeight } = useRatioHeight('square');
  const screenWidth = useScreenWidth();
  const media = blok.mobile_image?.filename
    ? screenWidth < CONSTANTS.mdWidth
      ? blok.mobile_image
      : blok.desktop_image
    : blok.desktop_image;

  return (
    <div
      {...storyblokEditable(blok)}
      className={classNames({
        'my-[72px] md:mt-28': true,
        'md:mb-28': !blok.actions,
        'md:mb-72 xl:mb-56': blok.actions,
      })}
    >
      <Container className="relative">
        <div className="col-span-full lg:col-start-2 lg:col-end-8 relative order-2 lg:order-1">
          <span className="block mt-9 text-center lg:text-left lg:mt-0 text-xs text-neutral-500 font-bold">
            {blok.tag.toUpperCase()}
          </span>
          <h1 className="mt-4 lg:mt-7 text-4xl text-center lg:text-left lg:text-6xl text-neutral-900 font-medium">
            {blok.title}
          </h1>
          <RichText
            document={blok.description}
            className="mt-5 text-lg text-center lg:text-left lg:text-xl text-neutral-500"
          />
          {blok.numbers && (
            <div className="mt-9 lg:flex lg:gap-10 lg:mt-12">
              {blok.numbers.map((item) => (
                <Numbers number={item.number} label={item.label} key={item._uid} />
              ))}
            </div>
          )}
        </div>
        <div className="col-span-full lg:col-start-8 lg:col-end-13 order-1 lg:order-2">
          <div ref={assetRef} style={{ height: assetHeight }}>
            <Image src={media?.filename} alt={media?.alt} />
          </div>
        </div>
        {blok.actions && (
          <div className="hidden md:block lg:col-start-2 lg:col-end-9 absolute left-0 col-span-full md:-bottom-44 xl:-bottom-28 w-full">
            <div className="flex md:gap-7 items-stretch justify-between">
              {blok.actions?.map((action) => (
                <BoxButton
                  title={action.title}
                  tag={action.tag.toUpperCase()}
                  link={`${action.link.cached_url}#${action.link.anchor}`}
                  color={action.tag_color}
                  key={action._uid}
                />
              ))}
            </div>
          </div>
        )}
      </Container>
      {blok.actions && (
        <div className="col-span-full order-3 mt-11 md:hidden">
          <Swiper
            grabCursor={true}
            slidesPerView={'auto'}
            slidesPerGroup={blok.actions.length - 1}
            centeredSlides={true}
            freeMode={true}
            modules={[FreeMode]}
            className="swiper-container-slider-subpage-header"
          >
            {blok.actions?.map((action) => (
              <SwiperSlide className="mr-7" key={action._uid}>
                <BoxButton
                  title={action.title}
                  tag={action.tag.toUpperCase()}
                  link={`${action.link.cached_url}#${action.link.anchor}`}
                  color={action.tag_color}
                />
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      )}
    </div>
  );
};

export default SubpageHeader;
