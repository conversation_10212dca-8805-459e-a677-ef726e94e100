import { storyblokEditable } from '@storyblok/react';
import classNames from 'classnames';
import Image from '../../components/Image';
import RichText from '../../components/RichText';
import { Props } from './types';

const SimpleQuote = ({ blok }: Props) => (
  <div
    {...storyblokEditable(blok)}
    className={classNames({
      'my-16 lg:my-24': !blok.isInArticle,
    })}
    id={blok.anchor_id}
  >
    <div
      className={classNames({
        'grid grid-cols-6 lg:grid-cols-12 gap-x-5 lg:gap-x-10 max-w-screen-xl': true,
        'mx-5 lg:mx-auto': !blok.isInArticle,
      })}
    >
      <div
        className={classNames({
          'col-span-full pl-3 lg:pl-5 border border-l-4 border-brand-orange-500 border-t-0 border-r-0 border-b-0':
            true,
          'lg:col-start-4 lg:col-end-10': !blok.isInArticle,
          'lg:col-start-3 lg:col-end-11': blok.isInArticle,
        })}
      >
        <RichText document={blok.quote} className="lg:text-2xl text-neutral-900" />
        <div className="flex items-center lg:mt-4">
          <div>
            {blok.author_image && blok.author_image?.filename && (
              <Image
                src={blok.author_image?.filename}
                alt={blok.author_image?.alt}
                classname="w-11 h-[42px] lg:w-[78px] lg:h-[75px] !min-h-[40px] mr-3"
              />
            )}
          </div>
          <div>
            <span className="block text-base font-neutral-900 font-medium">{blok.author}</span>
            <span className="block text-sm font-normal text-neutral-500">
              {blok.author_position}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
);

export default SimpleQuote;
