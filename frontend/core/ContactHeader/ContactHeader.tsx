import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@bramvanuldenn/whatson-components';
import { storyblokEditable } from '@storyblok/react';
import React, { useState } from 'react';
import { PopupModal } from 'react-calendly';
import Script from 'next/script';
import { ReactComponent as LinkedIn } from '../../public/svg/linkedin-black.svg';
import { ReactComponent as Facebook } from '../../public/svg/facebook.svg';
import Container from '../../components/Container';
import RichText from '../../components/RichText';
import { Props } from './types';
import Image from '../../components/Image';
import { Action } from '../../types/types';
import ContactForm from '../../components/ContactForm';

const ContactHeader = ({ blok }: Props) => {
  const [open, setOpen] = useState(false);

  return (
    <div {...storyblokEditable(blok)} className="my-[72px] lg:my-28">
      <Container>
        <div className="col-span-full lg:col-start-2 lg:col-end-7">
          <span className="block text-xs text-neutral-500 font-bold">{blok.tag.toUpperCase()}</span>
          <div className="grid grid-cols-4">
            <div className="col-start-1 col-end-4">
              <h1 className="mt-4 lg:mt-7 text-4xl lg:text-6xl text-neutral-900 font-medium">
                {blok.title}
              </h1>
            </div>
            <div className="col-span-full col-start-4 col-end-5">
              <div className="w-20 h-20 lg:w-40 lg:h-40">
                <Image src={blok.image?.filename} alt={blok.image?.alt} classname="!min-h-[40px]" />
              </div>
            </div>
          </div>
          <RichText
            document={blok.description}
            className="mt-5 text-neutral-500 text-base lg:text-xl"
          />
          <ContactForm />
          <div className="mt-5 flex items-center gap-2">
            <div className="p-3 text-neutral-700">
              <div className="w-[18px] h-[18px]">
                <a href="https://www.linkedin.com/company/whatsonbv/">
                  <LinkedIn />
                </a>
              </div>
            </div>
            <div className="p-3 text-neutral-700">
              <div className="w-[18px] h-[18px]">
                <a href="https://www.facebook.com/profile.php?id=100091305266849">
                  <Facebook />
                </a>
              </div>
            </div>
          </div>
          <Divider className="my-10" />
          <div className="flex items-start gap-3">
            <div className="text-neutral-400">
              <Icon name="HiMapPin" size="base" />
            </div>
            <div>
              <span className="block text-xs font-bold text-neutral-600">
                {blok.address_label.toUpperCase()}
              </span>
              <RichText document={blok.address} className="block mt-3 text-neutral-700" />
            </div>
          </div>
          <div className="flex items-start gap-3 mt-7">
            <div className="text-neutral-400">
              <Icon name="HiPhone" size="base" />
            </div>
            <div>
              <span className="block text-xs font-bold text-neutral-600">
                {blok.contact_label.toUpperCase()}
              </span>
              <RichText document={blok.contact} className="block mt-3 text-neutral-700" />
            </div>
          </div>
        </div>
        <div className="col-span-full lg:col-start-8 lg:col-end-12 mt-10 lg:mt-0">
          <h4 className="text-2xl font-medium text-neutral-900">{blok.book_demo_title}</h4>
          <RichText
            document={blok.book_demo_description}
            className="mt-3 text-base text-neutral-700"
          />
          <div
            className="calendly-inline-widget mt-4"
            data-url="https://calendly.com/d/cmfh-tj3-ty4/meeting-with-whatson?hide_gdpr_banner=1&primary_color=1a1a1a"
            style={{ minWidth: '320px', height: '700px' }}
          ></div>
          <Script
            type="text/javascript"
            src="https://assets.calendly.com/assets/external/widget.js"
            async
          />

          {blok.book_demo_action?.map((action: Action) => (
            <div className="mt-6" key={action._uid}>
              <Button
                size={action.size}
                variant={action.variant}
                RenderAs="button"
                onClick={() => {
                  setOpen(true);
                }}
              >
                {action.text}
              </Button>
              {typeof window !== 'undefined' && (
                <PopupModal
                  url={blok.demo_url}
                  onModalClose={() => {
                    setOpen(false);
                  }}
                  open={open}
                  rootElement={document.getElementById('__next') as HTMLElement}
                />
              )}
            </div>
          ))}
        </div>
        <div className="border border-t border-neutral-200 lg:col-start-2 lg:col-end-12 col-span-full mt-3 lg:mt-11"></div>
      </Container>
    </div>
  );
};

export default ContactHeader;
