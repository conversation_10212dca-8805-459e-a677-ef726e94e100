/* eslint-disable react-hooks/rules-of-hooks */
import {
  useStoryblokState,
  ISbStoryData,
  StoryblokComponent,
  useStoryblokApi,
} from '@storyblok/react';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Layout from '../core/Layout';
import resolveRelations from '../utils/resolveRelations';

type Locale = 'en' | 'nl';

type PageProps = {
  story: ISbStoryData;
  locale: Locale;
  locales: Locale[];
  defaultLocale: Locale;
};
type ParamsProps = {
  version: 'published' | 'draft';
  resolve_relations: string[];
  language: string;
};

export default function Page({ story, locale, locales, defaultLocale }: PageProps) {
  const st = useStoryblokState(story);

  return (
    <Layout locale={locale} locales={locales} defaultLocale={defaultLocale}>
      {st?.content && <StoryblokComponent blok={st.content} />}
    </Layout>
  );
}

type Context = {
  params: any; // TBD
  preview: boolean;
  previewData: any; // TBD
  locales: string[];
  locale: string;
  defaultLocale: string;
};

export async function getStaticProps(context: Context) {
  const defaultSlug = 'home';
  const { params, preview, locales, locale } = context;

  const slug = params.slug ? params.slug.join('/') : defaultSlug;

  const sbParams: ParamsProps = {
    version: 'draft', // Updated from Published
    language: locale,
    resolve_relations: resolveRelations,
  };

  if (preview) {
    sbParams.version = 'draft';
  }

  try {
    // Get page content from Storyblok
    const storyblokApi = useStoryblokApi();
    const { data } = await storyblokApi.get(`cdn/stories/${slug}`, sbParams);
    const translations = await serverSideTranslations(locale, ['common']);

    return {
      props: {
        ...translations,
        story: data ? data.story : false,
        preview: preview || false,
        locale,
        locales,
      },
      revalidate: 60,
    };
  } catch (error) {
    // Not found return 404
    return { notFound: true, revalidate: 60 };
  }
}

export async function getStaticPaths({ locales }: any) {
  const storyblokApi = useStoryblokApi();
  const { data } = await storyblokApi.get('cdn/links/');

  const paths: { params: { slug: any }; locale: string }[] = [];

  Object.keys(data.links).forEach((linkKey) => {
    if (data.links[linkKey].is_folder || data.links[linkKey].slug === 'home') {
      return;
    }

    const slug = data.links[linkKey].slug;
    const altSlugs = data.links[linkKey].alternates;
    let splittedSlug = slug.split('/');

    if (slug === 'home') {
      splittedSlug = false;
    }

    if (locales) {
      for (const locale of locales) {
        const altSlug = altSlugs?.find((item: any) => item.lang === locale);
        const altSplittedSlug = altSlug?.path.split('/');

        paths.push({
          params: { slug: altSlug ? altSplittedSlug : splittedSlug },
          locale,
        });
      }
    }
  });

  return {
    paths: paths.concat({ params: { slug: [''] }, locale: 'en' }),
    fallback: true,
  };
}
