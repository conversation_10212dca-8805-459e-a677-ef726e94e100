import { storyblokInit, apiPlugin } from '@storyblok/react';
import { appWithTranslation } from 'next-i18next';
import '../styles/globals.css';
import '@bramvanuldenn/whatson-components/build/index.css';
import PreLaunchPage from '../core/PreLaunchPage';
import Page from '../core/Page';
import HomePageHeader from '../core/HomePageHeader';
import TextWithNumbers from '../core/TextWithNumbers';
import LogoSlider from '../core/LogoSlider';
import ActionHeader from '../core/ActionHeader';
import BlogPage from '../core/BlogPage';
import FAQ from '../core/FAQ';
import TextWithAsset from '../core/TextWithAsset';
import SimpleQuote from '../core/SimpleQuote';
import ArticleImages from '../core/ArticleImages';
import FeatureHeader from '../core/FeatureHeader';
import ArticleText from '../core/ArticleText';
import ArticleList from '../core/ArticleList';
import ArticleTeaser from '../core/ArticleTeaser';
import BlogOverviewPage from '../core/BlogOverviewPage';
import Usps from '../core/Usps';
import FeatureSteps from '../core/FeatureSteps';
import CasePage from '../core/CasePage';
import ProductExplanation from '../core/ProductExplanation';
import SubpageHeader from '../core/SubpageHeader';
import CasesOverviewPage from '../core/CasesOverviewPage';
import Cta from '../core/Cta';
import QuotesSilder from '../core/QuotesSilder';
import ArticleCta from '../core/ArticleCta';
import StepImageText from '../core/StepImageText';
import Text from '../core/Text';
import TimerImageText from '../core/TimerImageText';
import PricingHeader from '../core/PricingHeader';
import HomeSteps from '../core/HomeSteps';
import NotFound from '../core/NotFound';
import MoreSolutions from '../core/MoreSolutions';
import ScrollableImageText from '../core/ScrollableImageText';
import TextSlider from '../core/TextSlider';
import JoinUs from '../core/JoinUs';
import ContactHeader from '../core/ContactHeader';
import Video from '../core/Video';
import Team from '../core/TeamMembers';
import ImageBlok from '../core/ImageBlok';
import LeadInfo from '../components/LeadInfo';
import type { AppProps } from 'next/app';

storyblokInit({
  accessToken: process.env.STORYBLOK_KEY,
  use: [apiPlugin],
  components: {
    pre_launch: PreLaunchPage,
    page: Page,
    home_header: HomePageHeader,
    text_with_numbers: TextWithNumbers,
    logo_slider: LogoSlider,
    action_header: ActionHeader,
    faq: FAQ,
    blog_page: BlogPage,
    text_with_asset: TextWithAsset,
    feature_header: FeatureHeader,
    simple_quote: SimpleQuote,
    article_images: ArticleImages,
    article_text_block: ArticleText,
    article_list: ArticleList,
    article_teaser: ArticleTeaser,
    blog_overview: BlogOverviewPage,
    usps: Usps,
    feature_steps: FeatureSteps,
    case_page: CasePage,
    product_explanation: ProductExplanation,
    subpage_header: SubpageHeader,
    cases_overview: CasesOverviewPage,
    case_header: QuotesSilder,
    quote_with_background: QuotesSilder,
    cta: Cta,
    article_cta: ArticleCta,
    step_image_text: StepImageText,
    text: Text,
    timer_with_asset: TimerImageText,
    pricing_header: PricingHeader,
    home_steps: HomeSteps,
    not_found: NotFound,
    more_solutions: MoreSolutions,
    scrollable_text_image: ScrollableImageText,
    text_slider: TextSlider,
    join_us: JoinUs,
    contact_header: ContactHeader,
    video: Video,
    team: Team,
    image: ImageBlok,
  },
});

function MyApp({ Component, pageProps }: AppProps) {
  return (
    <>
      <Component {...pageProps} />

      <LeadInfo />
    </>
  );
}

export default appWithTranslation(MyApp);
