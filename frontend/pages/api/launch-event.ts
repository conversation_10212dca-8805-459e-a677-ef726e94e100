import type { NextApiRequest, NextApiResponse } from 'next';

type Data = {
  ok: boolean;
  message: string;
};

export default async (req: NextApiRequest, res: NextApiResponse<Data>) => {
  const { body, method } = req;
  const { email, name } = body;

  if (!email || !name) {
    return res.status(422).json({
      ok: false,
      message: 'Unproccesable request, please provide the required fields',
    });
  }

  if (method === 'POST') {
    try {
      const response = await fetch('https://pixel.whatsoff.nl/event_signup', {
        body: JSON.stringify({ name, email }),
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const json = await response.json();

      if (response.status === 200) {
        return res.status(200).json({ ok: true, message: 'Thank you for your submission.' });
      }

      const message =
        json.message && typeof json.message === 'string' ? json.message : 'Something went wrong.';
      throw new Error(message);

      // Send API
    } catch (error: any) {
      // eslint-disable-next-line no-console
      const message = error && typeof error === 'string' ? error : 'Something went wrong.';

      return res.status(422).json({ ok: false, message });
    }
  }

  // Return 404 if someone pings the API with a method other than
  return res.status(404).json({ ok: false, message: 'Not found' });
};
