/* eslint-disable @typescript-eslint/no-unsafe-return */
import React, { Children, useMemo, cloneElement } from 'react';
import Link from 'next/link';
import { Props } from './type';

const NavLink = ({
  to,
  children,
  activeClassName,
  shouldWrap,
  wrapperClassName,
  linkClassName,
  target,
}: Props) => {
  const cleanPath = (path: any) => (path?.endsWith('/') ? path.substr(0, path.length - 1) : path);
  const cleanedTo = to.indexOf('http') === 0 ? to : cleanPath(to);

  const child = useMemo(() => {
    try {
      return children ? Children.only(Array.isArray(children) ? children[0] : children) : <span />;
    } catch (e) {
      return <span />;
    }
  }, [children]);

  const className = useMemo(() => {
    const childClassName = child.props.className || '';
    const isMatch = false;

    return isMatch ? `${childClassName} ${activeClassName}`.trim() : childClassName;
  }, [child, activeClassName]);

  const component = useMemo(
    () =>
      target ? (
        <a className={wrapperClassName} target="_blank">
          {cloneElement(child, { className })}
        </a>
      ) : shouldWrap ? (
        <a className={wrapperClassName}>{cloneElement(child, { className })}</a>
      ) : (
        cloneElement(child, { className })
      ),
    [shouldWrap, child, className, wrapperClassName, target]
  );

  const href = useMemo(
    () =>
      !cleanedTo || typeof cleanedTo !== 'string'
        ? '/'
        : cleanedTo.indexOf('http') === 0
        ? cleanedTo
        : cleanedTo.replace('/en/', '/').replace('/nl/', '/'),
    [cleanedTo]
  );

  return (
    <Link href={href} className={linkClassName}>
      {component}
    </Link>
  );
};

export default NavLink;
