import classNames from 'classnames';
import { useEffect, useState } from 'react';
import { Badge } from '@bramvanuldenn/whatson-components';
import { Props } from './types';

const FiltersCasesOverview = ({ options, updateFilter }: Props) => {
  const [selected, setSelected] = useState('All');

  useEffect(() => {
    updateFilter(selected);
  }, [selected]);

  return (
    <div className="col-span-full lg:col-start-2 lg:col-end-12 flex flex-col lg:flex-row gap-5 lg:gap-0 lg:justify-between lg:items-center mb-7 lg:mb-8">
      <h1 className="md:text-2xl text-3xl font-medium">See all stories</h1>
      <div className="col-span-full flex flex-wrap items-center gap-5">
        {options &&
          options.map((item) => (
            <div onClick={() => setSelected(item.name)} key={item.id} className="cursor-pointer">
              <Badge
                variant={item.name === selected ? 'pink' : 'clear'}
                size="lg"
                className={classNames({
                  'border border-neutral-200 text-neutral-500': item.name !== selected,
                })}
              >
                {item.name}
              </Badge>
            </div>
          ))}
      </div>
    </div>
  );
};

export default FiltersCasesOverview;
