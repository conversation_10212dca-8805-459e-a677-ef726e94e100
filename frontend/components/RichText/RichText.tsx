import React from 'react';
import {
  render,
  MARK_LINK,
  NODE_PARAGRAPH,
  NODE_LI,
  NODE_UL,
  MARK_BOLD,
  NODE_OL,
  NODE_HEADING,
  MARK_STYLED,
} from 'storyblok-rich-text-react-renderer';
import classNames from 'classnames';
import { Icon } from '@bramvanuldenn/whatson-components';
import { Props } from './type';

const RichText = ({ document, className, listType = 'disc' }: Props) => (
  // This functions adds the required classes for paragraphs and links.
  // Only add classes if there is more then one item.
  <div className={className}>
    {render(document, {
      markResolvers: {
        [MARK_LINK]: (children, props) => {
          const { href, target, linktype, anchor } = props;

          if (href) {
            if (linktype === 'email') {
              // Email links: add `mailto:` scheme and map to <a>
              return (
                <a className="cursor-pointer" href={`mailto:${href}`}>
                  {children}
                </a>
              );
            } else if (linktype === 'url') {
              // External links: map to <a>
              const withHttp = '';
              const to =
                href.match(/^(https?:)?\/\//) || href.match(/^(tel?:)?/)
                  ? href
                  : withHttp.concat('https://', href);

              return (
                <a className=" font-medium cursor-pointer" target={target} href={to}>
                  {children}
                </a>
              );
            }

            // Internal links: map to <NavLink>
            return (
              <a
                href={anchor ? `${href}#${anchor}` : href}
                className="cursor-pointer text-brand-pink-500"
              >
                {children}
              </a>
            );
          }

          return <a className="cursor-pointer text-brand-pink-500">{children}</a>;
        },
        [MARK_BOLD]: (children) => <strong className="font-medium">{children}</strong>,
        [MARK_STYLED]: (children, props) => <span className={props.class}>{children}</span>,
      },
      nodeResolvers: {
        [NODE_PARAGRAPH]: (children) => <p className="mb-4 last:mb-0">{children}</p>,
        [NODE_LI]: (children) => (
          <div>
            {listType === 'checkList' ? (
              <div className="flex gap-4 pb-1">
                <Icon name="HiCheck" size="lg" className="text-teal-500" />
                <span>{children}</span>
              </div>
            ) : (
              <li>{children}</li>
            )}
          </div>
        ),
        [NODE_UL]: (children) => (
          <div>
            {listType === 'checkList' ? (
              <ul
                className={classNames({
                  'inline-block list-none': true,
                })}
              >
                {children}
              </ul>
            ) : (
              <ul
                className={classNames({
                  'inline-block list-disc ml-5': true,
                })}
              >
                {children}
              </ul>
            )}
          </div>
        ),
        [NODE_OL]: (children) => (
          <div>
            <ol
              className={classNames({
                'inline-block list-decimal ml-8': true,
              })}
            >
              {children}
            </ol>
          </div>
        ),
        [NODE_HEADING]: (children, { level }) => {
          switch (level) {
            case 1:
              return <h1 className="font-medium text-3xl md:text-5xl mb-4">{children}</h1>;
            case 2:
              return <h2 className="font-medium text-2xl md:text-4xl mb-4">{children}</h2>;
            case 3:
              return <h3 className="font-medium text-xl md:text-3xl mb-4">{children}</h3>;
            case 4:
              return <h4 className="font-medium text-lg md:text-2xl mb-4">{children}</h4>;
            case 5:
              return <h5 className="font-medium text-base md:text-xl mb-4">{children}</h5>;
            case 6:
              return <h6 className="font-medium text-base md:text-lg mb-4">{children}</h6>;
            default:
              return <h2 className="font-medium text-2xl md:text-4xl mb-4">{children}</h2>;
          }
        },
      },
    })}
  </div>
);

export default RichText;
