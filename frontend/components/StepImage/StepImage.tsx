import classNames from 'classnames';
import Image from '../Image';
import { Props } from './types';

const StepImage = ({ number, image, color }: Props) => (
  <div
    className={classNames({
      'w-[105px] h-[105px] lg:w-[140px] lg:h-[140px] rounded-full p-4 relative flex items-center justify-center':
        true,
      'bg-teal-100': color === 'teal',
      'bg-brand-blue-100': color === 'blue',
      'bg-lime-100': color === 'lime',
      'bg-violet-100': color === 'violet',
    })}
  >
    <Image
      src={image?.filename}
      alt={image?.alt}
      classname="w-20 h-20 !min-h-[80px] lg:w-[106px] lg:h-[106px] lg:!min-h-[106px]"
    />
    <div className="absolute top-0 left-2">
      <div
        className={classNames({
          'w-6 h-6 lg:w-8 lg:h-8 rounded-full flex items-center justify-center text-white font-medium':
            true,
          'bg-teal-500': color === 'teal',
          'bg-brand-blue-500': color === 'blue',
          'bg-lime-500': color === 'lime',
          'bg-violet-500': color === 'violet',
        })}
      >
        {number}
      </div>
    </div>
  </div>
);

export default StepImage;
