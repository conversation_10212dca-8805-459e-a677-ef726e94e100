import { useState } from 'react';
import classNames from 'classnames';
import { Badge } from '@bramvanuldenn/whatson-components';
import { useTranslation } from 'next-i18next';
import Image from '../Image';
import { Props } from './type';
import { useScreenWidth } from '../../utils/useScreenWidth';
import { CONSTANTS } from '../../constants/constants';
import VideoPlayer from '../../components/VideoPlayer';
import NavLink from '../NavLink';

const ArticleCard = ({
  desktop_media,
  title,
  tags,
  time_to_read,
  to,
  mobile_media,
  published_date,
  inHeader,
  isCaseCard,
  logo,
  media_type,
}: Props) => {
  const [hover, setHover] = useState(false);
  const { t } = useTranslation('common');
  const screenWidth = useScreenWidth();
  const media = mobile_media?.filename
    ? screenWidth < CONSTANTS.mdWidth
      ? mobile_media
      : desktop_media
    : desktop_media;

  return (
    <div onMouseEnter={() => setHover(true)} onMouseLeave={() => setHover(false)}>
      <NavLink to={to}>
        <div className="relative">
          <div
            className={classNames({
              'w-full overflow-hidden': true,
              'h-52': !inHeader,
              'h-56 lg:h-[428px]': inHeader,
            })}
          >
            {media_type === 'video' ? (
              <div className="rounded-tl-[20px] overflow-hidden relative h-full hover:scale-105 transition-all duration-300 ease-in-out scale-100">
                <VideoPlayer videoLink={media?.filename} buttonPosition="center" />
              </div>
            ) : (
              <Image
                src={media?.filename}
                alt={media?.alt}
                imageClassName="rounded-tl-[20px]"
                hover={hover}
              />
            )}
            {tags && (
              <div className="absolute top-3 right-3 flex flex-wrap-reverse gap-2">
                {tags.map((tag) => (
                  <Badge variant="gray" size="base" key={tag._uid}>
                    {tag.text}
                  </Badge>
                ))}
              </div>
            )}
          </div>
          <div className="md:mt-6 mt-4">
            {isCaseCard && (
              <div className="mb-3">
                <Image
                  src={logo?.filename}
                  alt={logo?.alt}
                  classname="max-w-[100px] !min-h-[32px]"
                />
              </div>
            )}
            <span
              className={classNames({
                'text-lg md:text-xl font-medium hover:underline hover:underline-offset-4 hover:decoration-brand-pink-500':
                  true,
                'underline underline-offset-4 decoration-brand-pink-500': hover,
                'mt-6': !isCaseCard,
              })}
            >
              {title}
            </span>
            {!isCaseCard && (
              <div className="flex gap-2 font-medium items-center mt-3">
                <span className="text-neutral-700 font-medium text-sm">{published_date}</span>
                <span className="text-neutral-500">&bull;</span>
                <span className="text-neutral-400 font-medium text-sm">
                  {time_to_read} {t('read')}
                </span>
              </div>
            )}
          </div>
        </div>
      </NavLink>
    </div>
  );
};

export default ArticleCard;
