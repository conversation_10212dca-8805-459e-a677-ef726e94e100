import { Icon } from '@bramvanuldenn/whatson-components';
import classNames from 'classnames';
import React, { memo, useEffect, useRef, useState } from 'react';
import { Props } from './types';

// Created this component since muted attribute causes problems showing in mobile view.
// More details: https://stackoverflow.com/questions/59404642/react-html-video-tag-wont-autoplay-on-mobile-devices/59418124#59418124

const VideoPlayer = ({ videoLink, buttonPosition, autoPlay = true }: Props) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(true);

  const startVideo = () => {
    if (videoRef.current) {
      void videoRef.current.play();
      setIsPlaying(true);
    }
  };

  const pauseVideo = () => {
    if (videoRef.current) {
      videoRef.current.pause();
      setIsPlaying(false);
    }
  };

  const handleVideoPress = () => {
    if (videoRef.current && isPlaying) {
      pauseVideo();
    } else {
      startVideo();
    }
  };

  // stop the video if its outside of the viewport.
  useEffect(() => {
    const options = {
      rootMargin: '0px',
      threshold: [0.25, 0.75],
    };

    const handlePlay = (entries: any[]) => {
      entries.forEach((entry: { isIntersecting: boolean }) => {
        if (!entry.isIntersecting && !autoPlay) {
          pauseVideo();
        }
      });
    };

    const observer = new IntersectionObserver(handlePlay, options);

    if (videoRef.current) {
      observer.observe(videoRef.current);
    }
  });

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.defaultMuted = true;
    }
  }, []);

  return (
    <>
      <video
        className={classNames({
          'h-full object-cover w-full': true,
          'transform transition duration-300 ease-linear align-middle': true,
        })}
        ref={videoRef}
        playsInline
        autoPlay={autoPlay}
        loop
        muted
      >
        <source src={videoLink} type="video/mp4" />
      </video>
      {/* Play button */}
      <div
        className={classNames({
          'absolute cursor-pointer': true,
          'bottom-8 left-8': buttonPosition === 'bottom-left',
          'top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2': buttonPosition === 'center',
        })}
        onClick={handleVideoPress}
      >
        <div className="bg-black bg-opacity-40 text-white p-3 rounded-full">
          <Icon name={isPlaying ? 'HiPause' : 'HiPlay'} size="base" />
        </div>
      </div>
    </>
  );
};

export default memo(VideoPlayer);
