import classNames from 'classnames';
import { Props } from './types';

const Tag = ({ label, color, classname = '' }: Props) => (
  <div
    className={classNames({
      'w-fit rounded py-1 px-3 text-sm font-medium ': true,
      'bg-lime-100 text-lime-800': color === 'lime',
      'bg-brand-blue-100 text-brand-blue-800': color === 'blue',
      'bg-teal-100 text-teal-800': color === 'teal',
      'bg-violet-100 text-violet-800': color === 'violet',
      [classname]: true,
    })}
  >
    {label}
  </div>
);

export default Tag;
