import { But<PERSON>, Divider } from '@bramvanuldenn/whatson-components';
import classNames from 'classnames';
import { Action } from '../../types/types';
import NavLink from '../NavLink';
import RichText from '../RichText';
import { Props } from './types';

const PricingCard = ({
  isMostPopular,
  title,
  description,
  price,
  price_label,
  price_description,
  usps,
  action,
  number_of_items,
}: Props) => {
  const basis = (1 / number_of_items) * 100;

  return (
    <div
      className={classNames({
        'bg-white rounded-xl border py-8 px-6 relative basis-1/4 mb-5 lg:mb-0 last:mb-0': true,
        'border-2 border-neutral-900 lg:border-neutral-200': !isMostPopular,
        'border-4 border-teal-500': isMostPopular,
      })}
      style={{ flexBasis: `${basis}%` }}
    >
      <div className="flex flex-col h-full">
        <h4 className="text-center text-neutral-900 text-2xl lg:text-3xl font-bold grow">
          {title}
        </h4>
        <RichText
          document={description}
          className="mt-4 text-sm text-neutral-600 text-center grow"
        />
        <div className="mt-6 text-center text-neutral-900 grow">
          <span className="text-3xl font-medium">{price}</span>{' '}
          {price_label && <span className="text-xl block">/ {price_label}</span>}
          {price_description && (
            <span className="text-xs text-neutral-900 block mt-1">{price_description}</span>
          )}
        </div>
        <div className="my-6 grow">
          <Divider />
        </div>
        {usps && (
          <div className="flex-1 grow">
            {usps && (
              <RichText document={usps} className="text-neutral-900 text-sm" listType="checkList" />
            )}
          </div>
        )}
        <div className="mt-11">
          {action?.map((action: Action) => {
            const href = action.target?.cached_url;
            const page = href;
            const urlLink = action?.target?.anchor
              ? `/${page}#${action?.target?.anchor}`
              : `/${page}`;

            return (
              <div key={action._uid}>
                <NavLink to={urlLink} key={action._uid}>
                  <Button size={action.size} variant={action.variant} RenderAs="div" expand>
                    <div className="flex items-center gap-1">{action.text}</div>
                  </Button>
                </NavLink>
              </div>
            );
          })}
        </div>
        {isMostPopular && (
          <div className="absolute -top-4 left-1/2 -translate-x-1/2 transform bg-teal-500 shadow-md text-sm rounded py-1 px-3 text-white">
            <span>Most Popular</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default PricingCard;
