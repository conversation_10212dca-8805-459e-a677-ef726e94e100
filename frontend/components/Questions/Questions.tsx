import React, { Fragment } from 'react';
import classNames from 'classnames';
import { Disclosure, Transition } from '@headlessui/react';
import { Icon } from '@bramvanuldenn/whatson-components';
import RichText from '../../components/RichText';
import type { QuestionProps } from './types';

const Questions = ({ description, label }: QuestionProps) => (
  <div className="border-b border-neutral-100 py-7 overflow-hidden relative group">
    <Disclosure>
      {({ open }) => (
        <>
          <div>
            <Disclosure.Button className="flex lg:items-center w-full focus:outline-none justify-between cursor-pointer">
              <span className="text-xl font-bold text-left">{label}</span>
              <Icon
                name="HiChevronDown"
                size="xl"
                className={classNames({
                  'transform text-neutral-400 transition duration-300': true,
                  'rotate-180': open,
                  'rotate-0': !open,
                })}
              />
            </Disclosure.Button>
          </div>
          <Transition
            show={open}
            as={Fragment}
            enter="transition-all duration-500 ease-in-out"
            enterFrom="transform opacity-0 max-h-1"
            enterTo="transform opacity-100 max-h-[100rem]"
            leave="transition-all duration-150 ease-in-out"
            leaveFrom="transform opacity-100 max-h-[100rem]"
            leaveTo="transform opacity-0 max-h-1"
          >
            <Disclosure.Panel className="pt-1 -mb-4 text-neutral-400 text-lg">
              <RichText document={description} />
            </Disclosure.Panel>
          </Transition>
        </>
      )}
    </Disclosure>
  </div>
);

export default Questions;
