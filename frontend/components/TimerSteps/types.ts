import { IconName } from '@bramvanuldenn/whatson-components/build/components/Icon/types';
import { Media } from '../../types/types';

export type Step = {
  label: number;
  title: string;
  description: any;
  desktop_image: Media;
  mobile_image: Media;
  icon_color: 'teal' | 'violet' | 'blue' | 'lime' | 'orange';
  icon: IconName;
  isOpen: boolean;
  progress?: number;
  height?: number;
};

export type Props = {
  steps: Step[];
  title: string;
};
