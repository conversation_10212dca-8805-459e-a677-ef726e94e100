/* eslint-disable @typescript-eslint/no-unsafe-return */
import classNames from 'classnames';
import { Icon } from '@bramvanuldenn/whatson-components';
import React, { useEffect, useRef, useState } from 'react';
import { Transition } from '@headlessui/react';
import Image from '../Image';
import { useRatioHeight } from '../../utils/useRatioHeight';
import RichText from '../RichText';
import { Props, Step } from './types';

const stepDuration = 5000;

const TimerStep = ({ steps, title }: Props) => {
  const { ref: assetRef, height: assetHeight } = useRatioHeight('square');

  const array = steps.map((child) => ({
    label: child.label,
    desktop_image: child.desktop_image,
    mobile_image: child.mobile_image,
    icon: child.icon,
    icon_color: child.icon_color,
    isOpen: child.isOpen,
    progress: 0,
    height: 0,
  }));

  const [activeItem, setActiveItem] = useState(0);
  const [isInterval, setIsInterval] = useState(true);
  const [childArray, setChildArray] = useState(array);
  const [milliSec, setMilliSec] = useState(0);
  const increment = useRef<null | NodeJS.Timeout>(null);
  const interval = useRef<null | NodeJS.Timeout>(null);

  const updateNewList = (label: number) => {
    if (activeItem === steps.length - 1) {
      setActiveItem(0);
    } else {
      setActiveItem(activeItem + 1);
    }

    const newListsOpen = childArray.map((list) => {
      if (label === list.label) {
        return {
          ...list,
          isOpen: true,
        };
      }

      return {
        ...list,
        isOpen: false,
      };
    });

    setMilliSec(0);
    setChildArray(newListsOpen);
  };

  const openClicked = (label: number) => {
    clearInterval(increment.current as NodeJS.Timeout);
    setIsInterval(false);
    updateNewList(label);
  };

  useEffect(() => {
    interval.current = setInterval(() => {
      if (isInterval) {
        setMilliSec((milliSec) => milliSec + 100);
        clearInterval(increment.current as NodeJS.Timeout);

        const progressUpdate = childArray.map((list) => {
          if (list.label === activeItem) {
            if (list.progress >= stepDuration) {
              if (activeItem === steps.length - 1) {
                setActiveItem(0);
              } else {
                setActiveItem(activeItem + 1);
              }

              const newListsOpen = childArray.map((list) => {
                if (activeItem === list.label) {
                  return {
                    ...list,
                    isOpen: true,
                  };
                }

                return {
                  ...list,
                  isOpen: false,
                };
              });

              setMilliSec(0);
              setChildArray(newListsOpen);

              return {
                ...list,
                progress: 0,
              };
            }

            return {
              ...list,
              isOpen: true,
              progress: milliSec,
              height: 100 - (milliSec / stepDuration) * 100,
            };
          }

          if (list.progress >= stepDuration) {
            return {
              ...list,
              progress: 0,
              height: 0,
              isOpen: false,
            };
          }

          return {
            ...list,
            isOpen: false,
          };
        });

        setChildArray(progressUpdate);
      }
    }, 100);

    return () => {
      clearInterval(interval.current as NodeJS.Timeout);
    };
  }, [milliSec, isInterval, activeItem]);

  return (
    <>
      <div className="col-span-6">
        <div className="mb-10 text-3xl font-medium">{title}</div>
        {steps.map((child: Step) => {
          const state = childArray.find((item) => item.label === child.label);

          return (
            <div key={child.label}>
              <div
                className="mb-6 flex flex-col overflow-y-hidden gap-3 container "
                onClick={() => openClicked(child.label)}
              >
                <div className="flex justify-between items-start py-7">
                  <div className="flex gap-2 md:gap-4 items-start max-w-[95%]">
                    <div
                      className={classNames({
                        'p-3 w-fit rounded-tl-lg rounded-br-lg': true,
                        'bg-teal-100 text-teal-600': child.icon_color === 'teal',
                        'bg-brand-blue-100 text-brand-blue-600': child.icon_color === 'blue',
                        'bg-lime-100 text-lime-600': child.icon_color === 'lime',
                        'bg-violet-100 text-violet-600': child.icon_color === 'violet',
                        'bg-orange-100 text-orange-600': child.icon_color === 'orange',
                      })}
                    >
                      <Icon name={child.icon} />
                    </div>
                    <div className="flex-col flex gap-1">
                      <h3
                        className={classNames({
                          'text-xl font-bold': true,
                          'text-neutral-900': state?.isOpen,
                          'text-neutral-600': !state?.isOpen,
                        })}
                      >
                        {child.title}
                      </h3>
                      <Transition
                        show={state ? state.isOpen : false}
                        enter="transition-all duration-500 ease-in-out"
                        enterFrom="transform max-h-[1px]"
                        enterTo="transform max-h-[119rem]"
                        leave="transition-all duration-300 ease-in-out"
                        leaveFrom="transform max-h-[119rem]"
                        leaveTo="transform max-h-[1px]"
                      >
                        <RichText
                          document={child.description}
                          className="text-lg text-neutral-500"
                        />
                      </Transition>
                    </div>
                  </div>
                  <Icon
                    name="HiChevronDown"
                    size="xl"
                    className={classNames({
                      'transform text-neutral-400 transition duration-300': true,
                      'rotate-180': state?.isOpen,
                      'rotate-0': !state?.isOpen,
                    })}
                  />
                </div>
                <div
                  className={classNames({
                    'progress-moved h-1': state?.isOpen && isInterval,
                    'bg-neutral-200 overflow-x-hidden': true,
                    'h-px': !state?.isOpen,
                  })}
                >
                  <div
                    className={classNames({
                      'bg-neutral-200': true,
                      'w-full bg-neutral-900 h-1': state?.isOpen && !isInterval,
                      'h-px': !state?.isOpen,
                      'h-1': state?.isOpen,
                    })}
                  ></div>
                </div>
              </div>
              <Transition
                show={state ? state.isOpen : false}
                enter="transition-opacity duration-300 ease-in-out"
                enterFrom="transform opacity-0"
                enterTo="transform opacity-100"
                leave="transition-opacity duration-300 ease-in-out"
                leaveFrom="transform opacity-100"
                leaveTo="transform opacity-0"
              >
                <div className="lg:hidden mb-6 lg:mb-0">
                  <div
                    ref={assetRef}
                    className="rounded-tr-[20px] rounded-bl-[20px] rounded-br-[20px] overflow-hidden"
                    style={{ height: assetHeight }}
                  >
                    <Image
                      src={
                        child.mobile_image?.filename
                          ? child.mobile_image?.filename
                          : child.desktop_image?.filename
                      }
                      alt={
                        child.mobile_image?.alt ? child.mobile_image?.alt : child.desktop_image?.alt
                      }
                    />
                  </div>
                </div>
              </Transition>
            </div>
          );
        })}
      </div>
      <div className="col-span-6 lg:col-start-7 lg:col-end-13 hidden lg:block">
        {steps.map((child: any) => {
          const state = childArray.find((item: any) => item.label === child.label);

          return (
            <div
              key={child.label}
              className={classNames({
                relative: true,
              })}
            >
              <Transition
                show={state ? state.isOpen : false}
                enter="transition-opcaity"
                enterFrom="opacity-0 h-0"
                enterTo="opacity-100 h-full"
                leave="transition-opcaity"
                leaveFrom="opacity-100 h-full"
                leaveTo="opacity-0 h-0"
              >
                <div
                  ref={assetRef}
                  className="rounded-tr-[20px] rounded-bl-[20px] rounded-br-[20px] overflow-hidden"
                  style={{ height: assetHeight }}
                >
                  <Image src={child.desktop_image?.filename} alt={child.desktop_image?.alt} />
                </div>
              </Transition>
            </div>
          );
        })}
      </div>
    </>
  );
};

export default TimerStep;
