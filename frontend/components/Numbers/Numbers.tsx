import classNames from 'classnames';
import { Props } from './types';

const Numbers = ({ number, label, className = '', wrapperClassName = '' }: Props) => (
  <div
    className={classNames({
      'mb-10 last:mb-0 lg:mb-0': true,
      [wrapperClassName]: true,
    })}
  >
    <span
      className={classNames({
        'block text-4xl lg:text-5xl font-medium text-brand-pink-500': true,
        [className]: true,
      })}
    >
      {number}
    </span>
    <span
      className={classNames({
        'block mt-[18px] lg:mt-4 text-neutral-800 text-sm lg:text-lg font-medium': true,
        [className]: true,
      })}
    >
      {label}
    </span>
  </div>
);

export default Numbers;
