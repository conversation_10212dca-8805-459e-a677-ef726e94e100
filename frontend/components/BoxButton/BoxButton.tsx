import { Icon } from '@bramvanuldenn/whatson-components';
import classNames from 'classnames';
import NavLink from '../NavLink';
import { Props } from './types';

const BoxButton = ({ title, tag, link, color }: Props) => (
  <NavLink to={link}>
    <div className="bg-white border border-neutral-200 rounded-xl py-6 px-5 max-w-[240px] flex flex-col items-center cursor-pointer h-full">
      <span
        className={classNames({
          'block text-center text-xs font-bold': true,
          'text-brand-blue-600': color === 'blue',
          'text-teal-600': color === 'teal',
          'text-lime-600': color === 'lime',
          'text-violet-600': color === 'violet',
        })}
      >
        {tag}
      </span>
      <span className="block text-center mt-3 text-xl font-bold text-neutral-900 grow">
        {title}
      </span>
      <div className="mt-5 text-neutral-300">
        <Icon name="HiArrowDown" />
      </div>
    </div>
  </NavLink>
);

export default BoxButton;
