import React, { useMemo } from 'react';
import classNames from 'classnames';
import NextImage from 'next/image';
import { Props } from './types';

const Image = ({
  src,
  alt,
  classname,
  height,
  width,
  imageClassName,
  hover,
  isContain = false,
}: Props) => {
  const getOptimizedSrcUrl = (imageSrc: string) => {
    let srcUrl = imageSrc;
    const sizeUrlPart = `/${0}x${1200}`;

    srcUrl = imageSrc?.concat(`/m${sizeUrlPart}`);

    return srcUrl;
  };

  const optimizedSrcUrl = useMemo(() => getOptimizedSrcUrl(src), [src]);

  return optimizedSrcUrl ? (
    <div
      className={classNames({
        'relative h-full min-h-[120px]': true,
        'transform transition duration-300 ease-linear align-middle': true,
        'scale-105': hover,
        [classname]: true,
      })}
    >
      <NextImage
        fill
        style={{ objectFit: isContain ? 'contain' : 'cover' }}
        alt={alt}
        blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAACSSURBVHgBXZBZDgQhCETRVu9/Vv/cpx+TypghMQJSi4Ra67E3zvHLnuexMYbFGC2EYGst70UKjgYZ4EF9cu/POR1Fk6HWmuWcvyxvvff2O5VSnI1hsfbeHUiklH7SakhaVsghQCWSYJ5ARgGQmgMwkUAtL4RUVPvbLSGQPPNR2Uj6rTwy8O+TLUSSe0WwivH2/QGo0nXm6HoBqgAAAABJRU5ErkJggg=="
        src={optimizedSrcUrl}
        placeholder="blur"
        height={height}
        width={width}
        className={imageClassName}
      />
    </div>
  ) : null;
};

export default Image;
