import classNames from 'classnames';
import { useState } from 'react';
import { Button, Icon } from '@bramvanuldenn/whatson-components';
import { Transition } from '@headlessui/react';
import Image from '../Image';
import NavLink from '../NavLink';
import type { ProfileProps } from './types';

const ProfileCard = ({ image, name, position, bio, about_actions, card_link }: ProfileProps) => {
  const [open, setOpen] = useState(false);

  const cardHref = card_link?.cached_url;
  const cardPage = cardHref;
  const CardUrlLink = card_link?.anchor ? `/${cardPage}#${card_link?.anchor}` : `/${cardPage}`;

  return (
    <div className="group relative w-full md:w-[calc(50%_-_40px)] lg:w-[calc(33.33%_-_40px)]">
      {card_link ? (
        <NavLink to={CardUrlLink}>
          <>
            <div
              className={classNames({
                'transition-all duration-300 ease-in-out relative rounded-[20px] overflow-hidden':
                  true,
                'h-[284px]': true,
              })}
            >
              <Image src={image?.filename} alt={image?.alt} />
            </div>
            <div className="py-5">
              <div className="flex justify-between w-full items-center mb-5">
                <div className="flex flex-col gap-1">
                  <span className="text-neutral-900 font-medium text-xl">{name}</span>
                  <span className="text-neutral-600 text-lg">{position}</span>
                </div>
                {bio && (
                  <Button
                    icon={open ? 'HiMinus' : 'HiPlus'}
                    size="lg"
                    variant="transparent"
                    RenderAs="button"
                    onClick={() => {
                      setOpen(!open);
                    }}
                  />
                )}
              </div>
              {bio && (
                <Transition
                  show={open}
                  enter="transition-all duration-500 ease-in-out"
                  enterFrom="transform max-h-[1px]"
                  enterTo="transform max-h-[119rem]"
                  leave="transition-all duration-300 ease-in-out"
                  leaveFrom="transform max-h-[119rem]"
                  leaveTo="transform max-h-[1px]"
                >
                  <div className="flex flex-col gap-1">
                    <span className="text-neutral-800 font-medium">About</span>

                    <span className="text-lg text-neutral-600">{bio}</span>
                    {about_actions &&
                      about_actions.map((button) => {
                        const href = button.target?.cached_url;
                        const page = href;
                        const urlLink = button?.target?.anchor
                          ? `/${page}#${button?.target?.anchor}`
                          : `/${page}`;

                        return (
                          <NavLink to={urlLink} key={button._uid} linkClassName="mt-4">
                            <Button RenderAs="div" size={button.size} variant={button.variant}>
                              <div className="flex items-center gap-2">
                                {button.text}
                                <Icon name={button.icon} size="base" />
                              </div>
                            </Button>
                          </NavLink>
                        );
                      })}
                  </div>
                </Transition>
              )}
            </div>
          </>
        </NavLink>
      ) : (
        <>
          <div
            className={classNames({
              'transition-all duration-300 ease-in-out relative rounded-[20px] overflow-hidden':
                true,
              'h-[284px]': true,
            })}
          >
            <Image src={image?.filename} alt={image?.alt} />
          </div>
          <div className="py-5">
            <div className="flex justify-between w-full items-center mb-5">
              <div className="flex flex-col gap-1">
                <span className="text-neutral-900 font-medium text-xl">{name}</span>
                <span className="text-neutral-600 text-lg">{position}</span>
              </div>
              {bio && (
                <Button
                  icon={open ? 'HiMinus' : 'HiPlus'}
                  size="lg"
                  variant="transparent"
                  RenderAs="button"
                  onClick={() => {
                    setOpen(!open);
                  }}
                />
              )}
            </div>
            {bio && (
              <Transition
                show={open}
                enter="transition-all duration-500 ease-in-out"
                enterFrom="transform max-h-[1px]"
                enterTo="transform max-h-[119rem]"
                leave="transition-all duration-300 ease-in-out"
                leaveFrom="transform max-h-[119rem]"
                leaveTo="transform max-h-[1px]"
              >
                <div className="flex flex-col gap-1">
                  <span className="text-neutral-800 font-medium">About</span>

                  <span className="text-lg text-neutral-600">{bio}</span>
                  {about_actions &&
                    about_actions.map((button) => {
                      const href = button.target?.cached_url;
                      const page = href;
                      const urlLink = button?.target?.anchor
                        ? `/${page}#${button?.target?.anchor}`
                        : `/${page}`;

                      return (
                        <NavLink to={urlLink} key={button._uid} linkClassName="mt-4">
                          <Button RenderAs="div" size={button.size} variant={button.variant}>
                            <div className="flex items-center gap-2">
                              {button.text}
                              <Icon name={button.icon} size="base" />
                            </div>
                          </Button>
                        </NavLink>
                      );
                    })}
                </div>
              </Transition>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default ProfileCard;
