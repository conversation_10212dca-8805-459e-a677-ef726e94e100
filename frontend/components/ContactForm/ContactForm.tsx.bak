/* eslint-disable import/no-named-as-default */
import React, { useRef, useState, createRef, ChangeEvent } from 'react';
import ReCAPTCHA from 'react-google-recaptcha';
import classNames from 'classnames';
import { Icon, inputs, Divider, Button } from '@bramvanuldenn/whatson-components';
import { validateEmail } from '../../utils/validation';
import { Errors, Response } from './type';

const { Input, InputFeedback } = inputs;

const ContactForm = () => {
  const formRef = useRef<HTMLFormElement>(null);

  const recaptchaRef = createRef<ReCAPTCHA & HTMLDivElement>();
  const [email, setEmail] = useState('');
  const [fullName, setFullName] = useState('');
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');

  const [response, setResponse] = useState<Response>();
  const [success, setSuccess] = useState(false);
  const [errors, setErrors] = useState<Errors>({
    email: '',
    fullName: '',
    message: '',
  });
  const [emailValidation, setEmailValidation] = useState(true);
  const [nameValidation, setNameValidation] = useState(true);
  const [messageValidation, setMessageValidation] = useState(true);
  const recaptcha_site_key: string = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY ?? '';

  const submitMessage = (event: any) => {
    event.preventDefault();

    if (!fullName) {
      setNameValidation(false);
      setErrors({ ...errors, fullName: 'Please fill in your name' });

      return;
    }

    if (!email) {
      setEmailValidation(false);

      setErrors({ ...errors, email: 'Please fill in your email address' });

      return;
    }

    if (!validateEmail(email)) {
      setEmailValidation(false);

      setErrors({ ...errors, email: 'invalid email address' });

      return;
    }

    if (!message) {
      setMessageValidation(false);
      setErrors({ ...errors, message: 'Please fill in your message' });

      return;
    }

    if (validateEmail(email) && fullName && message) {
      setEmailValidation(true);
      setErrors({ email: '', fullName: '', message: '' });

      // if (recaptchaRef.current) {
      //   recaptchaRef.current.execute();
      // }
    }
  };

  const onReCAPTCHAChange = (token: string | null) => {
    if (!token) {
      return;
    }

    try {
      fetch('/api/contact-form', {
        body: JSON.stringify({
          email,
          fullName,
          subject,
          message,
          captcha: token,
        }),
        headers: {
          Accept: 'application/json, text/plain, */*',
          'Content-Type': 'application/json',
        },
        method: 'POST',
      })
        .then((response) => response.json())
        .then((data) => {
          const result = data;
          setResponse({ ok: result.ok, message: result.message });

          if (result.ok) {
            setEmail('');
            setFullName('');
            setSubject('');
            setMessage('');
            setSuccess(true);

            if (formRef.current) {
              formRef.current.reset();
            }
          }

          if (recaptchaRef.current) {
            recaptchaRef.current.reset();
          }

          setTimeout(() => {
            setSuccess(false);
          }, 5000);
        })
        .catch((error) => {
          // eslint-disable-next-line no-console
          console.log(error);
        });
    } catch (error) {
      return error;
    }
  };

  return (
    <>
      <Divider label="or" className="my-5" />
      <h3 className="font-medium text-2xl">Contact us</h3>
      <form ref={formRef} onSubmit={submitMessage} noValidate className="flex flex-col pt-4 gap-5">
        <div className="w-full">
          <Input
            type="text"
            label="Name"
            placeholder="your name"
            hasError={!!errors.fullName}
            value={fullName}
            onChange={(e: ChangeEvent<HTMLInputElement>) => setFullName(e.target.value)}
          />
          {!nameValidation && (
            <InputFeedback type="error" message={errors.fullName} className="text-xs mt-1" />
          )}
        </div>
        <div className="w-full">
          <Input
            type="email"
            label="Work email"
            placeholder="Your work email"
            hasError={!!errors.email}
            value={email}
            onChange={(e: ChangeEvent<HTMLInputElement>) => setEmail(e.target.value)}
          />
          {!emailValidation && (
            <InputFeedback type="error" message={errors.email} className="text-xs mt-1" />
          )}
        </div>

        <div className="w-full">
          <Input
            type="select"
            defaultSelectedOption={{ value: 1, label: 'Help', id: 'help' }}
            options={[
              { value: 1, label: 'Help', id: 'help' },
              { value: 2, label: 'Campaigns', id: 'campaigns' },
              { value: 3, label: 'Partnership', id: 'partnership' },
              { value: 4, label: 'Another topic', id: 'another-topic' },
            ]}
            caret="HiChevronDown"
            panelWidth="full"
            label="Subject"
            value={subject}
            onChange={(subject: string) => setSubject(subject)}
          />
        </div>
        <div className="w-full">
          <span className="mb-1 text-xs font-medium text-neutral-700 text-left">Message</span>
          <textarea
            rows={5}
            onChange={(e: ChangeEvent<HTMLTextAreaElement>) => setMessage(e.target.value)}
            value={message}
            className={classNames({
              'block w-full px-2 py-2.5 cursor-pointer placeholder-neutral-500 text-sm border rounded bg-white text-neutral-900 appearance-none disabled:bg-neutral-100 outline-none':
                true,
              'border-red-500': errors.message,
              ' border-neutral-300 hover:border-neutral-400 disabled:bg-neutral-100 focus:border-neutral-800 hover:focus:border-neutral-400':
                !errors.message,
            })}
          />
          {!messageValidation && (
            <InputFeedback type="error" message={errors.message} className="text-xs mt-1" />
          )}
        </div>

        <Button
          size="lg"
          variant="primary"
          RenderAs="button"
          expand
          onClick={submitMessage}
          className="mt-4"
        >
          {success ? (
            <div className="flex items-center justify-center gap-2">
              <Icon name="HiCheck" size="base" /> Successfully submitted
            </div>
          ) : (
            <div className="flex items-center justify-center gap-2">Send</div>
          )}
        </Button>
        {response && !response.ok && (
          <p className="mt-5 text-sm font-medium text-red-500">{response.message}</p>
        )}
        <div>
          <ReCAPTCHA
            ref={recaptchaRef}
            sitekey={recaptcha_site_key}
            size="invisible"
            onChange={onReCAPTCHAChange}
          />
        </div>
      </form>
    </>
  );
};

export default ContactForm;
