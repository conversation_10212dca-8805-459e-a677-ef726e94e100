import React, { useEffect } from 'react';

declare global {
  interface Window {
    hbspt: any;
    dataLayer: any;
  }
}

const ContactForm = () => {
  useEffect(() => {
    const script = document.createElement('script');
    script.src = 'https://js-eu1.hsforms.net/forms/embed/v2.js';

    document.body.appendChild(script);

    script.addEventListener('load', () => {
      if (window.hbspt) {
        window.hbspt.forms.create({
          region: 'eu1',
          portalId: '26926938',
          formId: '6ecb9727-faa6-41d4-a589-459c4d88a4bd',
          target: '#hubspotForm',
        });

        window.addEventListener('message', (event) => {
          if (event.data.type === 'hsFormCallback' && event.data.eventName === 'onFormSubmitted') {
            window.dataLayer = window.dataLayer || [];
            window.dataLayer.push({
              event: 'form_submit',
              form: {
                form_id: event.data.id,
                form_title: 'Contact form',
              },
            });
          }
        });
      }
    });
  }, []);

  return (
    <>
      <h3 className="font-medium text-2xl mt-5 md:mt-10">Contact us</h3>
      <div id="hubspotForm" className="mt-8"></div>
    </>
  );
};

export default ContactForm;
