import React from 'react';
import NextHead from 'next/head';
import { useRouter } from 'next/router';
import { Props } from './types';

const Head = ({
  title,
  description,
  og_title,
  og_description,
  og_image,
  twitter_title,
  twitter_image,
  twitter_description,
}: Props) => {
  const { locales, asPath } = useRouter();

  return (
    <NextHead>
      <meta charSet="UTF-8" />
      <title>{title || ''}</title>
      <meta name="description" content={description || ''} />
      {og_title && <meta property="og:title" content={og_title || ''} />}
      {og_description && <meta property="og:description" content={og_description || ''} />}
      {og_image && <meta property="og:image" content={og_image || ''} />}
      {twitter_title && <meta name="twitter:title" content={twitter_title || ''} />}
      {twitter_description && (
        <meta name="twitter:description" content={twitter_description || ''} />
      )}
      {twitter_image && <meta name="twitter:image" content={twitter_image || ''} />}
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <link rel="apple-touch-icon" sizes="180x180" href="/images/apple-touch-icon.png"></link>
      <link rel="icon" type="image/png" sizes="32x32" href="/images/favicon-32x32.png"></link>
      <link rel="icon" type="image/png" sizes="16x16" href="/images/favicon-16x16.png"></link>
      <link rel="manifest" href="/images/site.webmanifest"></link>
      <link rel="mask-icon" href="/images/safari-pinned-tab.svg" color="#ffffff"></link>
      <meta name="msapplication-TileColor" content="#ffffff"></meta>
      <meta name="theme-color" content="#ffffff"></meta>

      {locales?.map((locale) => (
        <link
          key={locale}
          rel="alternate"
          hrefLang={locale}
          href={`https://whatson.ai${locale === 'en' ? '/en' : ''}${asPath}`}
        />
      ))}
      <script
        type="text/javascript"
        async
        dangerouslySetInnerHTML={{
          __html: `
              (function(w, d, s, l, i) {
                    w[l] = w[l] || []
                    w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" })
                    const f = d.getElementsByTagName(s)[0],
                      j = d.createElement(s),
                      dl = l != "dataLayer" ? "&l=" + l : ""
                    j.async = true
                    j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl
                    f.parentNode.insertBefore(j, f)
                  })(window,document,'script','dataLayer',"GTM-NCZRKDQ")`,
        }}
      />
    </NextHead>
  );
};

export default Head;
