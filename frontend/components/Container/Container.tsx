import classNames from 'classnames';
import { Props } from './types';

const Container = ({ children, className = '', no_margin }: Props) => (
  <div
    className={classNames({
      'grid grid-cols-6 lg:grid-cols-12 relative gap-x-5 lg:gap-x-10 lg:px-10 lg:mx-auto max-w-screen-xl':
        true,
      'mx-5': !no_margin,
      'mx-0': no_margin,
      [className]: true,
    })}
  >
    {children}
  </div>
);

export default Container;
