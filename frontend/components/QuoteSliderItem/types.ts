import { Media, Action } from '../../types/types';
import { NumberItem } from '../../core/TextWithNumbers/types';

export type QuoteProps = {
  author: string;
  desktop_media: Media;
  mobile_media?: Media;
  media_type: 'video' | 'image';
  position: string;
  quote: any;
  numbers?: NumberItem[];
  isInHeader?: boolean;
  withBackground?: boolean;
  className?: string;
};

type ConditionalProps =
  | { isInHeader?: true; to: string; logo: Media }
  | {
      isInHeader?: false;
      to?: never;
      logo?: never;
    };
type BgProps =
  | { withBackground?: true; actions?: Action[] }
  | {
      withBackground?: false;
      actions?: never;
    };

export type Props = QuoteProps & ConditionalProps & BgProps;
