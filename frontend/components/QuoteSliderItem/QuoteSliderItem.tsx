import { Hyperlink, Icon, Button } from '@bramvanuldenn/whatson-components';
import classNames from 'classnames';
import Image from '../../components/Image';
import RichText from '../../components/RichText';
import { Props } from './types';
import { useScreenWidth } from '../../utils/useScreenWidth';
import { CONSTANTS } from '../../constants/constants';
import VideoPlayer from '../../components/VideoPlayer';
import Numbers from '../../components/Numbers';
import NavLink from '../../components/NavLink';

const QuoteSliderItem = ({
  author,
  isInHeader,
  desktop_media,
  mobile_media,
  media_type,
  position,
  quote,
  to,
  numbers,
  logo,
  actions,
  withBackground,
  className,
}: Props) => {
  const screenWidth = useScreenWidth();
  const media = mobile_media?.filename
    ? screenWidth < CONSTANTS.mdWidth
      ? mobile_media
      : desktop_media
    : desktop_media;

  return (
    <div className={`w-full h-full ${className}`}>
      {withBackground ? (
        <div
          className={classNames({
            'lg:px-10 px-5 bg-neutral-900 lg:h-full flex flex-col lg:flex-row lg:gap-9 gap-6 rounded-xl lg:py-12 py-8 items-start h-[calc(100%_-_6rem)] ':
              true,
          })}
        >
          {media.filename && (
            <>
              {media_type === 'video' ? (
                <div className="rounded-tl-[20px] rounded-bl-[20px] rounded-br-[20px] overflow-hidden relative lg:max-w-[308px] lg:min-w-[308px] w-full -mt-20 lg:h-[calc(100%_+_3rem)] h-[272px]">
                  <VideoPlayer videoLink={media?.filename} buttonPosition="center" />
                </div>
              ) : (
                <Image
                  src={media?.filename}
                  alt={media?.alt}
                  classname="lg:min-w-[308px] w-full -mt-20 lg:min-h-[calc(100%_+_3rem)] min-h-[272px]"
                  imageClassName="rounded-tr-[0px] rounded-tl-[20px] rounded-bl-[20px] rounded-br-[20px]"
                />
              )}
            </>
          )}

          <div className="flex flex-col gap-4 lg:gap-6">
            {logo?.filename && (
              <div className="max-w-[100px]">
                <Image src={logo?.filename} alt={logo?.alt} classname="!min-h-[32px]" />
              </div>
            )}
            <RichText document={quote} className="lg:text-2xl text-white" />
            <div>
              <span className="block text-neutral-300 font-medium text-xl">{author}</span>
              <span className="block text-neutral-300">{position}</span>
            </div>

            {(numbers || actions) && (
              <>
                <div className="border-t-2 border-neutral-700 w-full"></div>
                <div className="flex flex-col md:flex-row gap-8 md:gap-0 md:justify-between md:items-center">
                  {numbers && (
                    <div className="flex gap-8 items-center ">
                      {numbers?.map((item) => (
                        <Numbers
                          number={item.number}
                          label={item.label}
                          key={item._uid}
                          className="!text-white"
                          wrapperClassName="!mb-0"
                        />
                      ))}
                    </div>
                  )}
                  {actions?.map((button) => {
                    const href = button.target?.cached_url;
                    const page = href;
                    const urlLink = button?.target?.anchor
                      ? `/${page}#${button?.target?.anchor}`
                      : `/${page}`;

                    return (
                      <NavLink to={urlLink} key={button._uid}>
                        <Button RenderAs="div" size={button.size} variant={button.variant} expand>
                          <div className="flex items-center gap-2">
                            {button.text}
                            <Icon name={button.icon} size="base" />
                          </div>
                        </Button>
                      </NavLink>
                    );
                  })}
                </div>
              </>
            )}
          </div>
        </div>
      ) : (
        <div className="flex flex-col lg:flex-row lg:gap-9 lg:items-center gap-6">
          {media_type === 'video' ? (
            <div className="rounded-tl-[20px] rounded-bl-[20px] rounded-br-[20px] overflow-hidden relative h-[280px] lg:h-[456px] lg:max-w-[308px]">
              <VideoPlayer videoLink={media?.filename} buttonPosition="center" />
            </div>
          ) : (
            <Image
              src={media?.filename}
              alt={media?.alt}
              classname=" min-h-[280px] lg:min-h-[456px] min-w-[308px]"
              imageClassName="rounded-tr-[20px] rounded-bl-[20px] rounded-br-[20px]"
            />
          )}

          <div className="flex flex-col gap-4 lg:gap-7">
            {logo?.filename && isInHeader && (
              <div className="max-w-[100px]">
                <Image src={logo?.filename} alt={logo?.alt} classname="!min-h-[32px]" />
              </div>
            )}
            <RichText document={quote} className="lg:text-2xl text-neutral-900" />
            <div>
              <span className="block text-neutral-900 font-medium">{author}</span>
              <span className="block text-sm text-neutral-500">{position}</span>
            </div>
            {!isInHeader && (
              <div className="lg:flex lg:justify-between lg:items-center">
                {numbers?.map((item) => (
                  <Numbers number={item.number} label={item.label} key={item._uid} />
                ))}
              </div>
            )}

            {isInHeader && (
              <NavLink to={to}>
                <Hyperlink size="sm" RenderAs="div">
                  <div className="flex gap-2 items-center">
                    Read story
                    <Icon size="sm" name="HiArrowRight" />
                  </div>
                </Hyperlink>
              </NavLink>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default QuoteSliderItem;
