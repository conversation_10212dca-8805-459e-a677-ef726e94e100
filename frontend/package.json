{"name": "whatson.ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "proxy": "local-ssl-proxy --source 3010 --target 3000 --cert localhost.pem --key localhost-key.pem", "generate-ssl": "mkcert localhost"}, "dependencies": {"@bramvanuldenn/whatson-components": "1.0.0", "@headlessui/react": "^1.7.13", "@storyblok/react": "^2.0.13", "@svgr/webpack": "^6.5.1", "@types/body-scroll-lock": "^3.1.0", "@types/node": "18.14.4", "@types/react": "18.0.28", "@types/react-dom": "18.0.11", "body-scroll-lock": "^4.0.0-beta.0", "classnames": "^2.3.2", "eslint": "^8.35.0", "eslint-config-next": "13.2.3", "i18next": "^22.4.9", "local-ssl-proxy": "^1.3.0", "next": "13.2.3", "next-i18next": "^13.2.1", "prettier": "^2.8.4", "pretty-bytes": "^5.6.0", "react": "^18.2.0", "react-calendly": "^4.1.1", "react-code-blocks": "^0.0.9-0", "react-currency-input-field": "^3.6.10", "react-dom": "^18.2.0", "react-google-recaptcha": "^2.1.0", "react-i18next": "^12.1.4", "react-image-crop": "^8.6.5", "react-router-dom": "^5.2.0", "react-share": "^4.4.1", "react-toastify": "^9.1.1", "storyblok-rich-text-react-renderer": "^2.6.1", "swiper": "^9.1.1", "typescript": "4.9.5", "url-loader": "^4.1.1"}, "devDependencies": {"@types/react-google-recaptcha": "^2.1.5", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "autoprefixer": "^10.4.13", "eslint": "^8.23.1", "eslint-config-next": "12.3.1", "eslint-config-prettier": "^8.5.0", "eslint-config-standard-with-typescript": "^23.0.0", "eslint-import-resolver-typescript": "^3.5.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.0.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-react": "^7.31.8", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.21", "tailwindcss": "^3.2.7"}}