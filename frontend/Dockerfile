# Docker for easy local development 🔥🔥
FROM node:16.19-alpine3.17 as base
RUN apk update && apk upgrade && \
    apk add --no-cache \
    build-base bash git openssh python3 py3-pip

FROM base as build
ARG GITHUB_ACCESS_TOKEN
ENV GITHUB_ACCESS_TOKEN=${GITHUB_ACCESS_TOKEN}
WORKDIR /opt/app

# Copy package files
COPY package.json ./
COPY package-lock.json ./
COPY .npmrc ./

# Install dependencies
RUN npm install

FROM build as dev
# Copy the rest of the application
COPY . .

# Expose ports for Next.js dev server and HMR
EXPOSE 3000
EXPOSE 4040

# Start development server
CMD ["npm", "run", "dev"]
