const { colors: brandColors } = require('@bramvanuldenn/whatson-components');

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './app/**/*.{js,ts,jsx,tsx,scss}',
    './pages/**/*.{js,ts,jsx,tsx,scss}',
    './components/**/*.{js,ts,jsx,tsx,scss}',
    './core/**/*.{js,ts,jsx,tsx,scss}',

    // Or if using `src` directory:
    './src/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    fontFamily: {
      sans: [
        'F37GrotescText',
        'ui-sans-serif',
        'system-ui',
        '-apple-system',
        'BlinkMacSystemFont',
        '"Segoe UI"',
        'Roboto',
        '"Helvetica Neue"',
        'Arial',
        '"Noto Sans"',
        'sans-serif',
        '"Apple Color Emoji"',
        '"Segoe UI Emoji"',
        '"Segoe UI Symbol"',
        '"Noto Color Emoji"',
      ],
    },

    colors: ({ colors }) => ({
      ...colors,
      ...brandColors,
      'text-asset-background-mobile': '#FFF8F7',
      'gradient-black': '#262626',
    }),
    extend: {
      screens: {
        '2-md': '920px',
        '2-sm': '458px',
      },
    },
  },
};
