import type { IconName } from '@bramvanuldenn/whatson-components/build/components/Icon/types';

export type Slug = {
  lang: string;
  name: string;
  path: string;
};

export type Link = {
  alternate: any;
  content: any;
  full_slug: string;
  translated_slugs: Slug[];
  cached_url: string;
  uuid: string;
  name: string;
  _uid: string;
  anchor: string;
  published_at: string;
};

export type Media = {
  alt: string;
  copyright: string;
  fieldtype: string;
  filename: string;
  focus: string;
  id: number;
  is_external_url: boolean;
  name: string;
  title: string;
};

export type Action = {
  component: string;
  _uid: string;
  target: {
    id: string;
    url: string;
    linktype: string;
    cached_url: string;
    anchor: string;
  };
  text: string;
  variant: 'secondary' | 'primary' | 'gray' | 'transparent' | 'destructive' | 'pink' | 'dark-gray';
  size: 'xs' | 'sm' | 'base' | 'lg' | 'xl';
  icon?: string;
  isDemo?: boolean;
};

export type Badge = {
  component: string;
  _uid: string;
  text: string;
  variant:
    | 'clear'
    | 'gray'
    | 'pink'
    | 'violet'
    | 'yellow'
    | 'teal'
    | 'lime'
    | 'blue'
    | 'positive'
    | 'negative'
    | 'alert';
  size: 'base' | 'lg' | 'xl';
  icon: IconName;
};
