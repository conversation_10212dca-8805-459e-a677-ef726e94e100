/** @type {import('next').NextConfig} */
const { i18n } = require("./next-i18next.config");

const nextConfig = {
  i18n,
  reactStrictMode: true,
  swcMinify: true,
  env: {
    STORYBLOK_KEY: process.env.STORYBLOK_KEY,
  },
  images: {
    domains: ["a.storyblok.com"],
  },
  webpack(config) {
    config.resolve.modules.push(
      "node_modules/@bramvanuldenn/whatson-components/build"
    );

    config.module.rules.push({
      test: /\.svg$/,
      use: ["@svgr/webpack", "url-loader"],
    });

    return config;
  },
};

module.exports = nextConfig;
