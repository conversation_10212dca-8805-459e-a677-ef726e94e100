# Docker for easy local development 🔥🔥
FROM node:16.19-alpine3.17 as base
RUN apk update && apk upgrade && \
    apk add --no-cache \
    build-base bash git openssh python3 py3-pip
RUN yarn global add install-peerdeps

FROM base as build
ARG GITHUB_ACCESS_TOKEN
ENV GITHUB_ACCESS_TOKEN=${GITHUB_ACCESS_TOKEN}
WORKDIR /opt/app

# Copy SSH key and set up SSH
COPY id_github /root/.ssh/id_rsa
RUN chmod 600 /root/.ssh/id_rsa && \
    ssh-keyscan github.com >> /root/.ssh/known_hosts

# Clone the frontend repository using SSH
RUN <NAME_EMAIL>:bramvanuldenn/whatson-app.git /tmp/frontend && \
    cp -r /tmp/frontend/app/* /opt/app/ && \
    rm -rf /tmp/frontend

# Update package.json to use the correct component package name
# RUN sed -i 's/@akkeramsterdam\/whatson-components/@bramvanuldenn\/whatson-components/g' package.json

# Configure npm to use GitHub token for private packages
#RUN echo "//npm.pkg.github.com/:_authToken=${GITHUB_ACCESS_TOKEN}" >> .npmrc && \
#    echo "@bramvanuldenn:registry=https://npm.pkg.github.com" >> .npmrc

# Install dependencies
RUN yarn
RUN install-peerdeps -Y @bramvanuldenn/whatson-components

FROM build as dev
# Expose ports for Next.js dev server and HMR
EXPOSE 3000
EXPOSE 4040

# Start development server
ENTRYPOINT ["yarn", "start"]

FROM build as prod
RUN yarn
RUN install-peerdeps -Y @bramvanuldenn/whatson-components
RUN yarn build --noninteractive
ENTRYPOINT ["yarn", "start:prod"]
