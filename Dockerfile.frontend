# Docker for easy local development 🔥🔥
FROM node:16.19-alpine3.17 as base
RUN apk update && apk upgrade && \
    apk add --no-cache \
    build-base bash git openssh python3 py3-pip
RUN yarn global add install-peerdeps

FROM base as build
ARG GITHUB_ACCESS_TOKEN
ENV GITHUB_ACCESS_TOKEN=${GITHUB_ACCESS_TOKEN}
WORKDIR /opt/app


# Clone the frontend repository
RUN git clone https://bramvanuldenn:${GITHUB_ACCESS_TOKEN}@github.com/bramvanuldenn/whatson-app.git /tmp/frontend && \
    cp -r /tmp/frontend/app/* /opt/app/ && \
    rm -rf /tmp/frontend

# Install dependencies
RUN yarn
RUN install-peerdeps -Y @bramvanuldenn/whatson-components

FROM build as dev
# Expose ports for Next.js dev server and HMR
EXPOSE 3000
EXPOSE 4040

# Start development server
ENTRYPOINT ["yarn", "start"]

FROM build as prod
RUN yarn
RUN install-peerdeps -Y @bramvanuldenn/whatson-components
RUN yarn build --noninteractive
ENTRYPOINT ["yarn", "start:prod"]
